'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { LanguageSwitcher } from './language-switcher';
import { OnlineStatusIndicator } from './online-status-indicator';
import { useLanguage } from '../translations/language-context';
import { areDebugFeaturesEnabled } from '../utils/environment';

/**
 * Navbar component for the application
 *
 * This component provides navigation links and language switching functionality.
 * It also shows the online status of the user.
 */
export function Navbar() {
  const pathname = usePathname();
  const { t } = useLanguage();

  // Check if debug features are enabled
  const showDebugLinks = typeof window !== 'undefined' ? areDebugFeaturesEnabled() : false;

  return (
    <nav className="bg-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo and main navigation */}
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0 font-bold text-xl">
              <PERSON>
            </Link>

            <div className="hidden md:block ml-10">
              <div className="flex items-center space-x-4">
                <Link
                  href="/formulaire"
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    pathname.startsWith('/formulaire')
                      ? 'bg-blue-100 text-blue-800'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {t('common.form')}
                </Link>

                {/* API Documentation link */}
                <Link
                  href="/api-docs"
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    pathname.startsWith('/api-docs')
                      ? 'bg-blue-100 text-blue-800'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {t('common.apiDocs')}
                </Link>

                {/* Debug links - only shown in non-production environments */}
                {showDebugLinks && (
                  <Link
                    href="/test/google-drive"
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      pathname.startsWith('/test')
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    Test Tools
                  </Link>
                )}
              </div>
            </div>
          </div>

          {/* Right side items */}
          <div className="flex items-center space-x-4">
            <OnlineStatusIndicator />
            <LanguageSwitcher />
          </div>
        </div>

        {/* Mobile navigation */}
        <div className="md:hidden py-2 pb-4">
          <div className="flex flex-col space-y-2">
            <Link
              href="/formulaire"
              className={`px-3 py-2 rounded-md text-sm font-medium ${
                pathname.startsWith('/formulaire')
                  ? 'bg-blue-100 text-blue-800'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              {t('common.form')}
            </Link>

            {/* API Documentation link */}
            <Link
              href="/api-docs"
              className={`px-3 py-2 rounded-md text-sm font-medium ${
                pathname.startsWith('/api-docs')
                  ? 'bg-blue-100 text-blue-800'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              API Docs
            </Link>

            {/* Debug links - only shown in non-production environments */}
            {showDebugLinks && (
              <Link
                href="/test/google-drive"
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  pathname.startsWith('/test')
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                Test Tools
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
