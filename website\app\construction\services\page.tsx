import { Metadata } from "next";
import ConstructionServicesClient from "./construction-services-client";
import { getConstructionServicesContent } from "@/app/cms/utils/construction-services";

const constructionServicesData = getConstructionServicesContent();

export const metadata: Metadata = {
  title: constructionServicesData.metadata.title,
  description: constructionServicesData.metadata.description,
  keywords: constructionServicesData.metadata.keywords,
  openGraph: constructionServicesData.metadata.openGraph,
  alternates: {
    canonical: constructionServicesData.metadata.canonical
  }
};

export default function ConstructionServicesPage() {
  return <ConstructionServicesClient />;
}
