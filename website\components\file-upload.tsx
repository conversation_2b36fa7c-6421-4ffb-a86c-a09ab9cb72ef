"use client"

import { useCallback, useState } from "react"
import { useDropzone } from "react-dropzone"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Upload, X, File, ImageIcon, Video, FileText } from "lucide-react"
import { cn } from "@/lib/utils"

interface FileUploadProps {
  onUpload: (files: File[]) => void
  accept?: Record<string, string[]>
  maxSize?: number
  multiple?: boolean
  className?: string
}

interface UploadingFile {
  file: File
  progress: number
  id: string
}

export function FileUpload({
  onUpload,
  accept = {
    "image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp"],
    "video/*": [".mp4", ".mov", ".avi"],
    "application/pdf": [".pdf"],
  },
  maxSize = 10 * 1024 * 1024, // 10MB
  multiple = true,
  className,
}: FileUploadProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([])

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const newUploadingFiles = acceptedFiles.map((file) => ({
        file,
        progress: 0,
        id: Math.random().toString(36).substr(2, 9),
      }))

      setUploadingFiles((prev) => [...prev, ...newUploadingFiles])

      // Simulate upload progress
      newUploadingFiles.forEach((uploadingFile) => {
        const interval = setInterval(() => {
          setUploadingFiles((prev) =>
            prev.map((f) => (f.id === uploadingFile.id ? { ...f, progress: Math.min(f.progress + 10, 100) } : f)),
          )
        }, 200)

        setTimeout(() => {
          clearInterval(interval)
          setUploadingFiles((prev) => prev.filter((f) => f.id !== uploadingFile.id))
          onUpload([uploadingFile.file])
        }, 2000)
      })
    },
    [onUpload],
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
    maxSize,
    multiple,
  })

  const removeUploadingFile = (id: string) => {
    setUploadingFiles((prev) => prev.filter((f) => f.id !== id))
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith("image/")) return ImageIcon
    if (file.type.startsWith("video/")) return Video
    if (file.type === "application/pdf") return FileText
    return File
  }

  return (
    <div className={cn("space-y-4", className)}>
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
              isDragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-primary/50",
            )}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            {isDragActive ? (
              <p className="text-lg font-medium">Drop the files here...</p>
            ) : (
              <div className="space-y-2">
                <p className="text-lg font-medium">Drag & drop files here</p>
                <p className="text-sm text-muted-foreground">or click to select files</p>
                <p className="text-xs text-muted-foreground">
                  Supports images, videos, and PDFs up to {Math.round(maxSize / 1024 / 1024)}MB
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {uploadingFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Uploading Files</h4>
          {uploadingFiles.map((uploadingFile) => {
            const Icon = getFileIcon(uploadingFile.file)
            return (
              <Card key={uploadingFile.id}>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-4">
                    <Icon className="h-8 w-8 text-muted-foreground" />
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{uploadingFile.file.name}</p>
                        <Button variant="ghost" size="sm" onClick={() => removeUploadingFile(uploadingFile.id)}>
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="space-y-1">
                        <Progress value={uploadingFile.progress} className="h-2" />
                        <p className="text-xs text-muted-foreground">
                          {uploadingFile.progress}% • {Math.round(uploadingFile.file.size / 1024)} KB
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}
