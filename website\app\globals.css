@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-rethink-sans);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --background: oklch(1.00 0 0);
  --foreground: oklch(0 0 0);
  --card: oklch(0.98 0 0);
  --card-foreground: oklch(0 0 0);
  --popover: oklch(1.00 0 0);
  --popover-foreground: oklch(0 0 0);
  --primary: oklch(0.37 0.06 198.28);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.85 0 0);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.90 0 0);
  --muted-foreground: oklch(0 0 0);
  --accent: oklch(0.73 0.17 58.47);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(0.63 0.26 29.23);
  --destructive-foreground: oklch(1.00 0 0);
  --border: oklch(0.90 0 0);
  --input: oklch(0.98 0 0);
  --ring: oklch(0.37 0.06 198.28);
  --chart-1: oklch(0.37 0.06 198.28);
  --chart-2: oklch(0.73 0.17 58.47);
  --chart-3: oklch(0.85 0 0);
  --chart-4: oklch(1.00 0 0);
  --chart-5: oklch(0 0 0);
  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(0.37 0.06 198.28);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.73 0.17 58.47);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.90 0 0);
  --sidebar-ring: oklch(0.37 0.06 198.28);
  --font-sans: Helvetica;
  --font-serif: Times New Roman;
  --font-mono: Monaco;
  --radius: 4px;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0px;
}

.dark {
  --background: oklch(0.37 0.06 198.28);
  --foreground: oklch(1.00 0 0);
  --card: oklch(0.34 0.04 205.30);
  --card-foreground: oklch(1.00 0 0);
  --popover: oklch(0.37 0.06 198.28);
  --popover-foreground: oklch(1.00 0 0);
  --primary: oklch(1.00 0 0);
  --primary-foreground: oklch(0.37 0.06 198.28);
  --secondary: oklch(0.51 0 0);
  --secondary-foreground: oklch(1.00 0 0);
  --muted: oklch(0.39 0 0);
  --muted-foreground: oklch(1.00 0 0);
  --accent: oklch(0.73 0.17 58.47);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(0.63 0.26 29.23);
  --destructive-foreground: oklch(1.00 0 0);
  --border: oklch(0.39 0 0);
  --input: oklch(0.34 0.04 205.30);
  --ring: oklch(1.00 0 0);
  --chart-1: oklch(1.00 0 0);
  --chart-2: oklch(0.73 0.17 58.47);
  --chart-3: oklch(0.51 0 0);
  --chart-4: oklch(0.37 0.06 198.28);
  --chart-5: oklch(0 0 0);
  --sidebar: oklch(0.34 0.04 205.30);
  --sidebar-foreground: oklch(1.00 0 0);
  --sidebar-primary: oklch(1.00 0 0);
  --sidebar-primary-foreground: oklch(0.37 0.06 198.28);
  --sidebar-accent: oklch(0.73 0.17 58.47);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.39 0 0);
  --sidebar-ring: oklch(1.00 0 0);
  --font-sans: Helvetica;
  --font-serif: Times New Roman;
  --font-mono: Monaco;
  --radius: 4px;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    font-family: 'Rethink Sans';
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes progress {
  0% {
    width: 0%;
    left: 0;
  }
  50% {
    width: 30%;
    left: 30%;
  }
  100% {
    width: 0%;
    left: 100%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-progress {
  animation: progress 2s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}
