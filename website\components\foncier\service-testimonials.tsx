"use client";

import { useState, useEffect, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { FoncierService } from "@/lib/foncier-services-data";
import { Quote, Star, MapPin, User, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ServiceTestimonialsProps {
  service: FoncierService;
}

export function ServiceTestimonials({ service }: ServiceTestimonialsProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (service.testimonials && service.testimonials.length > 1) {
      const interval = setInterval(() => {
        setCurrentTestimonial(prev => 
          prev === service.testimonials!.length - 1 ? 0 : prev + 1
        );
      }, 6000);

      return () => clearInterval(interval);
    }
  }, [service.testimonials]);

  if (!service.testimonials || service.testimonials.length === 0) {
    return null;
  }

  const nextTestimonial = () => {
    setCurrentTestimonial(prev => 
      prev === service.testimonials!.length - 1 ? 0 : prev + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentTestimonial(prev => 
      prev === 0 ? service.testimonials!.length - 1 : prev - 1
    );
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating 
            ? 'fill-yellow-400 text-yellow-400' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
              <Quote className="w-4 h-4 mr-2" />
              Témoignages clients
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Ce que disent nos clients
            </h2>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Découvrez les retours d'expérience de clients qui ont fait appel à nos services 
              pour leur {service.title.toLowerCase()}.
            </p>
          </div>

          {/* Testimonials Carousel */}
          <div className={`relative transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="overflow-hidden rounded-2xl">
              <div 
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentTestimonial * 100}%)` }}
              >
                {service.testimonials.map((testimonial, index) => (
                  <div key={testimonial.id} className="w-full flex-shrink-0">
                    <Card className="border-0 shadow-xl bg-white">
                      <CardContent className="p-0">
                        <div className="grid lg:grid-cols-3 gap-0">
                          {/* Quote Section */}
                          <div className="lg:col-span-2 p-8 lg:p-12 space-y-6">
                            <div className="flex items-start space-x-4">
                              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                                <Quote className="w-8 h-8 text-primary" />
                              </div>
                              
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-4">
                                  {renderStars(testimonial.rating)}
                                  <span className="text-sm text-gray-600 ml-2">
                                    {testimonial.rating}/5
                                  </span>
                                </div>
                                
                                <blockquote className="text-xl lg:text-2xl text-gray-800 leading-relaxed italic">
                                  "{testimonial.content}"
                                </blockquote>
                              </div>
                            </div>

                            {/* Service Context */}
                            <div className="bg-primary/5 rounded-lg p-4 border-l-4 border-l-primary">
                              <h4 className="font-semibold text-gray-900 mb-2">
                                Service utilisé : {service.title}
                              </h4>
                              <p className="text-sm text-gray-600">
                                {service.shortDescription}
                              </p>
                            </div>
                          </div>

                          {/* Client Info Section */}
                          <div className="bg-gradient-to-br from-primary/10 to-accent/10 p-8 lg:p-12 flex flex-col justify-center">
                            <div className="text-center space-y-6">
                              {/* Avatar */}
                              <div className="w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                                <User className="w-12 h-12 text-primary" />
                              </div>

                              {/* Client Details */}
                              <div className="space-y-3">
                                <h3 className="text-xl font-bold text-gray-900">
                                  {testimonial.name}
                                </h3>
                                
                                <div className="space-y-2">
                                  <p className="text-gray-700 font-medium">
                                    {testimonial.role}
                                  </p>
                                  
                                  <div className="flex items-center justify-center space-x-2 text-gray-600">
                                    <MapPin className="w-4 h-4" />
                                    <span className="text-sm">{testimonial.location}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Rating Display */}
                              <div className="bg-white/50 rounded-lg p-4">
                                <div className="text-center">
                                  <div className="text-2xl font-bold text-primary mb-1">
                                    {testimonial.rating}.0
                                  </div>
                                  <div className="flex justify-center space-x-1 mb-2">
                                    {renderStars(testimonial.rating)}
                                  </div>
                                  <p className="text-xs text-gray-600">
                                    Satisfaction client
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Controls */}
            {service.testimonials.length > 1 && (
              <div className="flex items-center justify-between mt-8">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={prevTestimonial}
                  className="rounded-full"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>

                {/* Dots Indicator */}
                <div className="flex space-x-2">
                  {service.testimonials.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentTestimonial(index)}
                      className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                        index === currentTestimonial ? 'bg-primary' : 'bg-gray-300'
                      }`}
                    />
                  ))}
                </div>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={nextTestimonial}
                  className="rounded-full"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Trust Indicators */}
          <div className={`mt-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
              <div className="text-center space-y-6">
                <h3 className="text-2xl font-bold text-gray-900">
                  Pourquoi nos clients nous font confiance
                </h3>
                
                <div className="grid md:grid-cols-4 gap-8">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">98%</div>
                    <div className="text-sm text-gray-600">Taux de satisfaction</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">4.9/5</div>
                    <div className="text-sm text-gray-600">Note moyenne</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">300+</div>
                    <div className="text-sm text-gray-600">Clients satisfaits</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">95%</div>
                    <div className="text-sm text-gray-600">Recommandations</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className={`text-center mt-16 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-900">
                Rejoignez nos clients satisfaits
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Bénéficiez de la même expertise et du même niveau de service 
                que nos clients pour votre projet de {service.title.toLowerCase()}.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="group">
                  <a href={`/formulaire?service=${service.slug}`} className="flex items-center">
                    Commencer mon projet
                    <Quote className="ml-2 h-4 w-4 transition-transform group-hover:scale-110" />
                  </a>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <a href="/contact">
                    Parler à un expert
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
