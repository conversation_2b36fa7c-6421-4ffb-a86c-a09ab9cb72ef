import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, ChevronRight, Home, Building2 } from "lucide-react";

interface HeroSectionProps {
  heroData: {
    breadcrumb: Array<{
      text: string;
      href?: string;
      icon?: string;
      current?: boolean;
    }>;
    badge: {
      icon: string;
      text: string;
    };
    title: {
      main: string;
      highlight: string;
    };
    description: string;
    cta: {
      text: string;
      href: string;
      icon: string;
    };
  };
}

export default function HeroSection({ heroData }: HeroSectionProps) {
  return (
    <section className="bg-gradient-to-br from-orange-50 to-white py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
            {heroData.breadcrumb.map((item, index) => (
              <div key={index} className="flex items-center">
                {index > 0 && <ChevronRight className="w-4 h-4 mx-2" />}
                {item.current ? (
                  <span className="text-orange-600">{item.text}</span>
                ) : (
                  <Link href={item.href || "#"} className="hover:text-orange-600 transition-colors flex items-center">
                    {item.icon === "Home" && <Home className="w-4 h-4 mr-1" />}
                    {item.text}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          <div className="text-center space-y-6">
            <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium border border-orange-200">
              <Building2 className="w-4 h-4 mr-2" />
              {heroData.badge.text}
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
              {heroData.title.main}{" "}
              <span className="text-orange-600 relative">
                {heroData.title.highlight}
                <div className="absolute -bottom-2 left-0 w-full h-1 bg-orange-600/40 rounded-full"></div>
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              {heroData.description}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-orange-600 hover:bg-orange-700 group">
                <Link href={heroData.cta.href} className="flex items-center">
                  {heroData.cta.text}
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
