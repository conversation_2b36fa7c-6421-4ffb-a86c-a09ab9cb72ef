"use client";

import React from 'react';
import { Document, Page, Text, View, StyleSheet, Link } from '@react-pdf/renderer';
import { FormData } from './form-context';

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
  },
  section: {
    margin: 10,
    padding: 10,
    flexGrow: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 10,
    borderBottom: '1px solid #000000',
    paddingBottom: 5,
  },
  field: {
    marginBottom: 10,
  },
  label: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  value: {
    fontSize: 12,
    marginLeft: 10,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 10,
    color: '#666666',
  },
  link: {
    fontSize: 10,
    color: '#0000FF',
    textDecoration: 'underline',
  },
});

interface FormPDFProps {
  formData: FormData;
}

export const FormPDF: React.FC<FormPDFProps> = ({ formData }) => {
  // Debug: Log the form data to see what's available
  console.log('FormPDF - formData:', formData);
  console.log('FormPDF - uploadedFileUrls:', formData.uploadedFileUrls);

  // Create a deep copy of the form data to ensure we're not losing any properties
  const formDataCopy = JSON.parse(JSON.stringify(formData));

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Non spécifié';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR');
    } catch (e) {
      return dateString;
    }
  };

  // Format array for display
  const formatArray = (array?: string[]) => {
    if (!array || array.length === 0) return 'Aucun';
    return array.join(', ');
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <Text style={styles.title}>Formulaire de Demande</Text>

          {/* Personal Information */}
          <Text style={styles.subtitle}>Informations Personnelles</Text>

          <View style={styles.field}>
            <Text style={styles.label}>Nom complet:</Text>
            <Text style={styles.value}>{formData.fullName || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Genre:</Text>
            <Text style={styles.value}>{formData.gender || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Date de naissance:</Text>
            <Text style={styles.value}>{formatDate(formData.birthDate)}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Lieu de naissance:</Text>
            <Text style={styles.value}>{formData.birthPlace || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Nationalité:</Text>
            <Text style={styles.value}>{formData.nationality || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Profession:</Text>
            <Text style={styles.value}>{formData.profession || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Téléphone principal:</Text>
            <Text style={styles.value}>{formData.primaryPhone || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Téléphone secondaire:</Text>
            <Text style={styles.value}>{formData.secondaryPhone || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Email:</Text>
            <Text style={styles.value}>{formData.email || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Adresse:</Text>
            <Text style={styles.value}>{formData.address || 'Non spécifié'}</Text>
          </View>

          {/* Emergency Contacts */}
          <Text style={styles.subtitle}>Contacts d'Urgence</Text>

          <View style={styles.field}>
            <Text style={styles.label}>Contact d'urgence 1:</Text>
            <Text style={styles.value}>{formData.emergencyContact1Name || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Téléphone du contact 1:</Text>
            <Text style={styles.value}>{formData.emergencyContact1Phone || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Relation avec le contact 1:</Text>
            <Text style={styles.value}>{formData.emergencyContact1Relation || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Contact d'urgence 2:</Text>
            <Text style={styles.value}>{formData.emergencyContact2Name || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Téléphone du contact 2:</Text>
            <Text style={styles.value}>{formData.emergencyContact2Phone || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Relation avec le contact 2:</Text>
            <Text style={styles.value}>{formData.emergencyContact2Relation || 'Non spécifié'}</Text>
          </View>

          {/* Procedure Information */}
          <Text style={styles.subtitle}>Informations sur la Procédure</Text>

          <View style={styles.field}>
            <Text style={styles.label}>Statut du terrain:</Text>
            <Text style={styles.value}>{formData.landStatus || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Types de procédure:</Text>
            <Text style={styles.value}>{formatArray(formData.procedureTypes)}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Détails supplémentaires:</Text>
            <Text style={styles.value}>{formData.procedureOtherDetails || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Informations additionnelles:</Text>
            <Text style={styles.value}>{formData.additionalInfo || 'Non spécifié'}</Text>
          </View>

          {/* Documents and Location */}
          <Text style={styles.subtitle}>Documents et Localisation</Text>

          <View style={styles.field}>
            <Text style={styles.label}>Documents disponibles:</Text>
            <Text style={styles.value}>{formatArray(formData.availableDocuments)}</Text>
          </View>

          {/* Display uploaded file URLs */}
          {formDataCopy.uploadedFileUrls && Object.keys(formDataCopy.uploadedFileUrls).length > 0 && (
            <View style={styles.field}>
              <Text style={styles.label}>Fichiers téléchargés:</Text>
              {Object.entries(formDataCopy.uploadedFileUrls).map(([fileName, fileUrl], index) => {
                // Skip entries where the key contains a path separator (we only want to show the simple file names)
                if (fileName.includes('/')) {
                  return null;
                }

                console.log(`Rendering file ${index + 1} in form-pdf:`, fileName, fileUrl);
                return (
                  <View key={index} style={{ marginTop: 5 }}>
                    <Text style={styles.value}>
                      {fileName.split('/').pop() || fileName}:
                    </Text>
                    <Link src={fileUrl} style={styles.link}>
                      {fileUrl}
                    </Link>
                  </View>
                );
              }).filter(Boolean)}
            </View>
          )}

          {/* Debug information - log the form data */}
          {console.log('PDF Generator - Form Data:', JSON.stringify(formDataCopy, null, 2))}

          {/* Debug: Log if no files are found */}
          {(!formDataCopy.uploadedFileUrls || Object.keys(formDataCopy.uploadedFileUrls).length === 0) && (
            console.log('No uploaded file URLs found in form-pdf.tsx')
          )}

          <View style={styles.field}>
            <Text style={styles.label}>Détails des documents:</Text>
            <Text style={styles.value}>{formData.documentsDetails || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Type de zone:</Text>
            <Text style={styles.value}>{formData.zoneType || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Région:</Text>
            <Text style={styles.value}>{formData.region || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Département:</Text>
            <Text style={styles.value}>{formData.department || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Arrondissement:</Text>
            <Text style={styles.value}>{formData.subdivision || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Quartier:</Text>
            <Text style={styles.value}>{formData.neighborhood || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Détails de localisation:</Text>
            <Text style={styles.value}>{formData.locationDetails || 'Non spécifié'}</Text>
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Superficie:</Text>
            <Text style={styles.value}>{formData.area ? `${formData.area} m²` : 'Non spécifié'}</Text>
          </View>

          {/* Additional Comments */}
          <Text style={styles.subtitle}>Commentaires Additionnels</Text>

          <View style={styles.field}>
            <Text style={styles.value}>{formData.additionalComments || 'Aucun commentaire additionnel'}</Text>
          </View>
        </View>

        <Text style={styles.footer}>
          Formulaire généré le {new Date().toLocaleDateString('fr-FR')} - Charlie Oscar Consulting
        </Text>
      </Page>
    </Document>
  );
};
