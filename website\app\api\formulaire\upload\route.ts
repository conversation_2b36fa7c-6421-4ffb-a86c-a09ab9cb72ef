import { NextRequest, NextResponse } from 'next/server';
import { uploadToGoogleDrive, getMimeType } from '@/app/api/formulaire/utils/google-drive';
import { v4 as uuidv4 } from 'uuid';

// Global uploads folder ID from environment variables
const GLOBAL_UPLOADS_FOLDER_ID = process.env.GOOGLE_DRIVE_GLOBAL_UPLOADS_FOLDER_ID || process.env.GOOGLE_DRIVE_FOLDER_ID || '';

/**
 * API endpoint for uploading files to Google Drive
 */
export async function POST(request: NextRequest) {
  console.log('API: Received file upload request');
  try {
    // Parse the form data
    const formData = await request.formData();
    console.log('API: Form data parsed');

    // Get the file from the form data
    const file = formData.get('file') as File | null;
    if (!file) {
      console.error('API: No file provided in the request');
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }
    console.log('API: File received:', file.name, 'Size:', file.size, 'Type:', file.type);

    // Check file size (2MB limit)
    if (file.size > 2 * 1024 * 1024) { // 2MB in bytes
      console.error('API: File too large:', file.size);
      return NextResponse.json(
        { success: false, error: 'File size exceeds the 2MB limit' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv'
    ];

    // Get the MIME type
    const mimeType = file.type || getMimeType(file.name);
    console.log('API: MIME type:', mimeType);

    if (!allowedTypes.includes(mimeType)) {
      console.error('API: Invalid file type:', mimeType);
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Allowed types: PDF, images, Word, Excel, and text files.' },
        { status: 400 }
      );
    }

    // Get the file content as a buffer
    console.log('API: Converting file to buffer');
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    console.log('API: File buffer created, size:', fileBuffer.length);

    // Generate a unique filename to prevent collisions
    // Format: originalFileName_uuid
    const fileNameParts = file.name.split('.');
    const extension = fileNameParts.pop() || '';
    const baseName = fileNameParts.join('.');
    const uniqueFileName = `${baseName}_${uuidv4()}.${extension}`;
    console.log('API: Generated unique filename:', uniqueFileName);

    // Prepare upload options
    const uploadOptions = {
      folderId: GLOBAL_UPLOADS_FOLDER_ID,
      makePublic: true, // Make files publicly accessible so they can be linked in the PDF
      fields: 'id,name,webViewLink,webContentLink'
    };
    console.log('API: Upload options:', uploadOptions);

    // Upload the file to the global uploads folder in Google Drive
    console.log('API: Uploading file to global uploads folder in Google Drive');
    const result = await uploadToGoogleDrive(
      uniqueFileName,
      mimeType,
      fileBuffer,
      uploadOptions
    );

    console.log('API: Upload result:', result);

    // Return the result with the original filename for display purposes
    return NextResponse.json({
      ...result,
      originalFileName: file.name
    });
  } catch (error) {
    console.error('API: Error uploading file to Google Drive:', error);

    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('API: Error message:', errorMessage);

    return NextResponse.json(
      {
        success: false,
        error: errorMessage
      },
      { status: 500 }
    );
  }
}
