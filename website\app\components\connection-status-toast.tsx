"use client";

import React, { useEffect, useState } from "react";
import { useOnlineStatus } from "../contexts/online-status-context";
import { useLanguage } from "../translations/language-context";

export function ConnectionStatusToast() {
  const { isOnline } = useOnlineStatus();
  const { t } = useLanguage();
  const [showToast, setShowToast] = useState(false);
  const [previousOnlineStatus, setPreviousOnlineStatus] = useState(true);

  useEffect(() => {
    // Only show toast when status changes, not on initial render
    if (previousOnlineStatus !== isOnline) {
      setShowToast(true);
      const timer = setTimeout(() => {
        setShowToast(false);
      }, 5000); // Hide after 5 seconds

      return () => clearTimeout(timer);
    }
    
    setPreviousOnlineStatus(isOnline);
  }, [isOnline, previousOnlineStatus]);

  if (!showToast) return null;

  return (
    <div
      className={`fixed bottom-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm transition-all duration-300 ${
        isOnline ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
      }`}
      role="alert"
      data-testid="connection-status-toast"
    >
      <div className="flex items-center">
        <div className="flex-shrink-0">
          {isOnline ? (
            <svg
              className="h-5 w-5 text-green-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
          ) : (
            <svg
              className="h-5 w-5 text-red-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium">
            {isOnline
              ? t('common.connectionRestored')
              : t('common.connectionLost')}
          </p>
        </div>
      </div>
    </div>
  );
}
