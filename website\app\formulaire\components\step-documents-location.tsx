"use client";

import { useState, useRef } from "react";
import { useLanguage } from "@/app/translations/language-context";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useFormContext } from "./form-context";
import { useOnlineStatus } from "@/app/contexts/online-status-context";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileUpload } from "./file-upload";
import { uploadFileToGoogleDrive, hasActiveUploads } from "../utils/file-upload";
import { FormField } from "./form-field";
import { documentsLocationSchema } from "../validation/schemas";
import useFormValidation from "../validation/useFormValidation";

interface StepDocumentsLocationProps {
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

export function StepDocumentsLocation({
  onNext,
  onPrevious,
}: StepDocumentsLocationProps) {
  const { t } = useLanguage();
  const { formData, updateFormData } = useFormContext();
  const [availableDocuments, setAvailableDocuments] = useState<string[]>(
    formData.availableDocuments || []
  );
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [filePreviews, setFilePreviews] = useState<(string | null)[]>([null, null, null]);
  const [fileErrors, setFileErrors] = useState<(string | null)[]>([null, null, null]);
  const [region, setRegion] = useState<string>(formData.region || "");
  const fileInputRefs = [useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null)];
  const { isOnline } = useOnlineStatus();
  const [fileUploadIndexState,setFileUploadIndexState] = useState([true,true,true])

  const handleDocumentChange = (document: string) => {
    const updatedDocs = availableDocuments.includes(document)
      ? availableDocuments.filter((d) => d !== document)
      : [...availableDocuments, document];

    setAvailableDocuments(updatedDocs);
    updateFormData({ availableDocuments: updatedDocs });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    // setFileUploadIndexState([false,false,false])
    const tempFileUploadState = fileUploadIndexState
    tempFileUploadState[index] = true
    const file = e.target.files?.[0];

    // Reset error for this file
    const newFileErrors = [...fileErrors];
    newFileErrors[index] = null;
    setFileErrors(newFileErrors);

    if (file) {
      // Check file size (2MB limit)
      if (file.size > 2 * 1024 * 1024) {
        newFileErrors[index] = t('common.fileSize');
        setFileErrors(newFileErrors);
        if (fileInputRefs[index].current) {
          fileInputRefs[index].current!.value = "";
        }
        return;
      }

      // Update files array
      const newFiles = [...uploadedFiles];
      newFiles[index] = file;
      setUploadedFiles(newFiles);
      updateFormData({ uploadedDocuments: newFiles });

      // Create preview for image files
      const newFilePreviews = [...filePreviews];
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (event) => {
          newFilePreviews[index] = event.target?.result as string;
          setFilePreviews(newFilePreviews);
        };
        reader.readAsDataURL(file);
      } else {
        // For non-image files (like PDFs), just show the file name
        newFilePreviews[index] = null;
        setFilePreviews(newFilePreviews);
      }

      // If we have a user name, upload the file immediately
      if (formData.fullName) {
        // We don't need to await this since it's handled asynchronously
        uploadFileToGoogleDrive(file, formData, updateFormData);
      }
    } else {
      // Clear file and preview if no file selected
      const newFiles = [...uploadedFiles];
      newFiles[index] = undefined as any;
      setUploadedFiles(newFiles);

      const newFilePreviews = [...filePreviews];
      newFilePreviews[index] = null;
      setFilePreviews(newFilePreviews);

      updateFormData({
        uploadedDocuments: newFiles.filter(f => f !== undefined)
      });
    }
    setFileUploadIndexState([true,true,true])
  };

  // Initialize form validation
  const {
    validate,
    getFieldError,
    touchField,
    hasFieldError,
    validateField,
  } = useFormValidation(formData, documentsLocationSchema);

  // Handle field blur to validate individual fields
  const handleFieldBlur = (fieldName: string) => {
    touchField(fieldName);
    validateField(fieldName);
  };

  // Handle next button click
  const handleNext = () => {
    // Check if any files are currently uploading
    if (hasActiveUploads(formData)) {
      console.log('Cannot proceed while files are uploading');
      return;
    }

    // Validate the form
    const isValid = validate();

    if (isValid) {
      onNext();
    } else {
      // Focus the first field with an error
      const firstErrorField = document.querySelector('[aria-invalid="true"]') as HTMLElement;
      if (firstErrorField) {
        firstErrorField.focus();
      }
    }
  };

  // Department options based on selected region
  const getDepartments = (region: string) => {
    switch (region) {
      case "Centre":
        return ["Mfoundi", "Nyong-et-Kellé", "Nyong-et-So'o", "Lekié", "Mbam-et-Inoubou"];
      case "Littoral":
        return ["Wouri", "Sanaga-Maritime", "Nkam", "Moungo"];
      case "Sud":
        return ["Océan", "Vallée-du-Ntem", "Mvila", "Dja-et-Lobo"];
      case "Est":
        return ["Haut-Nyong", "Kadey", "Lom-et-Djérem", "Boumba-et-Ngoko"];
      case "Ouest":
        return ["Mifi", "Menoua", "Bamboutos", "Haut-Nkam", "Ndé", "Koung-Khi"];
      default:
        return [];
    }
  };

  return (
    <div className="space-y-8" data-testid="step-documents-location">
      <h2 className="text-xl font-semibold">{t('stepDocumentsLocation.title')}</h2>

      {/* Documents Section */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">{t('stepDocumentsLocation.documents.title')}</h3>
        <p className="text-sm text-gray-600" data-testid="documents-intro-text">
          {t('stepDocumentsLocation.documents.intro')}
        </p>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>{t('stepDocumentsLocation.documents.availableDocs.label')}</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2" data-testid="available-docs-checkbox-group">
              {[
                t('stepDocumentsLocation.documents.availableDocs.options.customaryProperty'),
                t('stepDocumentsLocation.documents.availableDocs.options.saleAct'),
                t('stepDocumentsLocation.documents.availableDocs.options.occupationPermit'),
                t('stepDocumentsLocation.documents.availableDocs.options.locationPlan'),
                t('stepDocumentsLocation.documents.availableDocs.options.landTitle'),
                t('stepDocumentsLocation.documents.availableDocs.options.otherDocs')
              ].map((doc) => (
                <div key={doc} className="flex items-start space-x-2">
                  <Checkbox
                    id={`doc-${doc.toLowerCase().replace(/\s+/g, '-')}`}
                    checked={availableDocuments.includes(doc)}
                    onCheckedChange={() => handleDocumentChange(doc)}
                    className="h-5 w-5 border-2 border-input data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                  />
                  <Label
                    htmlFor={`doc-${doc.toLowerCase().replace(/\s+/g, '-')}`}
                    className="text-sm"
                  >
                    {doc}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500">
              {t('stepDocumentsLocation.documents.availableDocs.help')}
            </p>
          </div>

          <div className="space-y-2">
            <Label>{t('stepDocumentsLocation.documents.upload.label')}</Label>
            <div className="space-y-6 border-2 border-dashed border-input bg-background rounded-md p-4 focus-within:border-primary" data-testid="docs-upload-area">
              {[0].map((index) => (
                <div key={index} className="space-y-2 pb-4 border-b border-gray-100 last:border-b-0 last:pb-0">
                  <p className="text-sm font-medium">{t('stepDocumentsLocation.documents.upload.documentLabel')} {index + 1}</p>
                  <input
                    type="file" 
                    disabled={!isOnline || (fileUploadIndexState[index] === false)}
                    accept=".pdf,.jpg,.png,.jpeg,.doc,.docx,.txt"
                    className="w-full"
                    onChange={(e) => handleFileUpload(e, index)}
                    data-testid={`doc-upload-${index + 1}`}
                    ref={fileInputRefs[index]}
                  />

                  {fileErrors[index] && (
                    <div className="flex items-center mt-2 text-red-500 text-sm">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      <span>{fileErrors[index]}</span>
                    </div>
                  )}
                  {
                    !isOnline && (
                      <div className="flex items-center mt-2 text-red-500 text-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"></svg>
                        <span>{t('common.fileUploadOffline')}</span>
                      </div>
                    )
                  }

                  {filePreviews[index] && (
                    <div className="mt-2">
                      <p className="text-xs font-medium mb-1">{t('common.filePreview')}</p>
                      <img
                        src={filePreviews[index] as string}
                        alt={`${t('stepDocumentsLocation.documents.upload.documentLabel')} ${index + 1}`}
                        className="max-h-32 max-w-full object-contain border rounded"
                      />
                    </div>
                  )}

                  {uploadedFiles[index] && !filePreviews[index] && (
                    <div className="mt-2 flex items-center text-sm text-green-600">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>{t('common.fileSelected')} {uploadedFiles[index].name}</span>
                    </div>
                  )}

                  {/* Upload Status */}
                  {uploadedFiles[index] && formData.uploadStatus && formData.uploadStatus[uploadedFiles[index].name] && (
                    <div className="mt-2">
                      {formData.uploadStatus[uploadedFiles[index].name] === 'uploading' && (
                        <div className="flex items-center text-blue-500 text-sm">
                          <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>{t('common.uploading')}</span>
                        </div>
                      )}

                      {formData.uploadStatus[uploadedFiles[index].name] === 'success' && (
                        <div className="flex items-center text-green-500 text-sm">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span>{t('common.uploaded')}</span>
                        </div>
                      )}

                      {formData.uploadStatus[uploadedFiles[index].name] === 'error' && (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-red-500 text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>{t('common.uploadFailed')}</span>
                          </div>

                          <button
                            type="button"
                            onClick={() => {
                              if (uploadedFiles[index]) {
                                uploadFileToGoogleDrive(uploadedFiles[index], formData, updateFormData);
                              }
                            }}
                            className="text-xs text-blue-500 hover:text-blue-700 ml-2"
                          >
                            {t('common.retry')}
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
              <p className="text-xs text-gray-500 mt-4">
                {t('stepDocumentsLocation.documents.upload.description')} ({t('common.fileFormats')})
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <FormField
              id="documentsDetails"
              label={t('stepDocumentsLocation.documents.details.label')}
              value={formData.documentsDetails || ""}
              onChange={(value) => updateFormData({ documentsDetails: value })}
              onBlur={() => handleFieldBlur('documentsDetails')}
              placeholder={t('stepDocumentsLocation.documents.details.placeholder')}
              multiline={true}
              error={getFieldError('documentsDetails')}
              data-testid="docs-details-textarea"
            />
            <p className="text-xs text-gray-500">
              {t('stepDocumentsLocation.documents.details.help')}
            </p>
          </div>
        </div>
      </div>

      {/* Location Section */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">{t('stepDocumentsLocation.location.title')}</h3>
        <p className="text-sm text-gray-600" data-testid="location-intro-text">
          {t('stepDocumentsLocation.location.intro')}
        </p>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="flex items-center">
              {t('stepDocumentsLocation.location.zoneType.label')}
              <span className="text-red-500 ml-1">{t('common.required')}</span>
            </Label>
            <RadioGroup
              value={formData.zoneType}
              onValueChange={(value) => {
                updateFormData({ zoneType: value as "Urbaine" | "Rurale" });
                touchField('zoneType');
                validateField('zoneType');
              }}
              data-testid="zone-type-radio"
              className="flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="Urbaine"
                  id="zone-urban"
                  className={`h-5 w-5 border-2 ${getFieldError('zoneType') ? "border-red-500" : "border-input"}`}
                />
                <Label htmlFor="zone-urban">{t('stepDocumentsLocation.location.zoneType.urban')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="Rurale"
                  id="zone-rural"
                  className={`h-5 w-5 border-2 ${getFieldError('zoneType') ? "border-red-500" : "border-input"}`}
                />
                <Label htmlFor="zone-rural">{t('stepDocumentsLocation.location.zoneType.rural')}</Label>
              </div>
            </RadioGroup>
            {getFieldError('zoneType') && (
              <div className="text-red-500 text-sm flex items-start mt-1" role="alert">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <span>{
                  /*@ts-ignore */
                    t(getFieldError('zoneType'))
                }</span>
              </div>
            )}
            <p className="text-xs text-gray-500">
              {t('stepDocumentsLocation.location.zoneType.help')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="region" className="flex items-center">
                {t('stepDocumentsLocation.location.region.label')}
                <span className="text-red-500 ml-1">{t('common.required')}</span>
              </Label>
              <Select
                value={region}
                onValueChange={(value) => {
                  setRegion(value);
                  updateFormData({
                    region: value,
                    department: "" // Reset department when region changes
                  });
                  touchField('region');
                  validateField('region');
                }}
              >
                <SelectTrigger
                  id="region"
                  data-testid="region-select"
                  className={`border-2 ${
                    getFieldError('region')
                      ? "border-red-500 focus:border-red-500"
                      : "border-input focus:border-primary"
                  }`}
                  aria-invalid={!!getFieldError('region')}
                >
                  <SelectValue placeholder={t('stepDocumentsLocation.location.region.placeholder')} />
                </SelectTrigger>
                <SelectContent>
                  {["Centre", "Littoral", "Sud", "Est", "Ouest", "Nord-Ouest", "Sud-Ouest", "Nord", "Extrême-Nord", "Adamaoua"].map((regionOption) => (
                    <SelectItem key={regionOption} value={regionOption}>
                      {regionOption}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {getFieldError('region') && (
                <div className="text-red-500 text-sm flex items-start mt-1" role="alert">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                  <span>{getFieldError('region')}</span>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <FormField
                id="department"
                label={t('stepDocumentsLocation.location.department.label')}
                value={formData.department || ""}
                onChange={(value) => updateFormData({ department: value })}
                onBlur={() => handleFieldBlur('department')}
                placeholder={t('stepDocumentsLocation.location.department.placeholder')}
                required={true}
                error={getFieldError('department')}
                data-testid="department-input"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <FormField
                id="subdivision"
                label={t('stepDocumentsLocation.location.subdivision.label')}
                value={formData.subdivision || ""}
                onChange={(value) => updateFormData({ subdivision: value })}
                onBlur={() => handleFieldBlur('subdivision')}
                placeholder={t('stepDocumentsLocation.location.subdivision.placeholder')}
                required={true}
                error={getFieldError('subdivision')}
                data-testid="subdivision-input"
              />
              <p className="text-xs text-gray-500">
                {t('stepDocumentsLocation.location.subdivision.help')}
              </p>
            </div>

            <div className="space-y-2">
              <FormField
                id="neighborhood"
                label={t('stepDocumentsLocation.location.neighborhood.label')}
                value={formData.neighborhood || ""}
                onChange={(value) => updateFormData({ neighborhood: value })}
                onBlur={() => handleFieldBlur('neighborhood')}
                placeholder={t('stepDocumentsLocation.location.neighborhood.placeholder')}
                required={true}
                error={getFieldError('neighborhood')}
                data-testid="neighborhood-input"
              />
            </div>
          </div>

          <div className="space-y-2">
            <FormField
              id="locationDetails"
              label={t('stepDocumentsLocation.location.locationDetails.label')}
              value={formData.locationDetails || ""}
              onChange={(value) => updateFormData({ locationDetails: value })}
              onBlur={() => handleFieldBlur('locationDetails')}
              placeholder={t('stepDocumentsLocation.location.locationDetails.placeholder')}
              error={getFieldError('locationDetails')}
              data-testid="location-details-input"
            />
            <p className="text-xs text-gray-500">
              {t('stepDocumentsLocation.location.locationDetails.help')}
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="area" className="flex items-center">
              {t('stepDocumentsLocation.location.area.label')}
              <span className="text-red-500 ml-1">{t('common.required')}</span>
            </Label>
            <div className="flex items-center">
              <Input
                id="area"
                placeholder={t('stepDocumentsLocation.location.area.placeholder')}
                value={formData.area || ""}
                onChange={(e) => updateFormData({ area: e.target.value })}
                onBlur={() => handleFieldBlur('area')}
                data-testid="area-input"
                className={`flex-1 border-2 ${
                  getFieldError('area')
                    ? "border-red-500 focus:border-red-500"
                    : "border-input focus:border-primary"
                } bg-background`}
                aria-invalid={!!getFieldError('area')}
              />
              <span className="ml-2 text-gray-600">{t('stepDocumentsLocation.location.area.unit')}</span>
            </div>
            {getFieldError('area') && (
              <div className="text-red-500 text-sm flex items-start mt-1" role="alert">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <span>{getFieldError('area')}</span>
              </div>
            )}
            <p className="text-xs text-gray-500">
              {t('stepDocumentsLocation.location.area.help')}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-4">
        <Button
          variant="outline"
          onClick={onPrevious}
          data-testid="docs-location-prev-button"
          className="border-primary text-primary hover:bg-primary/10"
        >
          {t('common.previous')}
        </Button>
        <Button
          onClick={handleNext}
          data-testid="docs-location-next-button"
          className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
          disabled={hasActiveUploads(formData)}
        >
          {hasActiveUploads(formData) ? t('common.waitForUploads') : t('common.next')}
        </Button>
      </div>
    </div>
  );
}
