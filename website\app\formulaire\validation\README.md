# Form Validation System

This directory contains the validation system for the multi-step form.

## Overview

The validation system is built using Zod, a TypeScript-first schema validation library. It provides real-time validation feedback as users type and prevents progression to the next step if there are validation errors.

## Files

- `schemas.ts`: Contains the Zod validation schemas for each step of the form.
- `useFormValidation.ts`: A custom React hook that provides validation functionality.

## How It Works

1. **Schema Definition**: Each step of the form has its own validation schema defined in `schemas.ts`.
2. **Validation Hook**: The `useFormValidation` hook provides functions for validating the form data against the schema.
3. **Real-time Validation**: As users type, the form fields are validated in real-time.
4. **Error Display**: Validation errors are displayed inline below each field.
5. **Step Progression**: Users are prevented from proceeding to the next step if there are validation errors.

## Validation Rules

- **Text Fields**: Most text fields require at least 3 characters.
- **Email**: Must be a valid email address format.
- **Phone Numbers**: Must match a specific phone number format.
- **Required Fields**: All required fields must be filled out.
- **Consent Checkboxes**: Must be checked.

## Integration with Form Components

The validation system is integrated with the form components using the `useFormValidation` hook. Each form field is wrapped in a `FormField` component that displays validation errors.

## Adding New Validation Rules

To add new validation rules:

1. Update the appropriate schema in `schemas.ts`.
2. Use the `useFormValidation` hook in your component.
3. Add validation error display to your form fields.

## Example Usage

```tsx
// Import the schema and hook
import { personalInfoSchema } from "../validation/schemas";
import useFormValidation from "../validation/useFormValidation";

// Initialize the validation hook
const {
  validate,
  getFieldError,
  touchField,
  hasFieldError,
  validateField,
} = useFormValidation(formData, personalInfoSchema);

// Validate a field on blur
const handleFieldBlur = (fieldName: string) => {
  touchField(fieldName);
  validateField(fieldName);
};

// Validate the form before proceeding
const handleNext = () => {
  const isValid = validate();
  
  if (isValid) {
    onNext();
  } else {
    // Focus the first field with an error
    const firstErrorField = document.querySelector('[aria-invalid="true"]') as HTMLElement;
    if (firstErrorField) {
      firstErrorField.focus();
    }
  }
};

// Display validation errors
<FormField
  id="fullName"
  label="Full Name"
  value={formData.fullName || ""}
  onChange={(value) => updateFormData({ fullName: value })}
  onBlur={() => handleFieldBlur('fullName')}
  placeholder="Enter your full name"
  required={true}
  error={getFieldError('fullName')}
/>
```
