"use client";

import { useState, useEffect } from "react";
import { z } from "zod";
import { FormData } from "../components/form-context";
import { ValidationErrors } from "./schemas";

/**
 * Custom hook for form validation
 *
 * @param formData The current form data
 * @param schema The Zod schema to validate against
 * @param validateOnChange Whether to validate on every change
 * @returns Validation state and functions
 */
export function useFormValidation(
  formData: FormData,
  schema: z.ZodType<any, any>,
  validateOnChange: boolean = true
) {
  // State for validation errors
  const [errors, setErrors] = useState<ValidationErrors>({});

  // State to track if validation has been triggered
  const [hasValidated, setHasValidated] = useState(false);

  // State to track which fields have been touched
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  // Validate the form data against the schema
  const validate = () => {
    try {
      // Parse the form data with the schema
      schema.parse(formData);

      // If validation passes, clear errors
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Convert Zod errors to a more usable format
        const newErrors: ValidationErrors = {};

        error.errors.forEach((err) => {
          const path = err.path.join(".");
          newErrors[path] = err.message;
        });

        setErrors(newErrors);
      }
      return false;
    } finally {
      // Mark that validation has been triggered
      setHasValidated(true);
    }
  };

  // Mark a field as touched
  const touchField = (fieldName: string) => {
    setTouchedFields((prev) => {
      const newSet = new Set(prev);
      newSet.add(fieldName);
      return newSet;
    });
  };

  // Validate a specific field
  const validateField = (fieldName: string) => {
    try {
      // Create a schema for just this field
      const fieldSchema = z.object({
        [fieldName]: (schema as any).shape[fieldName],
      });

      // Parse just this field
      fieldSchema.parse({
        [fieldName]: formData[fieldName as keyof FormData],
      });

      // If validation passes, clear error for this field
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });

      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Set error for this field
        const newErrors = { ...errors };

        error.errors.forEach((err) => {
          const path = err.path.join(".");
          newErrors[path] = err.message;
        });

        setErrors(newErrors);
      }
      return false;
    }
  };

  // Effect to validate on form data changes if validateOnChange is true
  useEffect(() => {
    if (validateOnChange && hasValidated) {
      validate();
    }
  }, [formData, validateOnChange, hasValidated]);

  // Get error for a specific field
  const getFieldError = (fieldName: string): string | undefined => {
    // Only show errors for touched fields or after validation has been triggered
    if (touchedFields.has(fieldName) || hasValidated) {
      return errors[fieldName];
    }
    return undefined;
  };

  // Check if a field has an error
  const hasFieldError = (fieldName: string): boolean => {
    return !!getFieldError(fieldName);
  };

  // Reset validation state
  const resetValidation = () => {
    setErrors({});
    setHasValidated(false);
    setTouchedFields(new Set());
  };

  return {
    errors,
    hasErrors: Object.keys(errors).length > 0,
    validate,
    validateField,
    touchField,
    getFieldError,
    hasFieldError,
    resetValidation,
    hasValidated,
  };
}

export default useFormValidation;
