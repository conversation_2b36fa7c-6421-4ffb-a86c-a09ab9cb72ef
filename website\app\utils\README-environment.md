# Environment-Based Feature Control

This module provides utilities for controlling feature availability based on the current environment.

## Overview

The environment utilities allow you to:

1. Determine the current environment (development, test, staging, production)
2. Control access to test routes based on the environment
3. Enable or disable debug features based on the environment

## Usage

### Checking the Current Environment

```typescript
import {
  getEnvironment,
  isDevelopment,
  isTest,
  isStaging,
  isProduction
} from '@/app/utils/environment';

// Get the current environment
const env = getEnvironment();
console.log(`Current environment: ${env}`);

// Check specific environments
if (isDevelopment()) {
  console.log('Running in development mode');
}

if (isProduction()) {
  console.log('Running in production mode');
}
```

### Controlling Test Routes

Test routes are automatically disabled in production environments through the middleware.

```typescript
import { areTestRoutesEnabled } from '@/app/utils/environment';

// Check if test routes are enabled
if (areTestRoutesEnabled()) {
  console.log('Test routes are enabled');
} else {
  console.log('Test routes are disabled');
}
```

### Controlling Debug Features

```typescript
import { areDebugFeaturesEnabled } from '@/app/utils/environment';

// Check if debug features are enabled
if (areDebugFeaturesEnabled()) {
  console.log('Debug features are enabled');
} else {
  console.log('Debug features are disabled');
}
```

## Implementation Details

### Environment Detection

The environment is determined by the `NODE_ENV` environment variable:

- `NODE_ENV=development` → Development environment
- `NODE_ENV=test` → Test environment
- `NODE_ENV=staging` → Staging environment
- `NODE_ENV=production` → Production environment

If `NODE_ENV` is not set, it defaults to `development`.

The implementation uses an enum to represent the possible environments:

```typescript
export enum Environment {
  Development = 'development',
  Test = 'test',
  Staging = 'staging',
  Production = 'production',
}
```

The `getEnvironment()` function reads the `NODE_ENV` variable and returns the appropriate enum value:

```typescript
export function getEnvironment(): Environment {
  const env = process.env.NODE_ENV || 'development';

  switch (env.toLowerCase()) {
    case 'production':
      return Environment.Production;
    case 'test':
      return Environment.Test;
    case 'staging':
      return Environment.Staging;
    case 'development':
    default:
      return Environment.Development;
  }
}
```

Helper functions like `isDevelopment()`, `isProduction()`, etc., make it easy to check the current environment.

### Test Routes Protection Architecture

The protection system has three layers:

1. **Server-side middleware**: Blocks HTTP requests to test routes in production
2. **API endpoint**: Provides environment information to client components
3. **Client-side guard component**: Prevents rendering test routes in production

#### 1. Server-Side Middleware

The middleware (`middleware.ts`) automatically blocks access to routes that start with `/api/test` or `/test` in production environments:

```typescript
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if this is a test route
  const isTestRoute = pathname.startsWith('/api/test') || pathname.startsWith('/test');

  // If this is a test route and test routes are disabled, return 404
  if (isTestRoute && !areTestRoutesEnabled()) {
    return new NextResponse(
      JSON.stringify({
        success: false,
        message: 'Test routes are disabled in this environment',
      }),
      {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }

  // Continue processing the request
  return NextResponse.next();
}
```

The middleware is configured to run only on test routes:

```typescript
export const config = {
  // Match all API routes and page routes that start with 'test'
  matcher: ['/api/test/:path*', '/test/:path*'],
};
```

#### 2. Environment API Endpoint

The `/api/environment` endpoint provides information about the current environment:

```typescript
export async function GET() {
  return NextResponse.json({
    environment: getEnvironment(),
    testRoutesEnabled: areTestRoutesEnabled(),
    debugFeaturesEnabled: areDebugFeaturesEnabled(),
  });
}
```

This endpoint is used by client components to check if test routes are enabled.

#### 3. Client-Side Protection

For client-side routes, the `TestRouteGuard` component provides an additional layer of protection by:

1. Checking the environment status via the `/api/environment` endpoint
2. Redirecting to the home page if test routes are disabled
3. Displaying an error message before redirecting

The component is implemented as follows:

```typescript
export default function TestRouteGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [isTestEnabled, setIsTestEnabled] = useState<boolean | null>(null);

  useEffect(() => {
    // Check if test routes are enabled
    fetch('/api/environment')
      .then(response => response.json())
      .then(data => {
        setIsTestEnabled(data.testRoutesEnabled);

        // If test routes are disabled, redirect to home page after a delay
        if (!data.testRoutesEnabled) {
          setTimeout(() => {
            router.push('/');
          }, 3000);
        }
      })
      .catch(error => {
        console.error('Error checking test routes status:', error);
        setIsTestEnabled(false);
      });
  }, [router]);

  // Show loading, error, or children based on the status
  if (isTestEnabled === null) {
    return <LoadingIndicator />;
  }

  if (!isTestEnabled) {
    return <AccessDeniedMessage />;
  }

  return <>{children}</>;
}
```

### Test Layout

All test pages are wrapped in a common layout that applies the `TestRouteGuard`:

```typescript
export default function TestLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <TestRouteGuard>
      <div className="container mx-auto py-4">
        <TestEnvironmentBanner />
        {children}
      </div>
    </TestRouteGuard>
  );
}
```

This ensures that all pages under `/test/` are automatically protected.

## Adding New Test Routes

When adding new test routes, follow these guidelines to ensure they're properly protected:

### API Test Routes

1. Place all API test routes under `/app/api/test/`:
   ```
   /app/api/test/your-feature/route.ts
   ```

2. The middleware will automatically protect these routes in production.

3. For additional protection, you can explicitly check the environment within the route handler:
   ```typescript
   import { areTestRoutesEnabled } from '@/app/utils/environment';

   export async function GET() {
     // Double-check that test routes are enabled
     if (!areTestRoutesEnabled()) {
       return new Response(
         JSON.stringify({ error: 'Test routes are disabled in this environment' }),
         { status: 404, headers: { 'Content-Type': 'application/json' } }
       );
     }

     // Your test route logic here
     return new Response(
       JSON.stringify({ message: 'Test route is working' }),
       { status: 200, headers: { 'Content-Type': 'application/json' } }
     );
   }
   ```

### Page Test Routes

1. Place all page test routes under `/app/test/`:
   ```
   /app/test/your-feature/page.tsx
   ```

2. The test layout will automatically apply the `TestRouteGuard` component.

3. If you need to create a test page outside the `/app/test/` directory, manually wrap it with the `TestRouteGuard` component:
   ```typescript
   import TestRouteGuard from "@/app/components/test-route-guard";

   export default function YourTestPage() {
     return (
       <TestRouteGuard>
         <div>Your test page content</div>
       </TestRouteGuard>
     );
   }
   ```

### Test Components and Utilities

1. For test-only components and utilities, consider placing them in dedicated directories:
   ```
   /app/components/test/
   /app/utils/test/
   ```

2. When using these components, check the environment:
   ```typescript
   import { areTestRoutesEnabled } from '@/app/utils/environment';
   import { TestComponent } from '@/app/components/test/test-component';

   export default function Page() {
     return (
       <div>
         {areTestRoutesEnabled() && <TestComponent />}
       </div>
     );
   }
   ```

### Testing the Protection

To verify that your test routes are properly protected:

1. Run your application with `NODE_ENV=production`:
   ```bash
   NODE_ENV=production npm run dev
   ```

2. Try accessing a test route (e.g., `/test/your-feature` or `/api/test/your-feature`).

3. You should receive a 404 error or be redirected to the home page.

## Environment Variables and Configuration

### Setting the Environment

To control the environment:

- **Development**: No action needed, defaults to development
  ```bash
  # This is the default
  npm run dev
  ```

- **Production**: Set `NODE_ENV=production` in your deployment environment
  ```bash
  # For testing production mode locally
  NODE_ENV=production npm run dev

  # In production deployment (e.g., Vercel, Netlify)
  # This is typically set automatically by the hosting platform
  ```

- **Staging**: Set `NODE_ENV=staging` in your deployment environment
  ```bash
  # For testing staging mode locally
  NODE_ENV=staging npm run dev

  # In staging deployment
  # Configure this in your hosting platform's environment variables
  ```

### Configuration in .env Files

You can also use `.env` files to set the environment:

- `.env.development` - Used in development mode
- `.env.production` - Used in production mode
- `.env.test` - Used in test mode
- `.env.staging` - Used in staging mode (requires custom configuration)

Example `.env.production` file:
```
NODE_ENV=production
# Other production-specific variables
```

### Deployment Considerations

When deploying your application:

1. **Vercel**: Configure environment variables in the Vercel dashboard:
   - Go to Project Settings > Environment Variables
   - Add `NODE_ENV=production` for production deployments
   - Add `NODE_ENV=staging` for preview deployments if needed

2. **Netlify**: Configure environment variables in the Netlify dashboard:
   - Go to Site Settings > Build & Deploy > Environment
   - Add `NODE_ENV=production` for production deployments

3. **Docker**: Set the environment variable in your Dockerfile or docker-compose.yml:
   ```dockerfile
   ENV NODE_ENV=production
   ```

4. **CI/CD Pipelines**: Set the environment variable in your CI/CD configuration:
   ```yaml
   # GitHub Actions example
   env:
     NODE_ENV: production
   ```

### Best Practices

1. **Never hardcode** environment checks in your application logic
2. **Always use** the environment utility functions
3. **Test your application** in all environments before deploying
4. **Document any environment-specific behavior** in your code with comments
