# Environment
# Set to 'production' in production environments
# Set to 'staging' in staging environments
# Defaults to 'development' if not set
NODE_ENV=development

# Google Drive API credentials
GOOGLE_DRIVE_TYPE="service_account"
GOOGLE_DRIVE_PROJECT_ID="your-project-id"
GOOGLE_DRIVE_PRIVATE_KEY_ID="your-private-key-id"
GOOGLE_DRIVE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
GOOGLE_DRIVE_CLIENT_EMAIL="<EMAIL>"
GOOGLE_DRIVE_CLIENT_ID="your-client-id"
GOOGLE_DRIVE_AUTH_URI="https://accounts.google.com/o/oauth2/auth"
GOOGLE_DRIVE_TOKEN_URI="https://oauth2.googleapis.com/token"
GOOG<PERSON>_DRIVE_AUTH_PROVIDER_CERT_URL="https://www.googleapis.com/oauth2/v1/certs"
GOOGLE_DRIVE_CLIENT_X509_CERT_URL="https://www.googleapis.com/robot/v1/metadata/x509/your-service-account-email%40your-project-id.iam.gserviceaccount.com"
GOOGLE_DRIVE_UNIVERSE_DOMAIN="googleapis.com"

# Google Drive folder ID where files will be uploaded by default
GOOGLE_DRIVE_FOLDER_ID="your-google-drive-folder-id"

# Google Drive API scopes
GOOGLE_DRIVE_SCOPES="https://www.googleapis.com/auth/drive.file"

# SMTP Configuration for Email Sending
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
EMAIL_FROM=<EMAIL>
