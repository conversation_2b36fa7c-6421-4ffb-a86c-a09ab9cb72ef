"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useLanguage } from "@/app/translations/language-context";

// Minimum length for text fields
const MIN_TEXT_LENGTH = 3;

interface FormFieldProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  type?: string;
  required?: boolean;
  error?: string;
  className?: string;
  multiline?: boolean;
  rows?: number;
}

/**
 * Form field component with validation
 *
 * This component renders an input field with a label and error message.
 * It supports both single-line inputs and multi-line textareas.
 */
export function FormField({
  id,
  label,
  value,
  onChange,
  onBlur,
  placeholder,
  type = "text",
  required = false,
  error,
  className = "",
  multiline = false,
  rows = 3,
}: FormFieldProps) {
  const { t } = useLanguage();

  // Handle blur events
  const handleBlur = () => {
    if (onBlur) {
      onBlur();
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={id} className="flex items-center">
        {label}
        {required && <span className="text-red-500 ml-1">{t('common.required')}</span>}
      </Label>

      {multiline ? (
        <Textarea
          id={id}
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          onBlur={handleBlur}
          placeholder={placeholder}
          rows={rows}
          className={`border-2 ${
            error
              ? "border-red-500 focus:border-red-500"
              : "border-input focus:border-primary"
          } bg-background min-h-[80px]`}
          aria-invalid={!!error}
          aria-describedby={error ? `${id}-error` : undefined}
        />
      ) : (
        <Input
          id={id}
          type={type}
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`border-2 ${
            error
              ? "border-red-500 focus:border-red-500"
              : "border-input focus:border-primary"
          } bg-background`}
          aria-invalid={!!error}
          aria-describedby={error ? `${id}-error` : undefined}
        />
      )}

      {error && (
        <div
          id={`${id}-error`}
          className="text-red-500 text-sm flex items-start mt-1"
          role="alert"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <span>
            {error.startsWith('validation.')
              ? error === 'validation.minLength'
                ? t(error).replace('{min}', MIN_TEXT_LENGTH.toString())
                : t(error)
              : error
            }
          </span>
        </div>
      )}
    </div>
  );
}

export default FormField;
