"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Shield, CheckCircle, ArrowRight } from "lucide-react";

export function FoncierHeroSection() {
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-cover bg-center"
      style={{ backgroundImage: "url('/images/company-intro-bg.jpeg')" }}
    >
      {/* Background Image Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900/80 via-gray-800/70 to-gray-900/80 z-10"></div>
      {/* Background Pattern - Aerial view simulation */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-black/90"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Cpath%20d%3D%22M36%2034v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6%2034v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6%204V0H4v4H0v2h4v4h2V6h4V4H6z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] bg-repeat"></div>
      </div>
      <div className="container mx-auto px-4 relative z-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className={`space-y-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              <div className="space-y-6">
                <div className="inline-flex items-center px-4 py-2 bg-primary/20 text-primary-foreground rounded-full text-sm font-medium backdrop-blur-sm border border-primary/30">
                  <Shield className="w-4 h-4 mr-2" />
                  Expertise Foncière Certifiée
                </div>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                  Sécurisez et valorisez vos {" "}
                  <span className="text-accent relative">
                    droits fonciers
                    <div className="absolute -bottom-2 left-0 w-full h-1 bg-accent/40 rounded-full"></div>
                  </span>
                  {" "}dès aujourd'hui
                </h1>
                <div className="space-y-4">
                  <p className="text-xl text-gray-200 leading-relaxed">
                    Bénéficiez de notre expertise professionnelle certifiée pour protéger vos biens immobiliers et optimiser vos investissements fonciers en toute sécurité.
                  </p>
                </div>
              </div>
              {/* Trust Indicators */}
              <div className="flex items-center space-x-8 pt-6 border-t border-white/20">
                <div className="text-center">
                  <div className="text-4xl font-bold text-white">20<span className="text-accent">+</span></div>
                  <div className="text-sm text-gray-300">Années d'expérience cumulées</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-white">10</div>
                  <div className="text-sm text-gray-300">Régions desservies</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-20">
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
