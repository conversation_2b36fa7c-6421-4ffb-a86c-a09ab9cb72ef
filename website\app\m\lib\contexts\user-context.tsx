"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"

export interface User {
  id?: string
  name: string
  email: string
  role: "admin" | "editor" | "publisher"
  status?: "active" | "inactive"
  createdAt?: string
  lastLogin?: string
  password?: string
}

interface UserContextType {
  users: User[]
  createUser: (userData: Omit<User, "id">) => Promise<User>
  updateUser: (id: string, userData: Partial<User>) => Promise<User>
  deleteUser: (id: string) => Promise<void>
  getUser: (id: string) => User | undefined
  toggleUserStatus: (id: string) => Promise<User>
  isLoading: boolean
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const createUser = async (userData: Omit<User, "id">): Promise<User> => {
    setIsLoading(true)

    try {
      // Create the exact JSON structure as per API documentation
      const requestBody = {
        name: userData.name,
        email: userData.email,
        role: userData.role,
        password: userData.password || "defaultPassword123",
      }

      console.log("User POST /api/users")
      console.log("Content-Type: application/json")
      console.log("Request Body:", JSON.stringify(requestBody, null, 2))

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const newUser: User = {
        ...userData,
        id: Math.random().toString(36).substr(2, 9),
        status: "active",
        createdAt: new Date().toISOString(),
      }

      setUsers((prev) => [...prev, newUser])
      return newUser
    } finally {
      setIsLoading(false)
    }
  }

  const updateUser = async (id: string, userData: Partial<User>): Promise<User> => {
    setIsLoading(true)

    try {
      const existingUser = users.find((u) => u.id === id)
      if (!existingUser) throw new Error("User not found")

      const updatedUser = {
        ...existingUser,
        ...userData,
      }

      setUsers((prev) => prev.map((u) => (u.id === id ? updatedUser : u)))
      return updatedUser
    } finally {
      setIsLoading(false)
    }
  }

  const deleteUser = async (id: string): Promise<void> => {
    setIsLoading(true)

    try {
      await new Promise((resolve) => setTimeout(resolve, 500))
      setUsers((prev) => prev.filter((u) => u.id !== id))
    } finally {
      setIsLoading(false)
    }
  }

  const toggleUserStatus = async (id: string): Promise<User> => {
    setIsLoading(true)

    try {
      const user = users.find((u) => u.id === id)
      if (!user) throw new Error("User not found")

      const updatedUser = {
        ...user,
        status: user.status === "active" ? "inactive" : ("active" as "active" | "inactive"),
      }

      setUsers((prev) => prev.map((u) => (u.id === id ? updatedUser : u)))
      return updatedUser
    } finally {
      setIsLoading(false)
    }
  }

  const getUser = (id: string): User | undefined => {
    return users.find((u) => u.id === id)
  }

  return (
    <UserContext.Provider
      value={{
        users,
        createUser,
        updateUser,
        deleteUser,
        getUser,
        toggleUserStatus,
        isLoading,
      }}
    >
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}
