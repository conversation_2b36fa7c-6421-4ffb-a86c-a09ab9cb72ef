import { Metadata } from "next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Building2, Home, Hammer, Shield, Target, Eye, Heart, Quote } from "lucide-react";
import { getAboutPageContent } from "@/app/cms/utils/about";

export const metadata: Metadata = {
  title: "À Propos - Notre Histoire et Expertise | Charlie Oscar Consulting",
  description: "Découvrez <PERSON> Consulting, votre partenaire de confiance pour vos projets fonciers, immobiliers et de construction au Cameroun. Notre mission, vision et valeurs.",
  keywords: [
    "Charlie Oscar Consulting",
    "à propos",
    "expertise foncière",
    "immobilier Cameroun",
    "construction BTP",
    "mission",
    "vision",
    "valeurs"
  ],
  openGraph: {
    title: "À Propos - Notre Histoire et Expertise | Charlie Oscar Consulting",
    description: "Découvrez <PERSON> Consulting, votre partenaire de confiance pour vos projets fonciers, immobiliers et de construction au Cameroun.",
    type: "website",
    locale: "fr_FR",
    siteName: "Charlie Oscar Consulting"
  },
  alternates: {
    canonical: "/a-propos"
  }
};

export default function AProposPage() {
  const about = getAboutPageContent();
  const hero = about.heroSection || {};
  const company = about.companyDescription || {};
  const mission = about.mission || {};
  const vision = about.vision || {};
  const values = about.values || [];
  const slogan = about.sloganSection || {};

  // Icon map for dynamic rendering
  const iconMap: Record<string, any> = {
    Building2,
    Home,
    Hammer,
    Heart,
    Award: Shield, // fallback for Award
    Lightbulb: Target, // fallback for Lightbulb
    Handshake: Quote // fallback for Handshake
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-20 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Cpath%20d%3D%22M36%2034v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6%2034v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6%204V0H4v4H0v2h4v4h2V6h4V4H6z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] bg-repeat"></div>
        </div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-4xl mx-auto space-y-8">
            <div className="inline-flex items-center px-4 py-2 bg-primary/20 text-primary-foreground rounded-full text-sm font-medium backdrop-blur-sm border border-primary/30">
              {hero.badge || "Qui sommes-nous ?"}
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
              
                <>À Propos de <span className="text-primary relative">Charlie Oscar<div className="absolute -bottom-2 left-0 w-full h-1 bg-primary/40 rounded-full"></div></span></>
              
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
              {hero.subtitle}
            </p>
            {/* Domain Icons */}
            <div className="flex justify-center space-x-8 pt-8">
              {Array.isArray(hero.domainIcons) && hero.domainIcons.length > 0 ? hero.domainIcons.map((domain: any, i: number) => {
                const Icon = iconMap[domain.icon] || Building2;
                return (
                  <div className="text-center" key={i}>
                    <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Icon className="w-8 h-8 text-primary" />
                    </div>
                    <span className="text-sm text-gray-300">{domain.label}</span>
                  </div>
                );
              }) : null}
            </div>
          </div>
        </div>
      </section>

      {/* Company Description */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center space-y-6 mb-16">
              <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
                {company.badge || "Notre entreprise"}
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                {company.title}
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                {company.content}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Mission, Vision, Values */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto space-y-16">
            {/* Mission */}
            <div className="text-center space-y-8">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
                  <Target className="w-4 h-4 mr-2" />
                  {mission.title || "Notre mission"}
                </div>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                {mission.subtitle || "Notre Mission et Objectifs"}
              </h2>
              <Card className="max-w-4xl mx-auto border-l-4 border-l-primary bg-white shadow-lg">
                <CardContent className="p-8">
                  <p className="text-xl text-gray-700 leading-relaxed text-center">
                    {mission.content}
                  </p>
                </CardContent>
              </Card>
            </div>
            {/* Vision */}
            <div className="text-center space-y-8">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
                  <Eye className="w-4 h-4 mr-2" />
                  {vision.title || "Notre vision"}
                </div>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                {vision.subtitle || "Vision et Engagement"}
              </h2>
              <Card className="max-w-4xl mx-auto border-l-4 border-l-accent bg-white shadow-lg">
                <CardContent className="p-8">
                  <p className="text-xl text-gray-700 leading-relaxed text-center">
                    {vision.content}
                  </p>
                </CardContent>
              </Card>
            </div>
            {/* Values */}
            <div className="space-y-8">
              <div className="text-center space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
                  <Heart className="w-4 h-4 mr-2" />
                  Nos valeurs
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  Nos Valeurs Fondamentales
                </h2>
              </div>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {values.map((value: any, i: number) => {
                  const Icon = iconMap[value.icon] || Shield;
                  const colorClass = value.color === 'primary' ? 'text-primary' : value.color === 'accent' ? 'text-accent' : value.color === 'secondary' ? 'text-secondary' : value.color === 'orange-500' ? 'text-orange-500' : 'text-primary';
                  return (
                    <Card className={`text-center hover:shadow-lg transition-shadow duration-300 border-t-4 border-t-${value.color || 'primary'}`} key={i}>
                      <CardHeader>
                        <div className={`w-16 h-16 bg-${value.color || 'primary'}/10 rounded-full flex items-center justify-center mx-auto mb-4`}>
                          <Icon className={`w-8 h-8 ${colorClass}`} />
                        </div>
                        <CardTitle className="text-xl">{value.title}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          {value.description}
                        </p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Slogan Section with Background */}
      <section className="py-20 relative overflow-hidden">
        {/* Background with domain illustrations */}
        <div className="absolute inset-0">
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/90 via-primary/80 to-accent/90 z-10"></div>
        </div>
        <div className="container mx-auto px-4 relative z-20">
          <div className="max-w-6xl mx-auto text-center space-y-12">
            {/* Slogan Header */}
            <div className="space-y-6">
              <div className="inline-flex items-center px-4 py-2 bg-white/20 text-white rounded-full text-sm font-medium backdrop-blur-sm border border-white/30">
                <Quote className="w-4 h-4 mr-2" />
                Notre slogan
              </div>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                {slogan.slogan}
              </h2>
              <p className="text-xl md:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed">
                {slogan.description}
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}