"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronDown,ArrowRight } from "lucide-react";
import Image from "next/image";
export function HeroSection({ content }: { content: any }) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23000000%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%227%22%20cy%3D%227%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] bg-repeat"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className={`space-y-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              <div className="space-y-6">
                <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
                  <span className="w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
                  {content?.badge}
                </div>
                
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary leading-tight">
                    EXPERTISE {" "}
                     <span className="text-accent relative">
                      FONCIÈRE,
                        <div className="absolute  left-0 w-full h-1 bg-accent/40 rounded-full"></div>
                     </span>
                      <span className="text-accent relative">
                        CONSTRUCTION
                          <div className="absolute  left-0 w-full h-1 bg-accent/40 rounded-full"></div>
                      </span>
                    &
                    <span className="text-accent relative">
                        IMMOBILIER
                        <div className="absolute  left-0 w-full h-1 bg-accent/40 rounded-full"></div>
                    </span>
                    
                    
                  </h1>
                
                <p className="text-lg text-gray-700">
                  <span className="font-bold text-md">Charlie Oscar Consulting : </span>
                      votre partenaire unique pour sécuriser, valoriser
                      et réaliser vos projets fonciers, immobiliers et
                      BTP au Cameroun.
                </p>
                
                
              </div>

              <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                {content?.cta && (
                  <Link href={content.cta.url}>
                    <Button size="lg" className="mt-4">
                      {content.cta.label}
                    </Button>
                  </Link>
                )}
                {content?.primaryAction && (
                  <Button asChild size="lg" className="group mt-4 " variant="outline">
                    <Link href={content.primaryAction.url} className="flex items-center">
                      {content.primaryAction.label}
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>
                )}
                
              </div>

              {/* {content?.trustIndicators && (
                <div className="flex gap-8 pt-8">
                  {content.trustIndicators.map((indicator: any, idx: number) => (
                    <div key={idx} className="flex flex-col items-center">
                      <span className="text-2xl font-bold text-primary">{indicator.value}</span>
                      <span className="text-xs text-gray-500">{indicator.label}</span>
                    </div>
                  ))}
                </div>
              )} */}
            </div>

            {/* Visual Element */}
            <div className={`relative transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              <div className="relative">
                {/* Main Image Placeholder */}
                        <img
                        src="/images/home-image.jpeg"
                        alt="Image d'une terrain en lotissement"
                        className="w-full h-full object-cover rounded-lg" 
                      />
               

                {/* Floating Cards */}
                <div className="absolute -top-4 -right-4 bg-white rounded-xl shadow-lg p-4 border border-gray-100">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-gray-700">EXPERTISE LOCALE</span>
                  </div>
                </div>

                <div className="absolute -bottom-4 -left-4 bg-white rounded-xl shadow-lg p-4 border border-gray-100">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <span className="text-sm font-medium text-gray-700">ACCOMPAGNEMENT PERSONNALISÉ</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <ChevronDown className="w-6 h-6 text-gray-400" />
      </div>
    </section>
  );
}
