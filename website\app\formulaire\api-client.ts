"use client";

import { FormData as FormDataType } from './components/form-context';

/**
 * Response type for form submission
 */
export interface SubmissionResponse {
  success: boolean;
  referenceNumber?: string;
  message?: string;
  error?: string;
}

/**
 * Submit form data to the API
 * @param formData The form data to submit
 * @param pdfBlob The generated PDF file as a Blob
 * @param uploadedFiles Any additional files uploaded by the user
 * @returns A promise that resolves to the submission response
 */
export async function submitFormToApi(
  formData: FormDataType,
  pdfBlob?: Blob,
  uploadedFiles?: File[]
): Promise<SubmissionResponse> {
  try {
    // Create a FormData object to send to the API
    const apiFormData = new FormData();

    // Add the form data as JSON
    apiFormData.append('formData', JSON.stringify(formData));

    // Add the PDF file if available
    if (pdfBlob) {
      const pdfFile = new File([pdfBlob], 'formulaire-charlie-oscar.pdf', {
        type: 'application/pdf'
      });
      apiFormData.append('pdfFile', pdfFile);
    }

    // Add any uploaded files
    if (uploadedFiles && uploadedFiles.length > 0) {
      uploadedFiles.forEach((file, index) => {
        apiFormData.append(`file_${index}`, file);
      });
    }

    // Add ID document if available
    if (formData.idDocument) {
      apiFormData.append('idDocument', formData.idDocument);
    }

    // Send the request to the API
    const response = await fetch('/api/formulaire/submit', {
      method: 'POST',
      body: apiFormData,
    });

    // Parse the response
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to submit form');
    }

    return data;
  } catch (error) {
    console.error('Error submitting form to API:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}


