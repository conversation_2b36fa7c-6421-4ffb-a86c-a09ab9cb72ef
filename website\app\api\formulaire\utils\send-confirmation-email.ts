import { sendEmail } from '@/app/api/send-mail/utils/mail-sender';
import { createConfirmationEmailTemplate, getConfirmationEmailSubject } from './email-templates';

/**
 * Interface for confirmation email options
 */
interface SendConfirmationEmailOptions {
  email: string;
  referenceNumber: string;
  language?: 'fr' | 'en';
}

/**
 * Sends a confirmation email to the user after form submission
 * 
 * @param options - The options for sending the confirmation email
 * @returns Promise resolving to the result of the email sending operation
 */
export async function sendConfirmationEmail({
  email,
  referenceNumber,
  language = 'fr'
}: SendConfirmationEmailOptions) {
  try {
    // Validate email with a more comprehensive regex
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!email || !emailRegex.test(email)) {
      console.error('Invalid email address for confirmation email:', email);
      return {
        success: false,
        message: 'Invalid email address',
        error: `The provided email "${email}" is not valid`
      };
    }
    
    // Validate reference number
    if (!referenceNumber) {
      console.error('Missing reference number for confirmation email');
      return {
        success: false,
        message: 'Missing reference number',
        error: 'A reference number is required to send a confirmation email'
      };
    }
    
    // Validate language
    if (language !== 'fr' && language !== 'en') {
      console.warn(`Invalid language "${language}" for confirmation email, defaulting to French`);
      language = 'fr';
    }

    // Get email content
    const subject = getConfirmationEmailSubject(language);
    const htmlContent = createConfirmationEmailTemplate(referenceNumber, language);

    // Send the email with a timeout to prevent hanging
    console.log(`Sending confirmation email to ${email} with reference ${referenceNumber} in ${language}`);

    // Create a promise that rejects after a timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Email sending timed out after 10 seconds')), 20000);
    });

    try {
      // Race the email sending against the timeout
      const result = await Promise.race([
        sendEmail({
          to: email,
          subject,
          html: htmlContent
        }),
        timeoutPromise
      ]) as any; // Using 'any' here because the race result type is complex

      // Log the result
      if (result.success) {
        console.log('Confirmation email sent successfully');
      } else {
        console.error('Failed to send confirmation email:', result.error);
      }
      
      return result;
    } catch (timeoutError) {
      console.error('Email sending timed out:', timeoutError);
      return {
        success: false,
        message: 'Email sending timed out',
        error: 'The operation took too long to complete'
      };
    }
  } catch (error) {
    console.error('Error sending confirmation email:', error);
    return {
      success: false,
      message: 'Error sending confirmation email',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
