import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Co<PERSON>, <PERSON>, <PERSON><PERSON>, Star } from "lucide-react";

interface WhyChooseSectionProps {
  whyChooseData: {
    badge: {
      icon: string;
      text: string;
    };
    title: string;
    description: string;
    advantages: Array<{
      id: number;
      icon: string;
      title: string;
      description: string;
      color: string;
    }>;
  };
}

const iconMap = {
  CheckCircle,
  Users,
  Cog,
  Eye,
  Zap,
  Star
};

export default function WhyChooseSection({ whyChooseData }: WhyChooseSectionProps) {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center space-y-4 mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium border border-orange-200">
              <CheckCircle className="w-4 h-4 mr-2" />
              {whyChooseData.badge.text}
            </div>

            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {whyChooseData.title}
            </h2>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {whyChooseData.description}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {whyChooseData.advantages.map((advantage) => {
              const IconComponent = iconMap[advantage.icon as keyof typeof iconMap];
              
              return (
                <div key={advantage.id} className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow border border-gray-200">
                  <div className="flex items-start space-x-4">
                    <div className={`w-12 h-12 bg-${advantage.color}-100 rounded-lg flex items-center justify-center flex-shrink-0`}>
                      <IconComponent className={`w-6 h-6 text-${advantage.color}-600`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">{advantage.title}</h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {advantage.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
