/**
 * Utility for uploading form files to Google Drive
 *
 * This module provides specialized functions for handling file uploads from form submissions
 * to Google Drive, including batch uploads and PDF file uploads. It builds on the core
 * Google Drive utilities to provide higher-level functionality specific to form processing.
 *
 * @module upload-form-files
 * @requires ./google-drive
 *
 * @example
 * // Upload multiple files from a form submission
 * const files = event.target.files;
 * const results = await uploadFormFiles(files, 'REF-12345');
 *
 * @example
 * // Upload a PDF generated from form data
 * const pdfBlob = await generatePdf(formData);
 * const result = await uploadPdfFile(pdfBlob, 'form-submission.pdf', 'REF-12345');
 */

import { uploadToGoogleDrive, getMimeType, FileUploadResult, FileUploadOptions } from './google-drive';

/**
 * Upload files from a form submission to Google Drive
 *
 * This function takes an array of File objects (typically from a form submission)
 * and uploads each file to Google Drive. It handles converting the files to buffers,
 * determining the appropriate MIME types, and optionally prefixing the file names
 * with a reference number.
 *
 * @param {File[]} files - Array of File objects from the form submission
 * @param {string} [referenceNumber] - Optional reference number to include in the file names
 *                                    (e.g., 'REF-12345-filename.pdf')
 * @returns {Promise<FileUploadResult[]>} A promise that resolves to an array of upload results,
 *                                       one for each file in the input array
 *
 * @throws {Error} The function handles errors internally and includes error information in the results
 *
 * @example
 * // Upload files without a reference number
 * const fileInput = document.getElementById('fileInput');
 * const results = await uploadFormFiles(Array.from(fileInput.files));
 *
 * @example
 * // Upload files with a reference number
 * const results = await uploadFormFiles(formFiles, 'REF-2023-001');
 *
 * // Check results
 * const successCount = results.filter(r => r.success).length;
 * console.log(`Successfully uploaded ${successCount} of ${results.length} files`);
 */
export async function uploadFormFiles(
  files: File[],
  referenceNumber?: string
): Promise<FileUploadResult[]> {
  const results: FileUploadResult[] = [];

  for (const file of files) {
    try {
      // Convert the file to a buffer
      const fileBuffer = Buffer.from(await file.arrayBuffer());

      // Generate a file name with the reference number if provided
      const fileName = referenceNumber
        ? `${referenceNumber}-${file.name}`
        : file.name;

      // Get the MIME type
      const mimeType = file.type || getMimeType(file.name);

      // Upload the file to Google Drive
      const result = await uploadToGoogleDrive(fileName, mimeType, fileBuffer);
      results.push(result);

      console.log(`Uploaded form file ${fileName}: ${result.success ? 'Success' : 'Failed'}`);
    } catch (error) {
      console.error(`Error uploading form file ${file.name}:`, error);
      results.push({
        success: false,
        fileName: file.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  return results;
}

/**
 * Upload a PDF file to Google Drive
 *
 * This function is specifically designed for uploading PDF files (typically generated from form data)
 * to Google Drive. It handles converting the PDF blob to a buffer and optionally prefixing the file name
 * with a reference number. The PDF is uploaded with private access by default.
 *
 * @param {Blob} pdfBlob - The PDF content as a Blob object (typically from a PDF generation library)
 * @param {string} fileName - The name to give the PDF file in Google Drive
 * @param {string} [referenceNumber] - Optional reference number to include in the file name
 *                                    (e.g., 'REF-12345-form.pdf')
 * @returns {Promise<FileUploadResult>} A promise that resolves to the result of the upload operation
 *
 * @throws {Error} The function handles errors internally and returns a FileUploadResult with success=false
 *
 * @example
 * // Generate and upload a PDF
 * const pdfBlob = await generatePdf(formData);
 * const result = await uploadPdfFile(pdfBlob, 'customer-form.pdf');
 *
 * @example
 * // Upload with a reference number
 * const result = await uploadPdfFile(pdfBlob, 'customer-form.pdf', 'REF-2023-001');
 *
 * if (result.success) {
 *   console.log(`PDF uploaded successfully. View at: ${result.webViewLink}`);
 * } else {
 *   console.error(`Failed to upload PDF: ${result.error}`);
 * }
 */
export async function uploadPdfFile(
  pdfBlob: Blob,
  fileName: string,
  referenceNumber?: string
): Promise<FileUploadResult> {
  try {
    // Convert the blob to a buffer
    const fileBuffer = Buffer.from(await pdfBlob.arrayBuffer());

    // Generate a file name with the reference number if provided
    const finalFileName = referenceNumber
      ? `${referenceNumber}-${fileName}`
      : fileName;

    // Upload the PDF to Google Drive
    const result = await uploadToGoogleDrive(
      finalFileName,
      'application/pdf',
      fileBuffer,
      { makePublic: false } // Keep the PDF private by default
    );

    console.log(`Uploaded PDF ${finalFileName}: ${result.success ? 'Success' : 'Failed'}`);
    return result;
  } catch (error) {
    console.error(`Error uploading PDF ${fileName}:`, error);
    return {
      success: false,
      fileName,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Interface for a file to be uploaded in a batch operation
 *
 * This interface defines the structure of file objects for batch uploads to Google Drive.
 * Unlike the File objects from form submissions, these are pre-processed with the file content
 * already converted to a Buffer.
 *
 * @interface FileToUpload
 */
export interface FileToUpload {
  /**
   * The name of the file to be created in Google Drive
   *
   * This name will be used as-is without any modifications.
   *
   * @type {string}
   * @memberof FileToUpload
   */
  name: string;

  /**
   * The content of the file as a Buffer
   *
   * This should be the raw binary content of the file, already converted to a Node.js Buffer.
   *
   * @type {Buffer}
   * @memberof FileToUpload
   */
  content: Buffer;

  /**
   * The MIME type of the file
   *
   * This is optional and will be determined from the file name if not provided.
   * For example, 'application/pdf' for PDF files, 'image/jpeg' for JPG images, etc.
   *
   * @type {string}
   * @memberof FileToUpload
   */
  mimeType?: string;
}

/**
 * Upload multiple files to Google Drive in a batch operation
 *
 * This function uploads multiple files to Google Drive in a single batch operation.
 * Unlike uploadFormFiles, this function expects the file content to already be converted
 * to Buffers. It's useful for uploading programmatically generated files or files from
 * sources other than form submissions.
 *
 * @param {FileToUpload[]} files - Array of files to upload, each with name, content, and optional MIME type
 * @param {FileUploadOptions} [options={}] - Upload options that apply to all files in the batch
 * @returns {Promise<FileUploadResult[]>} A promise that resolves to an array of upload results,
 *                                       one for each file in the input array
 *
 * @throws {Error} The function handles errors internally and includes error information in the results
 *
 * @example
 * // Upload multiple files to the default folder
 * const files = [
 *   { name: 'document.pdf', content: pdfBuffer },
 *   { name: 'image.jpg', content: imageBuffer, mimeType: 'image/jpeg' }
 * ];
 * const results = await batchUploadFiles(files);
 *
 * @example
 * // Upload multiple files to a specific folder
 * const results = await batchUploadFiles(files, {
 *   folderId: 'specific-folder-id',
 *   makePublic: true
 * });
 */
export async function batchUploadFiles(
  files: FileToUpload[],
  options: FileUploadOptions = {}
): Promise<FileUploadResult[]> {
  const results: FileUploadResult[] = [];

  for (const file of files) {
    try {
      // Get the MIME type if not provided
      const mimeType = file.mimeType || getMimeType(file.name);

      // Upload the file to Google Drive
      const result = await uploadToGoogleDrive(
        file.name,
        mimeType,
        file.content,
        options
      );

      results.push(result);
      console.log(`Batch uploaded file ${file.name}: ${result.success ? 'Success' : 'Failed'}`);
    } catch (error) {
      console.error(`Error in batch upload for file ${file.name}:`, error);
      results.push({
        success: false,
        fileName: file.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  return results;
}