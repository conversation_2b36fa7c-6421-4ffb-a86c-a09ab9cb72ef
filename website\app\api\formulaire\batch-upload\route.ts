import { NextRequest, NextResponse } from 'next/server';
import { uploadToGoogleDrive, createDriveFolder, getMimeType } from '@/app/api/formulaire/utils/google-drive';

// Define the response type
interface BatchUploadResponse {
  success: boolean;
  referenceNumber?: string;
  message?: string;
  error?: string;
  files?: {
    fileId?: string;
    fileName?: string;
    originalFileName?: string;
    webViewLink?: string;
    webContentLink?: string;
    success: boolean;
    error?: string;
  }[];
  folder?: {
    id: string;
    name: string;
    webViewLink?: string;
  };
}

/**
 * POST handler for batch file uploads
 * Receives multiple files and uploads them to a new folder in Google Drive
 * The folder name is based on a reference number with the format FER-ddMMyy-HHmm
 */
export async function POST(request: NextRequest): Promise<NextResponse<BatchUploadResponse>> {
  console.log('API: Received batch upload request');
  try {
    // Parse the form data
    const formData = await request.formData();
    console.log('API: Form data parsed');

    // Get the form data JSON
    const formDataJson = formData.get('formData');
    let formDataObj = {};
    let referenceNumber = '';

    if (formDataJson && typeof formDataJson === 'string') {
      try {
        formDataObj = JSON.parse(formDataJson);
        // Use the reference number from the form data if available
        if (formDataObj && typeof formDataObj === 'object' && 'referenceNumber' in formDataObj) {
          referenceNumber = formDataObj.referenceNumber as string;
        }
      } catch (e) {
        console.error('API: Error parsing form data JSON:', e);
      }
    }

    // If no reference number was provided, generate one
    if (!referenceNumber) {
      // Generate a reference number with the format FER-ddMMyy-HHmm
      const now = new Date();
      const day = String(now.getDate()).padStart(2, '0');
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const year = String(now.getFullYear()).slice(-2);
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      const milliseconds = String(now.getMilliseconds()).padStart(3, '0');

      referenceNumber = `FER-${day}${month}${year}-${hours}${minutes}-${seconds}${milliseconds}`;
    }

    console.log('API: Using reference number:', referenceNumber);

    // Create a new folder in Google Drive with the reference number as the name
    console.log('API: Creating folder in Google Drive with name:', referenceNumber);
    const folder = await createDriveFolder(referenceNumber);

    // Check if we got a valid folder or a fallback folder (offline mode)
    const isOfflineMode = folder.id?.startsWith('fallback-');

    if (!folder || !folder.id) {
      throw new Error('Failed to create folder in Google Drive');
    }

    if (isOfflineMode) {
      console.log('API: Operating in offline mode with fallback folder:', folder.id);
    } else {
      console.log('API: Folder created successfully with ID:', folder.id);
    }

    // Get the main file
    const mainFile = formData.get('mainFile') as File | null;

    // Array to store upload results
    const uploadResults = [];

    // Upload the main file if it exists
    if (mainFile) {
      console.log('API: Uploading main file:', mainFile.name);

      // Check file size (2MB limit)
      if (mainFile.size > 2 * 1024 * 1024) {
        console.error('API: Main file too large:', mainFile.size);
        return NextResponse.json(
          {
            success: false,
            error: 'Main file size exceeds the 2MB limit',
            referenceNumber
          },
          { status: 400 }
        );
      }

      // Get the file buffer
      const fileBuffer = Buffer.from(await mainFile.arrayBuffer());

      // Get the MIME type
      const mimeType = mainFile.type || getMimeType(mainFile.name);
      console.log('API: Main file MIME type:', mimeType);

      // Validate file type (must be PDF)
      if (mimeType !== 'application/pdf') {
        console.error('API: Invalid main file type:', mimeType);
        return NextResponse.json(
          {
            success: false,
            error: 'Main file must be a PDF document',
            referenceNumber
          },
          { status: 400 }
        );
      }

      // Upload the main file to the folder with the reference number as the filename
      let mainFileResult;

      if (isOfflineMode) {
        // In offline mode, create a mock result instead of trying to upload
        console.log('API: Skipping actual upload in offline mode for main file');
        mainFileResult = {
          success: true,
          fileId: `offline-file-${Date.now()}`,
          fileName: `${referenceNumber}.pdf`,
          webViewLink: '#offline-mode',
          webContentLink: '#offline-mode',
          directLink: '#offline-mode',
          error: 'File stored locally in offline mode'
        };
      } else {
        // Normal upload in online mode
        mainFileResult = await uploadToGoogleDrive(
          `${referenceNumber}.pdf`, // Use reference number for the main file name
          mimeType,
          fileBuffer,
          {
            folderId: folder.id,
            makePublic: false, // Keep files private
            fields: 'id,name,webViewLink,webContentLink'
          }
        );
      }

      console.log('API: Main file upload result:', mainFileResult);

      // Add the result to the array
      uploadResults.push({
        ...mainFileResult,
        originalFileName: mainFile.name
      });
    }

    // Get all related files
    const relatedFiles = [];
    const relatedFileNames = [];

    // Extract all related files from the form data
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('relatedFile_') && value instanceof File) {
        const index = key.split('_')[1];
        const nameKey = `relatedFileName_${index}`;
        const fileName = formData.get(nameKey) as string || '';

        relatedFiles.push(value);
        relatedFileNames.push(fileName);
      }
    }

    console.log('API: Found', relatedFiles.length, 'related files');

    // Upload each related file
    for (let i = 0; i < relatedFiles.length; i++) {
      const file = relatedFiles[i];
      const customName = relatedFileNames[i];

      console.log('API: Uploading related file:', file.name, 'with custom name:', customName);

      // Check file size (2MB limit)
      if (file.size > 2 * 1024 * 1024) {
        console.error('API: Related file too large:', file.size);
        uploadResults.push({
          success: false,
          fileName: file.name,
          originalFileName: file.name,
          error: 'File size exceeds the 2MB limit'
        });
        continue;
      }

      try {
        // Get the file buffer
        const fileBuffer = Buffer.from(await file.arrayBuffer());

        // Get the MIME type
        const mimeType = file.type || getMimeType(file.name);
        console.log('API: Related file MIME type:', mimeType);

        // Validate file type
        const acceptedTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'image/jpeg',
          'image/png'
        ];

        if (!acceptedTypes.includes(mimeType)) {
          console.error('API: Invalid related file type:', mimeType);
          uploadResults.push({
            success: false,
            fileName: file.name,
            originalFileName: file.name,
            error: 'Invalid file type. Allowed types: PDF, Word, and images.'
          });
          continue;
        }

        // Generate a unique filename that includes the custom name if provided
        const fileName = customName
          ? `${customName}_${file.name}` // Include custom name in the filename
          : file.name;

        // Upload the file to the folder
        let result;

        if (isOfflineMode) {
          // In offline mode, create a mock result instead of trying to upload
          console.log('API: Skipping actual upload in offline mode for related file:', fileName);
          result = {
            success: true,
            fileId: `offline-file-${Date.now()}-${i}`,
            fileName: fileName,
            webViewLink: '#offline-mode',
            webContentLink: '#offline-mode',
            directLink: '#offline-mode',
            error: 'File stored locally in offline mode'
          };
        } else {
          // Normal upload in online mode
          result = await uploadToGoogleDrive(
            fileName,
            mimeType,
            fileBuffer,
            {
              folderId: folder.id,
              makePublic: false, // Keep files private
              fields: 'id,name,webViewLink,webContentLink'
            }
          );
        }

        console.log('API: Related file upload result:', result);

        // Add the result to the array
        uploadResults.push({
          ...result,
          originalFileName: file.name
        });
      } catch (error) {
        console.error('API: Error uploading related file:', file.name, error);
        uploadResults.push({
          success: false,
          fileName: file.name,
          originalFileName: file.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Check if any files were uploaded successfully
    const anySuccess = uploadResults.some(result => result.success);

    if (!anySuccess) {
      console.error('API: No files were uploaded successfully');
      return NextResponse.json(
        {
          success: false,
          error: 'No files were uploaded successfully',
          referenceNumber,
          folder: {
            id: folder.id || '',
            name: folder.name || referenceNumber,
            webViewLink: folder.webViewLink || ''
          }
        },
        { status: 500 }
      );
    }

    // Return success response with upload results
    return NextResponse.json({
      success: true,
      referenceNumber,
      message: isOfflineMode
        ? `Files processed in offline mode with reference number: ${referenceNumber}. They will be uploaded when connectivity is restored.`
        : `Files uploaded successfully with reference number: ${referenceNumber}`,
      files: uploadResults,
      folder: {
        id: folder.id || '',
        name: folder.name || referenceNumber,
        webViewLink: folder.webViewLink || '',
        isOfflineMode: isOfflineMode
      }
    }, { status: 200 });

  } catch (error) {
    console.error('API: Error processing batch upload:', error);

    // Check if this is a network connectivity error
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    const isNetworkError = errorMessage.includes('ENOTFOUND') ||
                           errorMessage.includes('ETIMEDOUT') ||
                           errorMessage.includes('ECONNREFUSED') ||
                           errorMessage.includes('ECONNRESET') ||
                           errorMessage.includes('network') ||
                           errorMessage.includes('timeout');

    if (isNetworkError) {
      // For network errors, return a more user-friendly message
      return NextResponse.json({
        success: false,
        error: 'Network connectivity issue detected. Please check your internet connection and try again.',
        isOfflineMode: true,
        technicalError: errorMessage
      }, { status: 503 }); // 503 Service Unavailable
    }

    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
}
