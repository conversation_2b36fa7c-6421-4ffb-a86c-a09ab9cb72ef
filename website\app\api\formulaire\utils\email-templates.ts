/**
 * Email templates for form submission confirmations
 */

// Brand colors from globals.css
const BRAND_COLORS = {
  primary: '#0c3c7a', // --primary: oklch(0.37 0.06 198.28)
  accent: '#c98b2a',  // --accent: oklch(0.73 0.17 58.47)
  background: '#ffffff',
  text: '#000000',
  border: '#e5e5e5'
};

/**
 * Creates an HTML email template for form submission confirmation
 * 
 * @param referenceNumber - The reference number of the submitted form
 * @param language - The language to use for the email (fr or en)
 * @returns HTML string for the email
 */
export function createConfirmationEmailTemplate(referenceNumber: string, language: 'fr' | 'en' = 'fr'): string {
  // Validate inputs
  if (!referenceNumber) {
    referenceNumber = 'N/A'; // Provide a fallback if reference number is missing
  }
  
  // Text content based on language
  const content = {
    fr: {
      subject: 'Confirmation de soumission de formulaire - <PERSON>',
      greeting: 'Bon<PERSON>r,',
      confirmation: 'Nous vous confirmons que votre formulaire a été soumis avec succès.',
      reference: 'Numéro de référence:',
      nextSteps: 'Un expert de Charlie <PERSON> Consul<PERSON> vous contactera très prochainement.',
      noReply: 'Ceci est un message automatique, merci de ne pas y répondre.',
      footer: '© Charlie Oscar Consulting. Tous droits réservés.'
    },
    en: {
      subject: 'Form Submission Confirmation - <PERSON> Consulting',
      greeting: 'Hello,',
      confirmation: 'We confirm that your form has been successfully submitted.',
      reference: 'Reference number:',
      nextSteps: 'An expert from Charlie Oscar Consulting will contact you very shortly.',
      noReply: 'This is an automated message, please do not reply.',
      footer: '© Charlie Oscar Consulting. All rights reserved.'
    }
  };

  // Use the selected language or default to French
  const text = content[language] || content.fr;

  // HTML template
  return `
    <!DOCTYPE html>
    <html lang="${language}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${text.subject}</title>
      <style>
        body {
          font-family: 'Helvetica', Arial, sans-serif;
          line-height: 1.6;
          color: ${BRAND_COLORS.text};
          background-color: ${BRAND_COLORS.background};
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: ${BRAND_COLORS.primary};
          color: white;
          padding: 20px;
          text-align: center;
          border-top-left-radius: 4px;
          border-top-right-radius: 4px;
        }
        .content {
          background-color: white;
          padding: 30px;
          border-left: 1px solid ${BRAND_COLORS.border};
          border-right: 1px solid ${BRAND_COLORS.border};
        }
        .reference {
          background-color: #f8f9fa;
          border: 1px solid ${BRAND_COLORS.border};
          border-radius: 4px;
          padding: 15px;
          margin: 20px 0;
          text-align: center;
          font-size: 18px;
          font-weight: bold;
          color: ${BRAND_COLORS.primary};
        }
        .footer {
          background-color: #f8f9fa;
          color: #666;
          text-align: center;
          padding: 15px;
          font-size: 12px;
          border-bottom-left-radius: 4px;
          border-bottom-right-radius: 4px;
          border: 1px solid ${BRAND_COLORS.border};
        }
        .note {
          font-size: 12px;
          color: #666;
          margin-top: 20px;
          padding-top: 15px;
          border-top: 1px solid ${BRAND_COLORS.border};
        }
        .accent {
          color: ${BRAND_COLORS.accent};
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Charlie Oscar Consulting</h1>
        </div>
        <div class="content">
          <p>${text.greeting}</p>
          <p>${text.confirmation}</p>
          
          <div class="reference">
            <span>${text.reference}</span><br>
            <span class="accent">${referenceNumber}</span>
          </div>
          
          <p>${text.nextSteps}</p>
          
          <div class="note">
            <p>${text.noReply}</p>
          </div>
        </div>
        <div class="footer">
          <p>${text.footer}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Get the subject line for confirmation emails
 * 
 * @param language - The language to use (fr or en)
 * @returns The subject line
 */
export function getConfirmationEmailSubject(language: 'fr' | 'en' = 'fr'): string {
  const subjects = {
    fr: 'Confirmation de soumission de formulaire - Charlie Oscar Consulting',
    en: 'Form Submission Confirmation - Charlie Oscar Consulting'
  };
  
  return subjects[language] || subjects.fr;
}
