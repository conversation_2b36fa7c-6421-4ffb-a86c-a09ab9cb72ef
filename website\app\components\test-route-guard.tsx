"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

/**
 * TestRouteGuard component
 * 
 * This component checks if test routes are enabled in the current environment.
 * If not, it redirects to the home page or displays an error message.
 * 
 * @param children The content to render if test routes are enabled
 * @returns The children if test routes are enabled, otherwise an error message
 */
export default function TestRouteGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [isTestEnabled, setIsTestEnabled] = useState<boolean | null>(null);

  useEffect(() => {
    // Check if test routes are enabled
    fetch('/api/environment')
      .then(response => response.json())
      .then(data => {
        setIsTestEnabled(data.testRoutesEnabled);
        
        // If test routes are disabled, redirect to home page after a delay
        if (!data.testRoutesEnabled) {
          setTimeout(() => {
            router.push('/');
          }, 3000);
        }
      })
      .catch(error => {
        console.error('Error checking test routes status:', error);
        setIsTestEnabled(false);
      });
  }, [router]);

  // Show loading state while checking
  if (isTestEnabled === null) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // If test routes are disabled, show an error message
  if (!isTestEnabled) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <div className="text-red-500 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-4">Test routes are disabled in this environment.</p>
          <p className="text-gray-500 text-sm">Redirecting to home page...</p>
        </div>
      </div>
    );
  }

  // If test routes are enabled, render the children
  return <>{children}</>;
}
