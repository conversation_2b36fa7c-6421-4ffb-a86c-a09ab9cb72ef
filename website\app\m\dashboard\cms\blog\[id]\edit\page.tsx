"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useRouter, useParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Save, Upload, X, Plus } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useBlog } from "@/app/m/lib/contexts/blog-context"

export default function EditBlogPostPage() {
  const router = useRouter()
  const params = useParams()
  const { getPost, updatePost, isLoading } = useBlog()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const postId = params.id as string
  const existingPost = getPost(postId)

  // Form state
  const [title, setTitle] = useState("")
  const [intro, setIntro] = useState("")
  const [mainContent, setMainContent] = useState("")
  const [categories, setCategories] = useState<string[]>([])
  const [relatedPosts, setRelatedPosts] = useState<string[]>([])
  const [coverImage, setCoverImage] = useState<File | null>(null)
  const [coverImagePreview, setCoverImagePreview] = useState<string>("")

  // Input states for adding new items
  const [newCategory, setNewCategory] = useState("")
  const [newRelatedPost, setNewRelatedPost] = useState("")

  // Load existing post data
  useEffect(() => {
    if (existingPost) {
      setTitle(existingPost.title)
      setIntro(existingPost.intro)
      setMainContent(existingPost.mainContent)
      setCategories(existingPost.categories)
      setRelatedPosts(existingPost.relatedPosts)
      // Note: We can't pre-populate the file input, but we could show existing cover image URL
    }
  }, [existingPost])

  // Redirect if post not found
  if (!existingPost) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Post Not Found</h2>
            <p className="text-muted-foreground">The blog post you're looking for doesn't exist.</p>
            <Button asChild className="mt-4">
              <Link href="/m/dashboard/cms/blog">Back to Posts</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setCoverImage(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setCoverImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeCoverImage = () => {
    setCoverImage(null)
    setCoverImagePreview("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const addCategory = () => {
    if (newCategory.trim() && !categories.includes(newCategory.trim())) {
      setCategories([...categories, newCategory.trim()])
      setNewCategory("")
    }
  }

  const removeCategory = (categoryToRemove: string) => {
    setCategories(categories.filter((cat) => cat !== categoryToRemove))
  }

  const addRelatedPost = () => {
    if (newRelatedPost.trim() && !relatedPosts.includes(newRelatedPost.trim())) {
      setRelatedPosts([...relatedPosts, newRelatedPost.trim()])
      setNewRelatedPost("")
    }
  }

  const removeRelatedPost = (postToRemove: string) => {
    setRelatedPosts(relatedPosts.filter((post) => post !== postToRemove))
  }

  const handleSave = async () => {
    try {
      await updatePost(postId, {
        title,
        intro,
        mainContent,
        categories,
        relatedPosts,
        coverImage: coverImage || undefined,
      })

      router.push("/m/dashboard/cms/blog")
    } catch (error) {
      console.error("Error updating blog post:", error)
      alert("Error updating blog post. Please try again.")
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/m/dashboard/cms/blog">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Posts
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Blog Post</h1>
          <p className="text-muted-foreground">Update your blog article</p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-6">
          {/* Cover Image */}
          <Card>
            <CardHeader>
              <CardTitle>Cover Image</CardTitle>
              <CardDescription>Upload a new cover image or keep the existing one</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {coverImagePreview ? (
                  <div className="relative">
                    <Image
                      src={coverImagePreview || "/placeholder.svg"}
                      alt="Cover preview"
                      width={400}
                      height={200}
                      className="w-full h-48 object-cover rounded-md"
                    />
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={removeCoverImage}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div
                    className="border-2 border-dashed border-muted-foreground/25 rounded-md p-8 text-center cursor-pointer hover:border-primary/50 transition-colors"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-lg font-medium">Click to upload new cover image</p>
                    <p className="text-sm text-muted-foreground">PNG, JPG, GIF up to 10MB</p>
                  </div>
                )}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleCoverImageChange}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          {/* Post Content */}
          <Card>
            <CardHeader>
              <CardTitle>Post Content</CardTitle>
              <CardDescription>Update the main content of your blog post</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter post title..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="intro">Introduction</Label>
                <Textarea
                  id="intro"
                  value={intro}
                  onChange={(e) => setIntro(e.target.value)}
                  placeholder="Write an engaging introduction..."
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="mainContent">Main Content</Label>
                <Textarea
                  id="mainContent"
                  value={mainContent}
                  onChange={(e) => setMainContent(e.target.value)}
                  placeholder="Write the main content of your blog post..."
                  rows={15}
                  className="min-h-[400px] resize-y"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          {/* Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Categories</CardTitle>
              <CardDescription>Update categories for your blog post</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  placeholder="Add category..."
                  onKeyPress={(e) => e.key === "Enter" && addCategory()}
                />
                <Button onClick={addCategory} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Badge key={category} variant="secondary" className="flex items-center gap-1">
                    {category}
                    <X className="h-3 w-3 cursor-pointer" onClick={() => removeCategory(category)} />
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Related Posts */}
          <Card>
            <CardHeader>
              <CardTitle>Related Posts</CardTitle>
              <CardDescription>Update slugs of related blog posts</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newRelatedPost}
                  onChange={(e) => setNewRelatedPost(e.target.value)}
                  placeholder="Add post slug..."
                  onKeyPress={(e) => e.key === "Enter" && addRelatedPost()}
                />
                <Button onClick={addRelatedPost} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-2">
                {relatedPosts.map((post) => (
                  <div key={post} className="flex items-center justify-between p-2 bg-muted rounded-md">
                    <span className="text-sm">{post}</span>
                    <X className="h-4 w-4 cursor-pointer" onClick={() => removeRelatedPost(post)} />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="flex gap-2">
            <Button onClick={handleSave} className="flex-1" disabled={isLoading}>
              <Save className="mr-2 h-4 w-4" />
              {isLoading ? "Updating..." : "Update Post"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
