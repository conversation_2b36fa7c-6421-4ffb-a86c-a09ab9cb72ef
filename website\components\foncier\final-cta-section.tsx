"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Phone, Mail, MapPin, Calendar, MessageSquare, ArrowRight } from "lucide-react";
import { getFoncierContent } from "@/app/cms/utils/foncier";
import { getContactInfo } from "@/app/cms/utils/contact";

export function FinalCtaSection() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);
  const finalCta = getFoncierContent().finalCta;

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const contact = getContactInfo();
  const safePhone = typeof contact === 'object' && contact && contact.phone ? contact.phone : "";
  const safeEmail = typeof contact === 'object' && contact && contact.email ? contact.email : "";
  const contactMethods = [
    {
      icon: Phone,
      title: "Appelez-nous",
      description: "Échange immédiat avec un expert",
      action: `tel:${safePhone.replace(/\s+/g, '')}`,
      label: safePhone,
      availability: "Lun-Ven: 8h-18h",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: Mail,
      title: "Écrivez-nous",
      description: "Réponse détaillée sous 24h",
      action: `mailto:${safeEmail}`,
      label: safeEmail,
      availability: "Réponse garantie",
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: Calendar,
      title: "Prenez RDV",
      description: "Consultation en présentiel",
      action: "/contact",
      label: "Réserver un créneau",
      availability: "Yaoundé & déplacements",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    }
  ];

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Cpath%20d%3D%22M36%2034v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6%2034v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6%204V0H4v4H0v2h4v4h2V6h4V4H6z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] bg-repeat"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Main CTA */}
          <div className={`text-center space-y-8 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl font-bold leading-tight">
                Besoin d'un accompagnement{" "}
                <span className="text-primary relative">
                  personnalisé ?
                  <div className="absolute -bottom-2 left-0 w-full h-1 bg-primary/40 rounded-full"></div>
                </span>
              </h2>
              
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Contactez nos experts fonciers dès maintenant pour sécuriser votre patrimoine 
                et valoriser vos investissements immobiliers.
              </p>
            </div>

            {/* Primary CTAs */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-white px-8 py-4 text-lg group">
                <Link href="/formulaire" className="flex items-center">
                  Demander un devis gratuit
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              
              <Button asChild size="lg" variant="outline" className="border-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg">
                <Link href="/contact">
                  Prendre rendez-vous
                </Link>
              </Button>
            </div>
          </div>

          <div className={`grid md:grid-cols-3 gap-8 mb-16 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            {contactMethods.map((method, index) => {
              const Icon = method.icon;
              
              return (
                <Card key={index} className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/15 transition-all duration-300 group">
                  <CardContent className="p-6 text-center space-y-4">
                    <div className={`w-16 h-16 ${method.bgColor} rounded-full flex items-center justify-center mx-auto transition-transform duration-300 group-hover:scale-110`}>
                      <Icon className={`w-8 h-8 ${method.color}`} />
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-xl font-bold text-white">
                        {method.title}
                      </h3>
                      <p className="text-gray-300 text-sm">
                        {method.description}
                      </p>
                    </div>

                    <div className="space-y-3">
                      <Button asChild variant="outline">
                        {method.action.startsWith('/') ? (
                          <Link href={method.action}>
                            {method.label}
                          </Link>
                        ) : (
                          <a href={method.action}>
                            {method.label}
                          </a>
                        )}
                      </Button>
                      
                      <p className="text-xs text-gray-400">
                        {method.availability}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Emergency Contact */}
          <div className={`text-center mt-16 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="bg-primary/10 backdrop-blur-sm rounded-xl p-6 border border-primary/20">
              <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
                <div className="text-center md:text-left">
                  <h4 className="text-lg font-bold text-white mb-2">
                    Urgence foncière ?
                  </h4>
                  <p className="text-gray-300 text-sm">
                    Contactez notre ligne d'urgence pour les situations critiques
                  </p>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button asChild variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
                    <a href="tel:+237600000000" className="flex items-center">
                      <Phone className="w-4 h-4 mr-2" />
                      Appel d'urgence
                    </a>
                  </Button>
                  
                  <Button asChild variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
                    <a href="mailto:<EMAIL>" className="flex items-center">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Email urgent
                    </a>
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Final Message */}
          <div className={`text-center mt-12 transition-all duration-1000 delay-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="bg-gray-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {finalCta?.title || "Prêt à sécuriser vos droits fonciers ?"}
              </h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                {finalCta?.description || "Nos experts vous accompagnent à chaque étape pour garantir le succès de votre projet foncier."}
              </p>

              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-white px-8 py-4 text-lg">
                <Link href="/formulaire" className="flex items-center">
                  {finalCta?.ctaText || "Contactez-nous dès maintenant"}
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
