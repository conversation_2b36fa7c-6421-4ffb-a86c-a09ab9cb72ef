import { <PERSON><PERSON><PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getAllImmobilierServices, getFeaturedImmobilierServices } from "@/lib/immobilier-services-data";
import { ArrowRight, Home, Key, Building, MapPin, Users, Shield, CheckCircle } from "lucide-react";
import UnderConstruction from "@/components/UnderConstruction";

export const metadata: Metadata = {
  title: "Immobilier - Solutions complètes | Charlie Oscar Consulting",
  description: "Découvrez nos services immobiliers au Cameroun : achat, vente, location de biens résidentiels et commerciaux, conseil juridique immobilier.",
  keywords: [
    "immobilier Cameroun",
    "achat vente maison",
    "location bureau",
    "conseil immobilier",
    "transaction immobilière",
    "Charlie Oscar"
  ],
  openGraph: {
    title: "Immobilier - Solutions complètes | Charlie Oscar Consulting",
    description: "Découvrez nos services immobiliers au Cameroun : achat, vente, location de biens résidentiels et commerciaux.",
    type: "website",
    locale: "fr_FR",
    siteName: "Charlie Oscar Consulting"
  },
  alternates: {
    canonical: "/immobilier"
  }
};

export default function ImmobilierPage() {
  const allServices = getAllImmobilierServices();
  const featuredServices = getFeaturedImmobilierServices();

  
  
    return (
      <UnderConstruction />
    );  
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section - Modern & Clean */}
      <section className="relative py-20 lg:py-32 overflow-hidden">
        {/* Clean geometric background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white">
          <div className="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-bl from-primary/5 to-transparent"></div>
          <div className="absolute bottom-0 left-0 w-1/3 h-2/3 bg-gradient-to-tr from-accent/5 to-transparent"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Content */}
              <div className="space-y-8">
                <div className="space-y-6">
                  <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
                    <Home className="w-4 h-4 mr-2" />
                    Solutions immobilières
                  </div>
                  
                  <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                    Votre partenaire
                    <span className="block text-primary">immobilier</span>
                    de confiance
                  </h1>
                  
                  <p className="text-xl text-gray-600 leading-relaxed">
                    De l'achat à la vente, de la location au conseil juridique, 
                    nous vous accompagnons dans tous vos projets immobiliers au Cameroun.
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Button asChild size="lg" className="group">
                    <Link href="/immobilier/services" className="flex items-center">
                      Découvrir nos services
                      <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>
                  
                  <Button asChild size="lg" variant="outline">
                    <Link href="/formulaire">
                      Demander un devis
                    </Link>
                  </Button>
                </div>

                {/* Trust indicators */}
                <div className="flex items-center space-x-8 pt-8">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">500+</div>
                    <div className="text-sm text-gray-600">Transactions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">98%</div>
                    <div className="text-sm text-gray-600">Satisfaction</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">10+</div>
                    <div className="text-sm text-gray-600">Années d'expérience</div>
                  </div>
                </div>
              </div>

              {/* Visual Elements */}
              <div className="relative">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-4">
                    <Card className="border-0 shadow-lg bg-white">
                      <CardContent className="p-6">
                        <Building className="w-8 h-8 text-primary mb-3" />
                        <h3 className="font-semibold text-gray-900 mb-2">Vente & Achat</h3>
                        <p className="text-sm text-gray-600">Transactions sécurisées</p>
                      </CardContent>
                    </Card>
                    
                    <Card className="border-0 shadow-lg bg-white">
                      <CardContent className="p-6">
                        <Key className="w-8 h-8 text-accent mb-3" />
                        <h3 className="font-semibold text-gray-900 mb-2">Location</h3>
                        <p className="text-sm text-gray-600">Résidentiel & commercial</p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className="space-y-4 pt-8">
                    <Card className="border-0 shadow-lg bg-white">
                      <CardContent className="p-6">
                        <Shield className="w-8 h-8 text-secondary mb-3" />
                        <h3 className="font-semibold text-gray-900 mb-2">Conseil Juridique</h3>
                        <p className="text-sm text-gray-600">Expertise légale</p>
                      </CardContent>
                    </Card>
                    
                    <Card className="border-0 shadow-lg bg-white">
                      <CardContent className="p-6">
                        <MapPin className="w-8 h-8 text-orange-500 mb-3" />
                        <h3 className="font-semibold text-gray-900 mb-2">Évaluation</h3>
                        <p className="text-sm text-gray-600">Prix du marché</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Section Header */}
            <div className="text-center space-y-6 mb-16">
              <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
                Nos services
              </div>
              
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                Une expertise complète à votre service
              </h2>
              
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Que vous soyez particulier ou professionnel, nous avons la solution 
                immobilière adaptée à vos besoins.
              </p>
            </div>

            {/* Featured Services */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {featuredServices.map((service, index) => (
                <Card key={service.id} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                  <CardHeader className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Badge className="bg-primary/10 text-primary border-primary/20">
                        {service.category}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {service.estimatedDuration}
                      </Badge>
                    </div>
                    
                    <CardTitle className="text-xl group-hover:text-primary transition-colors">
                      {service.title}
                    </CardTitle>
                    
                    <CardDescription className="text-gray-600 leading-relaxed">
                      {service.shortDescription}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <h4 className="font-semibold text-gray-900 text-sm">Avantages clés :</h4>
                      <ul className="space-y-1">
                        {service.results.slice(0, 3).map((result, resultIndex) => (
                          <li key={resultIndex} className="flex items-start space-x-2 text-sm text-gray-600">
                            <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0 mt-0.5" />
                            <span>{result}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <Button asChild variant="outline" className="w-full group/btn">
                      <Link href={`/immobilier/services/${service.slug}`} className="flex items-center justify-center">
                        En savoir plus
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* All Services Link */}
            <div className="text-center">
              <Button asChild size="lg" variant="outline">
                <Link href="/immobilier/services" className="flex items-center">
                  Voir tous nos services immobiliers
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                <div className="space-y-6">
                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                    Pourquoi choisir Charlie Oscar pour vos projets immobiliers ?
                  </h2>
                  
                  <p className="text-xl text-gray-600 leading-relaxed">
                    Notre expertise du marché camerounais et notre approche personnalisée 
                    font de nous le partenaire idéal pour tous vos projets immobiliers.
                  </p>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Users className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Accompagnement personnalisé</h3>
                      <p className="text-gray-600 text-sm">Un conseiller dédié vous accompagne tout au long de votre projet</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Shield className="w-6 h-6 text-accent" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Sécurité juridique</h3>
                      <p className="text-gray-600 text-sm">Toutes nos transactions sont sécurisées juridiquement</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <MapPin className="w-6 h-6 text-secondary" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Connaissance du marché</h3>
                      <p className="text-gray-600 text-sm">Expertise approfondie du marché immobilier camerounais</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="relative">
                <div className="bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl p-8">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary mb-2">500+</div>
                      <div className="text-sm text-gray-600">Transactions réussies</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary mb-2">98%</div>
                      <div className="text-sm text-gray-600">Clients satisfaits</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary mb-2">15j</div>
                      <div className="text-sm text-gray-600">Délai moyen</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                      <div className="text-sm text-gray-600">Support client</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Prêt à concrétiser votre projet immobilier ?
            </h2>
            
            <p className="text-xl text-primary-foreground/90">
              Contactez nos experts pour une consultation personnalisée et découvrez 
              comment nous pouvons vous aider à réussir votre projet.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" variant="secondary" className="group">
                <Link href="/formulaire" className="flex items-center">
                  Demander un devis gratuit
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
                <Link href="/contact">
                  Prendre rendez-vous
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
