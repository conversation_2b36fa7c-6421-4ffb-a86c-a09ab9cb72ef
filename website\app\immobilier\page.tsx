import { Metada<PERSON> } from "next";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Home,
  Building2,
  MapPin,
  Key,
  Users,
  Award,
  CheckCircle,
  Target,
  Shield,
  Clock,
  Star,
  TrendingUp,
  ShieldCheck,
  UserCheck,
  Eye,
  Zap,
  Scale,
  Compass
} from "lucide-react";
import { getImmobilierContent } from "@/app/cms/utils/immobilier";

const immobilierData = getImmobilierContent();

export const metadata: Metadata = {
  title: immobilierData.metadata.title,
  description: immobilierData.metadata.description,
  keywords: immobilierData.metadata.keywords,
  openGraph: immobilierData.metadata.openGraph,
  alternates: {
    canonical: immobilierData.metadata.canonical
  }
};

export default function ImmobilierPage() {
  const heroData = immobilierData.hero;
  const aboutData = immobilierData.about;
  const servicesData = immobilierData.services;
  const advantagesData = immobilierData.advantages;
  const ctaData = immobilierData.cta;

  const backgroundImageStyle = {
    backgroundImage: `url('${heroData.backgroundImage}')`
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60 z-10"></div>
          <div className="w-full h-full bg-gradient-to-br from-blue-900 via-gray-800 to-green-900"></div>
          {/* Real estate image - easily modifiable */}
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={backgroundImageStyle}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative z-20">
          <div className="max-w-4xl mx-auto text-center text-white space-y-8">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-blue-600/90 backdrop-blur-sm rounded-full text-sm font-medium border border-blue-500">
              <Home className="w-4 h-4 mr-2" />
              {heroData.badge.text}
            </div>

            {/* Main Title */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
              {heroData.title.main}
              <span className="block text-blue-400">{heroData.title.highlight}</span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed">
              {heroData.subtitle}
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center pt-8">
              {heroData.buttons.map((button, index) => (
                <Button
                  key={index}
                  asChild
                  size="lg"
                  className={button.variant === 'primary'
                    ? "bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg group"
                    : "border-white text-primary hover:bg-white hover:text-blue-600 px-8 py-4 text-lg backdrop-blur-sm"
                  }
                  variant={button.variant === 'primary' ? 'default' : 'outline'}
                >
                  <Link href={button.href} className="flex items-center">
                    {button.text}
                    {button.icon && <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />}
                  </Link>
                </Button>
              ))}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-16 max-w-2xl mx-auto">
              {heroData.stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-blue-400 mb-2">{stat.value}</div>
                  <div className="text-sm text-gray-300">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce"></div>
          </div>
        </div>
      </section>

      {/* About Section - Minimalist with Images */}
      <section className="py-32 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-20 items-center">
              {/* Content */}
              <div className="space-y-12">
                <div className="space-y-8">
                  <div className="inline-flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
                    <Building2 className="w-4 h-4 mr-2" />
                    {aboutData.badge}
                  </div>

                  <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                    {aboutData.title}
                  </h2>

                  <p className="text-xl text-gray-600 leading-relaxed">
                    {aboutData.description}
                  </p>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-2 gap-8">
                  {aboutData.features.map((feature, index) => (
                    <div key={index} className="space-y-4">
                      <div className="w-14 h-14 bg-blue-50 rounded-2xl flex items-center justify-center">
                        {feature.icon === 'target' && <Target className="w-7 h-7 text-blue-600" />}
                        {feature.icon === 'shield' && <Shield className="w-7 h-7 text-blue-600" />}
                        {feature.icon === 'users' && <Users className="w-7 h-7 text-blue-600" />}
                        {feature.icon === 'network' && <Building2 className="w-7 h-7 text-blue-600" />}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                        <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Images Grid - Minimalist Layout */}
              <div className="relative">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-6">
                    <div className="h-64 rounded-3xl overflow-hidden shadow-lg">
                      <Image
                        src={aboutData.images[0]}
                        alt="Immobilier moderne"
                        width={400}
                        height={300}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="h-48 rounded-3xl overflow-hidden shadow-lg">
                      <Image
                        src={aboutData.images[1]}
                        alt="Consultation immobilière"
                        width={400}
                        height={200}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                  <div className="space-y-6 pt-12">
                    <div className="h-48 rounded-3xl overflow-hidden shadow-lg">
                      <Image
                        src={aboutData.images[2]}
                        alt="Architecture moderne"
                        width={400}
                        height={200}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="h-64 rounded-3xl overflow-hidden shadow-lg">
                      <Image
                        src={aboutData.images[3]}
                        alt="Intérieur élégant"
                        width={400}
                        height={300}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-6 -right-6 w-12 h-12 bg-blue-100 rounded-full opacity-60"></div>
                <div className="absolute -bottom-6 -left-6 w-8 h-8 bg-gray-200 rounded-full opacity-60"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section - Clean & Visual */}
      <section className="py-32 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center space-y-8 mb-20">
              <div className="inline-flex items-center px-6 py-3 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
                <Building2 className="w-4 h-4 mr-2" />
                Nos Services
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 max-w-4xl mx-auto leading-tight">
                {servicesData.title}
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {servicesData.subtitle}
              </p>
            </div>

            {/* Services Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {servicesData.items.map((service, index) => (
                <Card key={service.id} className="group hover:shadow-2xl transition-all duration-500 border-0 shadow-lg bg-white overflow-hidden">
                  {/* Service Image */}
                  <div className="h-48 overflow-hidden">
                    <Image
                      src={service.image}
                      alt={service.title}
                      width={400}
                      height={200}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                  </div>

                  <CardHeader className="space-y-4 p-8">
                    <div className="flex items-center justify-between">
                      <Badge className={`
                        ${service.color === 'orange' ? 'bg-orange-100 text-orange-800 border-orange-200' : ''}
                        ${service.color === 'blue' ? 'bg-blue-100 text-blue-800 border-blue-200' : ''}
                        ${service.color === 'green' ? 'bg-green-100 text-green-800 border-green-200' : ''}
                        ${service.color === 'purple' ? 'bg-purple-100 text-purple-800 border-purple-200' : ''}
                        ${service.color === 'yellow' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : ''}
                        ${service.color === 'red' ? 'bg-red-100 text-red-800 border-red-200' : ''}
                      `}>
                        {service.icon === 'map' && <MapPin className="w-3 h-3 mr-1" />}
                        {service.icon === 'home' && <Home className="w-3 h-3 mr-1" />}
                        {service.icon === 'building' && <Building2 className="w-3 h-3 mr-1" />}
                        {service.icon === 'key' && <Key className="w-3 h-3 mr-1" />}
                        {service.icon === 'compass' && <Compass className="w-3 h-3 mr-1" />}
                        {service.icon === 'scale' && <Scale className="w-3 h-3 mr-1" />}
                        Service
                      </Badge>
                    </div>

                    <CardTitle className={`text-xl group-hover:text-${service.color}-600 transition-colors`}>
                      {service.title}
                    </CardTitle>

                    <CardDescription className="text-gray-600 leading-relaxed">
                      {service.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="px-8 pb-8">
                    <Button
                      asChild
                      variant="outline"
                      className={`w-full group/btn border-${service.color}-200 hover:bg-${service.color}-50`}
                    >
                      <Link href={`/immobilier/services#${service.id}`} className="flex items-center justify-center">
                        En savoir plus
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* All Services Link */}
            <div className="text-center">
              <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4">
                <Link href="/immobilier/services" className="flex items-center">
                  Découvrir tous nos services immobiliers
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Advantages Section - Minimalist Grid */}
      <section className="py-32 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center space-y-8 mb-20">
              <div className="inline-flex items-center px-6 py-3 bg-blue-50 text-blue-800 rounded-full text-sm font-medium">
                <Star className="w-4 h-4 mr-2" />
                Nos Avantages
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 max-w-4xl mx-auto leading-tight">
                {advantagesData.title}
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {advantagesData.subtitle}
              </p>
            </div>

            {/* Advantages Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {advantagesData.items.map((advantage, index) => (
                <div key={index} className="group p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100">
                  <div className="space-y-6">
                    <div className="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                      {advantage.icon === 'trending-up' && <TrendingUp className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'shield-check' && <ShieldCheck className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'user-check' && <UserCheck className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'users' && <Users className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'eye' && <Eye className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'zap' && <Zap className="w-8 h-8 text-blue-600" />}
                    </div>

                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                        {advantage.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {advantage.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Stats Section */}
            <div className="mt-20 bg-gradient-to-br from-blue-50 via-white to-gray-50 rounded-3xl p-12 border border-gray-100">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div className="text-center space-y-3">
                  <div className="text-4xl font-bold text-blue-600">10+</div>
                  <div className="text-sm text-gray-600 font-medium">Années d'expérience</div>
                </div>
                <div className="text-center space-y-3">
                  <div className="text-4xl font-bold text-blue-600">100%</div>
                  <div className="text-sm text-gray-600 font-medium">Transactions sécurisées</div>
                </div>
                <div className="text-center space-y-3">
                  <div className="text-4xl font-bold text-blue-600">24/7</div>
                  <div className="text-sm text-gray-600 font-medium">Accompagnement</div>
                </div>
                <div className="text-center space-y-3">
                  <div className="text-4xl font-bold text-blue-600">6</div>
                  <div className="text-sm text-gray-600 font-medium">Services spécialisés</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Clean & Modern */}
      <section className="py-32 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center space-y-12">
            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl font-bold leading-tight">
                {ctaData.title}
              </h2>

              <p className="text-xl text-blue-100 leading-relaxed max-w-3xl mx-auto">
                {ctaData.description}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              {ctaData.buttons.map((button, index) => (
                <Button
                  key={index}
                  asChild
                  size="lg"
                  className={button.variant === 'secondary'
                    ? "bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg group"
                    : "border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg"
                  }
                  variant={button.variant === 'secondary' ? 'secondary' : 'outline'}
                >
                  <Link href={button.href} className="flex items-center">
                    {button.text}
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
