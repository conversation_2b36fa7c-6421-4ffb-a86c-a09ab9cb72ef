/**
 * Environment utility functions
 * 
 * This module provides utilities for checking the current environment
 * and controlling feature availability based on environment.
 */

/**
 * Enum representing the possible environments
 */
export enum Environment {
  Development = 'development',
  Test = 'test',
  Staging = 'staging',
  Production = 'production',
}

/**
 * Get the current environment
 * 
 * @returns The current environment based on NODE_ENV
 */
export function getEnvironment(): Environment {
  const env = process.env.NODE_ENV || 'development';
  
  switch (env.toLowerCase()) {
    case 'production':
      return Environment.Production;
    case 'test':
      return Environment.Test;
    case 'staging':
      return Environment.Staging;
    case 'development':
    default:
      return Environment.Development;
  }
}

/**
 * Check if the current environment is development
 * 
 * @returns true if the current environment is development
 */
export function isDevelopment(): boolean {
  return getEnvironment() === Environment.Development;
}

/**
 * Check if the current environment is test
 * 
 * @returns true if the current environment is test
 */
export function isTest(): boolean {
  return getEnvironment() === Environment.Test;
}

/**
 * Check if the current environment is staging
 * 
 * @returns true if the current environment is staging
 */
export function isStaging(): boolean {
  return getEnvironment() === Environment.Staging;
}

/**
 * Check if the current environment is production
 * 
 * @returns true if the current environment is production
 */
export function isProduction(): boolean {
  return getEnvironment() === Environment.Production;
}

/**
 * Check if test routes should be enabled
 * 
 * Test routes are enabled in development and staging environments,
 * but disabled in production.
 * 
 * @returns true if test routes should be enabled
 */
export function areTestRoutesEnabled(): boolean {
  // Enable test routes in development and staging, but not in production
  return !isProduction();
}

/**
 * Check if debug features should be enabled
 * 
 * Debug features are enabled in development and test environments,
 * but disabled in staging and production.
 * 
 * @returns true if debug features should be enabled
 */
export function areDebugFeaturesEnabled(): boolean {
  // Enable debug features in development and test, but not in staging or production
  return isDevelopment() || isTest();
}
