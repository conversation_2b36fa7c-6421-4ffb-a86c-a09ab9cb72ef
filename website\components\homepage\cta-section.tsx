"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Phone, Mail, MapPin, Clock, ArrowRight } from "lucide-react";

export function CTASection({ content }: { content: any }) {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const iconMap: Record<string, any> = { Phone, Mail, MapPin, Clock };
  const contactMethods = (content?.contactMethods || []).map((method: any) => ({
    ...method,
    icon: iconMap[method.icon] || Phone
  }));

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Main CTA */}
          <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <Card className="overflow-hidden shadow-2xl border-0 bg-gradient-to-br from-primary via-primary to-primary/90">
              <CardContent className="p-0">
                <div className="grid lg:grid-cols-2 gap-0">
                  {/* Content */}
                  <div className="p-8 lg:p-12 text-white space-y-8">
                    <div className="space-y-6">
                      <div className="inline-flex items-center px-4 py-2 bg-white/20 text-white rounded-full text-sm font-medium backdrop-blur-sm">
                        <Clock className="w-4 h-4 mr-2" />
                        {content?.sectionTitle}
                      </div>
                      <h2 className="text-3xl md:text-4xl font-bold leading-tight">
                        {content?.sectionSubtitle}
                      </h2>
                      <p className="text-xl text-white/90 leading-relaxed">
                        {content?.benefitsTitle}
                      </p>
                      <ul className="list-disc pl-6">
                        {content?.benefits && content.benefits.map((benefit: string, idx: number) => (
                          <li key={idx}>{benefit}</li>
                        ))}
                      </ul>
                    </div>
                    <div className="flex gap-4 pt-4">
                      {content?.primaryAction && (
                        <Button asChild size="lg" className="bg-white text-primary" variant={"solid"}>
                          <Link href={content.primaryAction.url}>
                            {content.primaryAction.label}
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      )}

                    </div>
                  </div>
                  {/* Visual/Contact Methods */}
                  <div className="bg-white/10 p-8 lg:p-12 flex flex-col justify-center space-y-8">
                    <div className="space-y-2">
                      <div className="text-lg font-bold text-white/80">{content?.visual?.headline}</div>
                      <div className="text-sm text-white/60">{content?.visual?.subline}</div>
                      {/* <div className="text-xs text-white/50">{content?.visual?.online} &bull; {content?.visual?.clients}</div> */}
                    </div>
                    <div className="space-y-4">
                      {contactMethods.map((method: any, idx: number) => {
                        const Icon = method.icon;
                        return (
                          <div key={idx} className={`flex items-center gap-4 p-4 rounded-lg ${method.bgColor} ${method.color}`}>
                            <Icon className="w-6 h-6" />
                            <div>
                              <div className="font-bold">{method.title}</div>
                              <div className="text-xs">{method.description}</div>
                              <a href={method.action} className="underline text-sm">{method.value}</a>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
