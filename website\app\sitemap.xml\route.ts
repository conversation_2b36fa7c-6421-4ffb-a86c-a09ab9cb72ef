import { getAllFoncierServices } from '@/lib/foncier-services-data';
import { getAllImmobilierServices } from '@/lib/immobilier-services-data';
import { getAllConstructionServices } from '@/lib/construction-services-data';

export async function GET() {
  const baseUrl = 'https://www.charlieoscarconsulting.com';
  
  // Get all services for dynamic routes
  const foncierServices = getAllFoncierServices();
  const immobilierServices = getAllImmobilierServices();
  const constructionServices = getAllConstructionServices();

  // Static pages with their priorities and change frequencies
  const staticPages = [
    {
      url: '',
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly',
      priority: 1.0
    },
    {
      url: '/a-propos',
      lastModified: new Date().toISOString(),
      changeFrequency: 'monthly',
      priority: 0.8
    },
    {
      url: '/contact',
      lastModified: new Date().toISOString(),
      changeFrequency: 'monthly',
      priority: 0.7
    },
    {
      url: '/formulaire',
      lastModified: new Date().toISOString(),
      changeFrequency: 'monthly',
      priority: 0.9
    },
    // Foncier section
    {
      url: '/foncier',
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly',
      priority: 0.9
    },
    {
      url: '/foncier/services',
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly',
      priority: 0.8
    },
    // Immobilier section
    {
      url: '/immobilier',
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly',
      priority: 0.9
    },
    {
      url: '/immobilier/services',
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly',
      priority: 0.8
    },
    // Construction section
    {
      url: '/construction',
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly',
      priority: 0.9
    },
    {
      url: '/construction/services',
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly',
      priority: 0.8
    },
    // Additional static pages
    {
      url: '/blog',
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly',
      priority: 0.7
    },
    {
      url: '/projets',
      lastModified: new Date().toISOString(),
      changeFrequency: 'monthly',
      priority: 0.7
    },
    {
      url: '/mentions-legales',
      lastModified: new Date().toISOString(),
      changeFrequency: 'yearly',
      priority: 0.5
    },
    {
      url: '/confidentialite',
      lastModified: new Date().toISOString(),
      changeFrequency: 'yearly',
      priority: 0.5
    },
    {
      url: '/conditions',
      lastModified: new Date().toISOString(),
      changeFrequency: 'yearly',
      priority: 0.5
    },
    {
      url: '/site-map',
      lastModified: new Date().toISOString(),
      changeFrequency: 'monthly',
      priority: 0.4
    }
  ];

  // Dynamic service pages
  const foncierServicePages = foncierServices.map(service => ({
    url: `/foncier/services/${service.slug}`,
    lastModified: new Date().toISOString(),
    changeFrequency: 'monthly',
    priority: service.featured ? 0.8 : 0.7
  }));

  const immobilierServicePages = immobilierServices.map(service => ({
    url: `/immobilier/services/${service.slug}`,
    lastModified: new Date().toISOString(),
    changeFrequency: 'monthly',
    priority: service.featured ? 0.8 : 0.7
  }));

  const constructionServicePages = constructionServices.map(service => ({
    url: `/construction/services/${service.slug}`,
    lastModified: new Date().toISOString(),
    changeFrequency: 'monthly',
    priority: service.featured ? 0.8 : 0.7
  }));

  // Combine all pages
  const allPages = [
    ...staticPages,
    ...foncierServicePages,
    ...immobilierServicePages,
    ...constructionServicePages
  ];

  // Generate XML sitemap
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
${allPages
  .map(
    (page) => `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changeFrequency}</changefreq>
    <priority>${page.priority}</priority>
  </url>`
  )
  .join('\n')}
</urlset>`;

  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600'
    }
  });
}
