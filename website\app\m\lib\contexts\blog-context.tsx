"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"

export interface BlogPost {
  id?: string
  title: string
  intro: string
  mainContent: string
  categories: string[]
  relatedPosts: string[]
  coverImage?: File
  createdAt?: string
  updatedAt?: string
  author?: string
  status?: "draft" | "published"
}

interface BlogContextType {
  posts: BlogPost[]
  createPost: (postData: Omit<BlogPost, "id">) => Promise<BlogPost>
  updatePost: (id: string, postData: Partial<BlogPost>) => Promise<BlogPost>
  deletePost: (id: string) => Promise<void>
  getPost: (id: string) => BlogPost | undefined
  isLoading: boolean
}

const BlogContext = createContext<BlogContextType | undefined>(undefined)

export function BlogProvider({ children }: { children: React.ReactNode }) {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const createPost = async (postData: Omit<BlogPost, "id">): Promise<BlogPost> => {
    setIsLoading(true)

    try {
      // Create FormData for multipart/form-data
      const formData = new FormData()

      if (postData.coverImage) {
        formData.append("CoverImage", postData.coverImage)
      }

      // Create the exact JSON structure as per API documentation
      const requestBody = {
        title: postData.title,
        intro: postData.intro,
        mainContent: postData.mainContent,
        categories: postData.categories,
        relatedPosts: postData.relatedPosts,
      }

      console.log("Blog POST /api/blog")
      console.log("Content-Type: multipart/form-data")
      console.log("Form Data - CoverImage:", postData.coverImage)
      console.log("Request Body:", JSON.stringify(requestBody, null, 2))

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const newPost: BlogPost = {
        ...postData,
        id: Math.random().toString(36).substr(2, 9),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: "draft",
      }

      setPosts((prev) => [...prev, newPost])
      return newPost
    } finally {
      setIsLoading(false)
    }
  }

  const updatePost = async (id: string, postData: Partial<BlogPost>): Promise<BlogPost> => {
    setIsLoading(true)

    try {
      const existingPost = posts.find((p) => p.id === id)
      if (!existingPost) throw new Error("Post not found")

      const updatedPost = {
        ...existingPost,
        ...postData,
        updatedAt: new Date().toISOString(),
      }

      setPosts((prev) => prev.map((p) => (p.id === id ? updatedPost : p)))
      return updatedPost
    } finally {
      setIsLoading(false)
    }
  }

  const deletePost = async (id: string): Promise<void> => {
    setIsLoading(true)

    try {
      await new Promise((resolve) => setTimeout(resolve, 500))
      setPosts((prev) => prev.filter((p) => p.id !== id))
    } finally {
      setIsLoading(false)
    }
  }

  const getPost = (id: string): BlogPost | undefined => {
    return posts.find((p) => p.id === id)
  }

  return (
    <BlogContext.Provider
      value={{
        posts,
        createPost,
        updatePost,
        deletePost,
        getPost,
        isLoading,
      }}
    >
      {children}
    </BlogContext.Provider>
  )
}

export function useBlog() {
  const context = useContext(BlogContext)
  if (context === undefined) {
    throw new Error("useBlog must be used within a BlogProvider")
  }
  return context
}
