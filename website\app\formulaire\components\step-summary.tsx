"use client";

import React, { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useFormContext } from "./form-context";
import { useOnlineStatus } from "@/app/contexts/online-status-context";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useLanguage } from "@/app/translations/language-context";
import { getPdfBlob } from "../pdf-generator";
import { submitForm } from "../utils/submit-form";
import { FormField } from "./form-field";
import { summarySchema } from "../validation/schemas";
import useFormValidation from "../validation/useFormValidation";
import { hasActiveUploads } from "../utils/file-upload";

interface StepSummaryProps {
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

export function StepSummary({
  onPrevious,
}: StepSummaryProps) {
  const { t } = useLanguage();
  const { formData, updateFormData, resetFormData } = useFormContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [referenceNumber, setReferenceNumber] = useState("");

  // We don't need to store the PDF blob in state anymore
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  const { isOnline } = useOnlineStatus();

  // Initialize form validation
  const {
    validate,
    getFieldError,
    touchField,
    validateField,
  } = useFormValidation(formData, summarySchema);

  // Handle field blur to validate individual fields
  const handleFieldBlur = (fieldName: string) => {
    touchField(fieldName);
    validateField(fieldName);
  };

  // Ref to track if API submission has been done
  const apiSubmissionDoneRef = useRef(false);

  // Function to handle PDF generation - no longer needed as we generate PDF directly in handleSubmit

  const handleSubmit = async () => {
    // Check if any files are currently uploading
    if (hasActiveUploads(formData)) {
      console.log('Cannot submit while files are uploading');
      setSubmissionError('Please wait for all file uploads to complete before submitting');
      return;
    }

    setIsSubmitting(true);
    setSubmissionError(null);

    // Reset the API submission flag
    apiSubmissionDoneRef.current = false;

    // Log the complete form data object for debugging
    console.log('Form data being submitted:', formData);
    console.table(formData); // This provides a more readable table view in the console

    // Validate form data
    const isValid = validate();

    if (!isValid) {
      // Show validation errors to the user
      console.error('Form validation failed');
      setSubmissionError('Validation failed: Please check the form for errors');
      setIsSubmitting(false);
      return;
    }

    try {
      // Use the existing reference number if available, otherwise generate a new one
      // in the format USER-DDMMYY_HHMM
      let referenceNum = formData.referenceNumber;

      if (!referenceNum) {
        // Generate a reference number in the format CAPITALIZED_USER_NAME-ddMMYY_HHmm
        const now = new Date();
        const day = String(now.getDate()).padStart(2, '0');
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const year = String(now.getFullYear()).slice(-2);
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        // Process the name part
        let namePart = 'USER';
        if (formData.fullName && formData.fullName.trim()) {
          const firstName = formData.fullName.split(' ')[0];
          namePart = firstName.toUpperCase().replace(/[^A-Z]/g, '');
        }

        // Create the reference number with underscore instead of colon to avoid file path issues
        referenceNum = `${namePart}-${day}${month}${year}_${hours}${minutes}`;
        console.log('Generated new reference number:', referenceNum);
      }

      setReferenceNumber(referenceNum);

      // Store the reference number in the form data for consistency
      updateFormData({ referenceNumber: referenceNum });

      // Generate the PDF from the form data
      console.log('Generating PDF from form data');
      console.log('Form data before PDF generation:', JSON.stringify(formData, null, 2));
      console.log('uploadedFileUrls before PDF generation:', formData.uploadedFileUrls);

      // Create a deep copy of the form data to ensure we're not losing any properties
      const formDataCopy = JSON.parse(JSON.stringify(formData));

      const pdfBlob = await getPdfBlob(formDataCopy);
      console.log('PDF generated successfully, size:', pdfBlob.size);

      // Generate a filename for the PDF
      const fileName = `charlie-oscar-${referenceNum}.pdf`;

      // Submit the form and upload the PDF to Google Drive
      console.log('Submitting form and uploading PDF');
      console.log('Form data before submission:', JSON.stringify(formData, null, 2));
      console.log('uploadedFileUrls before submission:', formData.uploadedFileUrls);

      const result = await submitForm(
        formDataCopy,
        pdfBlob,
        fileName,
        'application/pdf',
        updateFormData
      );

      if (result.success) {
        console.log('Form submitted successfully with file ID:', result.fileId);

        // Save the reference number before resetting the form
        const submittedReferenceNumber = formData.referenceNumber;

        // Reset the form data to start fresh
        resetFormData();

        // Set the form as submitted and show the confirmation screen
        setIsSubmitted(true);

        // Keep the reference number for display on the success screen
        if (submittedReferenceNumber) {
          setReferenceNumber(submittedReferenceNumber);
        }

        if (result.folderInfo) {
          console.log('Files uploaded to folder:', result.folderInfo.name);
          console.log('Folder can be viewed at:', result.folderInfo.webViewLink);
        }
      } else {
        setSubmissionError(result.error || 'Failed to submit form');
        console.error('Submission error:', result.error);
      }

      // Log the reference number for debugging
      console.log('Form submitted successfully with reference:', referenceNum);
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmissionError('An error occurred while submitting the form');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="space-y-6 py-8 text-center" data-testid="submission-confirmation">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-green-600"
          >
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
            <polyline points="22 4 12 14.01 9 11.01" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold">{t('stepSummary.confirmation.title')}</h2>
        <p className="text-gray-600">
          {t('stepSummary.confirmation.message')}
        </p>
        <div className="bg-blue-50 p-4 rounded-md my-6">
          <p className="font-medium">{t('stepSummary.confirmation.referenceLabel')}</p>
          <p className="text-xl font-bold text-blue-700">{referenceNumber}</p>
          <p className="text-sm text-gray-600 mt-2">
            {t('stepSummary.confirmation.referenceHelp')}
          </p>
        </div>
        {submissionError && (
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md my-4">
            <p className="font-medium">Error:</p>
            <p>{submissionError}</p>
          </div>
        )}

        <div className="flex flex-col items-center justify-center mt-6 space-y-4">
          <p className="text-green-600 font-medium">
            {t('common.success')}
          </p>

          <Button
            onClick={() => {
              // Reset form and redirect to first step
              resetFormData();
              // Redirect to the first step (assuming it's at /formulaire)
              window.location.href = '/formulaire';
            }}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
          >
            {t('stepSummary.confirmation.newFormButton')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8" data-testid="step-summary">
      <h2 className="text-xl font-semibold">{t('stepSummary.title')}</h2>

      <p className="text-sm text-gray-600" data-testid="summary-intro-text">
        {t('stepSummary.intro')}
      </p>

      <Accordion type="single" collapsible className="w-full" data-testid="summary-accordion">
        <AccordionItem value="personal" data-testid="summary-personal-section">
          <AccordionTrigger>{t('stepSummary.sections.personal.title')}</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4 text-sm">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{t('stepSummary.personalInfo.name')}:</p>
                  <p>{formData.fullName || t('common.notProvided')}</p>
                </div>
                <div>
                  <p className="font-medium">{t('stepSummary.personalInfo.gender')}:</p>
                  <p>{formData.gender || t('common.notProvided')}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{t('stepSummary.personalInfo.birthDate')}:</p>
                  <p>{formData.birthDate ? new Date(formData.birthDate).toLocaleDateString() : t('common.notProvided')}</p>
                </div>
                <div>
                  <p className="font-medium">{t('stepSummary.personalInfo.birthPlace')}:</p>
                  <p>{formData.birthPlace || t('common.notProvided')}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{t('stepSummary.personalInfo.nationality')}:</p>
                  <p>{formData.nationality || t('common.notProvided')}</p>
                </div>
                <div>
                  <p className="font-medium">{t('stepSummary.personalInfo.profession')}:</p>
                  <p>{formData.profession || t('common.notProvided')}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{t('stepPersonal.contactInfo.primaryPhone.label')}:</p>
                  <p>{formData.primaryPhone || t('common.notProvided')}</p>
                </div>
                <div>
                  <p className="font-medium">{t('stepPersonal.contactInfo.secondaryPhone.label')}:</p>
                  <p>{formData.secondaryPhone || t('common.notProvided')}</p>
                </div>
              </div>

              <div>
                <p className="font-medium">{t('stepSummary.contactInfo.email')}:</p>
                <p>{formData.email || t('common.notProvided')}</p>
              </div>

              <div>
                <p className="font-medium">{t('stepSummary.contactInfo.address')}:</p>
                <p>{formData.address || t('common.notProvided')}</p>
              </div>

              <div>
                <p className="font-medium">{t('stepPersonal.contactInfo.idDocument.label')}:</p>
                <p>{formData.idDocument ? formData.idDocument.name : t('common.notProvided')}</p>
              </div>

              
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="emergency" data-testid="summary-emergency-section">
          <AccordionTrigger>{t('stepSummary.sections.emergency.title')}</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4 text-sm">
              <div className="p-3 bg-gray-50 rounded-md">
                <h4 className="font-medium mb-2">{t('stepEmergencyProcedure.emergencyContacts.contact1.title')}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="font-medium">{t('stepEmergencyProcedure.emergencyContacts.contact1.name.label')}:</p>
                    <p>{formData.emergencyContact1Name || t('common.notProvided')}</p>
                  </div>
                  <div>
                    <p className="font-medium">{t('stepEmergencyProcedure.emergencyContacts.contact1.phone.label')}:</p>
                    <p>{formData.emergencyContact1Phone || t('common.notProvided')}</p>
                  </div>
                </div>
                <div>
                  <p className="font-medium">{t('stepEmergencyProcedure.emergencyContacts.contact1.relation.label')}:</p>
                  <p>{formData.emergencyContact1Relation || t('common.notProvided')}</p>
                </div>
              </div>

              <div className="p-3 bg-gray-50 rounded-md">
                <h4 className="font-medium mb-2">{t('stepEmergencyProcedure.emergencyContacts.contact2.title')}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="font-medium">{t('stepEmergencyProcedure.emergencyContacts.contact2.name.label')}:</p>
                    <p>{formData.emergencyContact2Name || t('common.notProvided')}</p>
                  </div>
                  <div>
                    <p className="font-medium">{t('stepEmergencyProcedure.emergencyContacts.contact2.phone.label')}:</p>
                    <p>{formData.emergencyContact2Phone || t('common.notProvided')}</p>
                  </div>
                </div>
                <div>
                  <p className="font-medium">{t('stepEmergencyProcedure.emergencyContacts.contact2.relation.label')}:</p>
                  <p>{formData.emergencyContact2Relation || t('common.notProvided')}</p>
                </div>
              </div>

            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="procedure" data-testid="summary-procedure-section">
          <AccordionTrigger>{t('stepSummary.sections.procedure.title')}</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4 text-sm">
              <div>
                <p className="font-medium">{t('stepEmergencyProcedure.landStatus.label')}:</p>
                <p>
                  {formData.landStatus ?
                    (formData.landStatus === 'owner' ? t('stepEmergencyProcedure.landStatus.options.owner') :
                     formData.landStatus === 'heir' ? t('stepEmergencyProcedure.landStatus.options.heir') :
                     formData.landStatus === 'buyer' ? t('stepEmergencyProcedure.landStatus.options.buyer') :
                     formData.landStatus === 'applicant' ? t('stepEmergencyProcedure.landStatus.options.applicant') :
                     formData.landStatus === 'other' ? t('stepEmergencyProcedure.landStatus.options.other') :
                     formData.landStatus) :
                    t('common.notProvided')}
                </p>
                {formData.landStatus === "other" && (
                  <p className="ml-4 mt-1">{formData.otherLandStatus || t('common.notSpecified')}</p>
                )}
              </div>

              <div>
                <p className="font-medium">{t('stepEmergencyProcedure.procedureType.label')}:</p>
                {formData.procedureTypes && formData.procedureTypes.length > 0 ? (
                  <ul className="list-disc ml-5 mt-1">
                    {formData.procedureTypes.map((type, index) => (
                      <li key={index}>{type}</li>
                    ))}
                  </ul>
                ) : (
                  <p>{t('common.notProvided')}</p>
                )}

                {formData.procedureTypes?.includes("Autre") && (
                  <div className="mt-2">
                    <p className="font-medium">{t('stepEmergencyProcedure.procedureType.otherLabel')}:</p>
                    <p>{formData.otherProcedureType || t('common.notSpecified')}</p>
                  </div>
                )}
              </div>

              <div>
                <p className="font-medium">{t('stepEmergencyProcedure.additionalInfo.label')}:</p>
                <p>{formData.additionalInfo || t('common.notProvided')}</p>
              </div>

            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="documents" data-testid="summary-documents-section">
          <AccordionTrigger>{t('stepSummary.sections.documents.title')}</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4 text-sm">
              <div>
                <p className="font-medium">{t('stepDocumentsLocation.documents.availableDocs.label')}:</p>
                {formData.availableDocuments && formData.availableDocuments.length > 0 ? (
                  <ul className="list-disc ml-5 mt-1">
                    {formData.availableDocuments.map((doc, index) => (
                      <li key={index}>{doc}</li>
                    ))}
                  </ul>
                ) : (
                  <p>{t('common.notProvided')}</p>
                )}
              </div>

              <div>
                <p className="font-medium">{t('stepDocumentsLocation.documents.upload.label')}:</p>
                {formData.uploadedDocuments && formData.uploadedDocuments.length > 0 ? (
                  <ul className="list-disc ml-5 mt-1">
                    {formData.uploadedDocuments.map((doc, index) => (
                      <li key={index}>{doc.name}</li>
                    ))}
                  </ul>
                ) : (
                  <p>{t('stepSummary.documents.none')}</p>
                )}
              </div>

              <div>
                <p className="font-medium">{t('stepDocumentsLocation.documents.details.label')}:</p>
                <p>{formData.documentsDetails || t('common.notProvided')}</p>
              </div>

            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="location" data-testid="summary-location-section">
          <AccordionTrigger>{t('stepSummary.sections.location.title')}</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4 text-sm">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{t('stepDocumentsLocation.location.zoneType.label')}:</p>
                  <p>{formData.zoneType || t('common.notProvided')}</p>
                </div>
                <div>
                  <p className="font-medium">{t('stepSummary.locationInfo.region')}:</p>
                  <p>{formData.region || t('common.notProvided')}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{t('stepSummary.locationInfo.department')}:</p>
                  <p>{formData.department || t('common.notProvided')}</p>
                </div>
                <div>
                  <p className="font-medium">{t('stepSummary.locationInfo.subdivision')}:</p>
                  <p>{formData.subdivision || t('common.notProvided')}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{t('stepSummary.locationInfo.neighborhood')}:</p>
                  <p>{formData.neighborhood || t('common.notProvided')}</p>
                </div>
                <div>
                  <p className="font-medium">{t('stepDocumentsLocation.location.locationDetails.label')}:</p>
                  <p>{formData.locationDetails || t('common.notProvided')}</p>
                </div>
              </div>

              <div>
                <p className="font-medium">{t('stepSummary.locationInfo.area')}:</p>
                <p>{formData.area ? `${formData.area} m²` : t('common.notProvided')}</p>
              </div>

            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <div className="space-y-4">
        <div className="space-y-2">
          <FormField
            id="additionalComments"
            label={t('stepSummary.additionalComments.label')}
            value={formData.additionalComments || ""}
            onChange={(value) => updateFormData({ additionalComments: value })}
            onBlur={() => handleFieldBlur('additionalComments')}
            placeholder={t('stepSummary.additionalComments.placeholder')}
            multiline={true}
            error={getFieldError('additionalComments')}
            data-testid="additional-comments-textarea"
          />
          <p className="text-xs text-gray-500">
            {t('stepSummary.additionalComments.help')}
          </p>
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-md">
        <p className="text-sm text-gray-600 mb-4" data-testid="final-data-protection-text">
          {t('stepSummary.finalConsent.dataProtection')}
        </p>
        <div className="flex items-start space-x-2">
          <Checkbox
            id="finalConsent"
            checked={formData.finalConsent}
            onCheckedChange={(checked) => {
              updateFormData({ finalConsent: checked as boolean });
              touchField('finalConsent');
              validateField('finalConsent');
            }}
            data-testid="final-consent-checkbox"
            className={`h-5 w-5 border-4 ${
              getFieldError('finalConsent')
                ? "border-red-500 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                : "border-input data-[state=checked]:bg-primary data-[state=checked]:border-primary"
            }`}
          />
          <div className="grid gap-1.5 leading-none">
            <label
              htmlFor="finalConsent"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {t('stepSummary.finalConsent.label')}{t('common.required')}
            </label>
            {getFieldError('finalConsent') && (
              <div className="text-red-500 text-sm flex items-start mt-1" role="alert">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <span>{
                  /*@ts-ignore */
                    t(getFieldError('finalConsent'))
                }</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-4">
        <Button
          variant="outline"
          onClick={onPrevious}
          data-testid="summary-prev-button"
          className="border-primary text-primary hover:bg-primary/10"
        >
          {t('common.previous')}
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.finalConsent || !isOnline || hasActiveUploads(formData)}
          data-testid="submit-form-button"
          className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
        >
          {isSubmitting ? t('common.processing') : hasActiveUploads(formData) ? t('common.waitForUploads') : t('common.submit')}
        </Button>
      </div>
    </div>
  );
}
