"use client"

import { useState, useEffect } from "react"
import { useRouter, useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Save, Plus, X, Star } from "lucide-react"
import Link from "next/link"
import { useFoncier } from "@/app/m/lib/contexts/foncier-context"

interface Prestation {
  title: string
  description: string
}

interface Advantage {
  title: string
  description: string
}

interface WhenToUse {
  title: string
  description: string
}

interface Testimonial {
  name: string
  role: string
  location: string
  content: string
  rating: number
}

interface FAQ {
  question: string
  answer: string
}

export default function EditFoncierServicePage() {
  const router = useRouter()
  const params = useParams()
  const { getService, updateService, isLoading } = useFoncier()

  const serviceId = params.id as string
  const existingService = getService(serviceId)

  // Basic form state
  const [title, setTitle] = useState("")
  const [shortDescription, setShortDescription] = useState("")
  const [detailedDescription, setDetailedDescription] = useState("")
  const [category, setCategory] = useState("")
  const [estimatedDuration, setEstimatedDuration] = useState("")
  const [complexity, setComplexity] = useState<"Simple" | "Modéré" | "Complexe" | "">("")
  const [featured, setFeatured] = useState(false)

  // Complex arrays state
  const [prestations, setPrestations] = useState<Prestation[]>([])
  const [advantages, setAdvantages] = useState<Advantage[]>([])
  const [whenToUse, setWhenToUse] = useState<WhenToUse[]>([])
  const [testimonials, setTestimonials] = useState<Testimonial[]>([])
  const [faq, setFaq] = useState<FAQ[]>([])

  // Load existing service data
  useEffect(() => {
    if (existingService) {
      setTitle(existingService.title)
      setShortDescription(existingService.shortDescription)
      setDetailedDescription(existingService.detailedDescription)
      setCategory(existingService.category)
      setEstimatedDuration(existingService.estimatedDuration)
      setComplexity(existingService.complexity)
      setFeatured(existingService.featured)
      setPrestations(existingService.prestations)
      setAdvantages(existingService.advantages)
      setWhenToUse(existingService.whenToUse)
      setTestimonials(existingService.testimonials)
      setFaq(existingService.faq)
    }
  }, [existingService])

  // Redirect if service not found
  if (!existingService) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Service Not Found</h2>
            <p className="text-muted-foreground">The foncier service you're looking for doesn't exist.</p>
            <Button asChild className="mt-4">
              <Link href="/m/dashboard/cms/services/foncier">Back to Services</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Prestation handlers
  const addPrestation = () => {
    setPrestations([...prestations, { title: "", description: "" }])
  }

  const updatePrestation = (index: number, field: keyof Prestation, value: string) => {
    const updated = prestations.map((item, i) => (i === index ? { ...item, [field]: value } : item))
    setPrestations(updated)
  }

  const removePrestation = (index: number) => {
    setPrestations(prestations.filter((_, i) => i !== index))
  }

  // Advantage handlers
  const addAdvantage = () => {
    setAdvantages([...advantages, { title: "", description: "" }])
  }

  const updateAdvantage = (index: number, field: keyof Advantage, value: string) => {
    const updated = advantages.map((item, i) => (i === index ? { ...item, [field]: value } : item))
    setAdvantages(updated)
  }

  const removeAdvantage = (index: number) => {
    setAdvantages(advantages.filter((_, i) => i !== index))
  }

  // When to use handlers
  const addWhenToUse = () => {
    setWhenToUse([...whenToUse, { title: "", description: "" }])
  }

  const updateWhenToUse = (index: number, field: keyof WhenToUse, value: string) => {
    const updated = whenToUse.map((item, i) => (i === index ? { ...item, [field]: value } : item))
    setWhenToUse(updated)
  }

  const removeWhenToUse = (index: number) => {
    setWhenToUse(whenToUse.filter((_, i) => i !== index))
  }

  // Testimonial handlers
  const addTestimonial = () => {
    setTestimonials([...testimonials, { name: "", role: "", location: "", content: "", rating: 5 }])
  }

  const updateTestimonial = (index: number, field: keyof Testimonial, value: string | number) => {
    const updated = testimonials.map((item, i) => (i === index ? { ...item, [field]: value } : item))
    setTestimonials(updated)
  }

  const removeTestimonial = (index: number) => {
    setTestimonials(testimonials.filter((_, i) => i !== index))
  }

  // FAQ handlers
  const addFAQ = () => {
    setFaq([...faq, { question: "", answer: "" }])
  }

  const updateFAQ = (index: number, field: keyof FAQ, value: string) => {
    const updated = faq.map((item, i) => (i === index ? { ...item, [field]: value } : item))
    setFaq(updated)
  }

  const removeFAQ = (index: number) => {
    setFaq(faq.filter((_, i) => i !== index))
  }

  const handleSave = async () => {
    try {
      await updateService(serviceId, {
        title,
        shortDescription,
        detailedDescription,
        category,
        estimatedDuration,
        complexity: complexity as "Simple" | "Modéré" | "Complexe",
        featured,
        prestations,
        advantages,
        whenToUse,
        testimonials,
        faq,
      })

      router.push("/m/dashboard/cms/services/foncier")
    } catch (error) {
      console.error("Error updating foncier service:", error)
      alert("Error updating service. Please try again.")
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/m/dashboard/cms/services/foncier">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Services
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Foncier Service</h1>
          <p className="text-muted-foreground">Update your real estate service offering</p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Update essential details about your service</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Service Title</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter service title..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="shortDescription">Short Description</Label>
                <Textarea
                  id="shortDescription"
                  value={shortDescription}
                  onChange={(e) => setShortDescription(e.target.value)}
                  placeholder="Brief description of the service..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="detailedDescription">Detailed Description</Label>
                <Textarea
                  id="detailedDescription"
                  value={detailedDescription}
                  onChange={(e) => setDetailedDescription(e.target.value)}
                  placeholder="Comprehensive description of the service..."
                  rows={8}
                  className="min-h-[200px] resize-y"
                />
              </div>
            </CardContent>
          </Card>

          {/* Prestations */}
          <Card>
            <CardHeader>
              <CardTitle>Prestations</CardTitle>
              <CardDescription>Update services and deliverables included</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {prestations.map((prestation, index) => (
                <div key={index} className="p-4 border rounded-md space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Prestation {index + 1}</h4>
                    <Button variant="ghost" size="sm" onClick={() => removePrestation(index)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <Input
                      value={prestation.title}
                      onChange={(e) => updatePrestation(index, "title", e.target.value)}
                      placeholder="Prestation title..."
                    />
                    <Textarea
                      value={prestation.description}
                      onChange={(e) => updatePrestation(index, "description", e.target.value)}
                      placeholder="Prestation description..."
                      rows={2}
                    />
                  </div>
                </div>
              ))}
              <Button onClick={addPrestation} variant="outline" className="w-full bg-transparent">
                <Plus className="mr-2 h-4 w-4" />
                Add Prestation
              </Button>
            </CardContent>
          </Card>

          {/* Advantages */}
          <Card>
            <CardHeader>
              <CardTitle>Advantages</CardTitle>
              <CardDescription>Update key benefits and advantages of this service</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {advantages.map((advantage, index) => (
                <div key={index} className="p-4 border rounded-md space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Advantage {index + 1}</h4>
                    <Button variant="ghost" size="sm" onClick={() => removeAdvantage(index)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <Input
                      value={advantage.title}
                      onChange={(e) => updateAdvantage(index, "title", e.target.value)}
                      placeholder="Advantage title..."
                    />
                    <Textarea
                      value={advantage.description}
                      onChange={(e) => updateAdvantage(index, "description", e.target.value)}
                      placeholder="Advantage description..."
                      rows={2}
                    />
                  </div>
                </div>
              ))}
              <Button onClick={addAdvantage} variant="outline" className="w-full bg-transparent">
                <Plus className="mr-2 h-4 w-4" />
                Add Advantage
              </Button>
            </CardContent>
          </Card>

          {/* When to Use */}
          <Card>
            <CardHeader>
              <CardTitle>When to Use</CardTitle>
              <CardDescription>Update scenarios when this service is recommended</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {whenToUse.map((item, index) => (
                <div key={index} className="p-4 border rounded-md space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Scenario {index + 1}</h4>
                    <Button variant="ghost" size="sm" onClick={() => removeWhenToUse(index)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <Input
                      value={item.title}
                      onChange={(e) => updateWhenToUse(index, "title", e.target.value)}
                      placeholder="Scenario title..."
                    />
                    <Textarea
                      value={item.description}
                      onChange={(e) => updateWhenToUse(index, "description", e.target.value)}
                      placeholder="Scenario description..."
                      rows={2}
                    />
                  </div>
                </div>
              ))}
              <Button onClick={addWhenToUse} variant="outline" className="w-full bg-transparent">
                <Plus className="mr-2 h-4 w-4" />
                Add Scenario
              </Button>
            </CardContent>
          </Card>

          {/* Testimonials */}
          <Card>
            <CardHeader>
              <CardTitle>Testimonials</CardTitle>
              <CardDescription>Update client testimonials and reviews</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {testimonials.map((testimonial, index) => (
                <div key={index} className="p-4 border rounded-md space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Testimonial {index + 1}</h4>
                    <Button variant="ghost" size="sm" onClick={() => removeTestimonial(index)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      value={testimonial.name}
                      onChange={(e) => updateTestimonial(index, "name", e.target.value)}
                      placeholder="Client name..."
                    />
                    <Input
                      value={testimonial.role}
                      onChange={(e) => updateTestimonial(index, "role", e.target.value)}
                      placeholder="Client role..."
                    />
                  </div>
                  <Input
                    value={testimonial.location}
                    onChange={(e) => updateTestimonial(index, "location", e.target.value)}
                    placeholder="Location..."
                  />
                  <Textarea
                    value={testimonial.content}
                    onChange={(e) => updateTestimonial(index, "content", e.target.value)}
                    placeholder="Testimonial content..."
                    rows={3}
                  />
                  <div className="flex items-center gap-2">
                    <Label>Rating:</Label>
                    <div className="flex gap-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-5 w-5 cursor-pointer ${
                            star <= testimonial.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                          }`}
                          onClick={() => updateTestimonial(index, "rating", star)}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              ))}
              <Button onClick={addTestimonial} variant="outline" className="w-full bg-transparent">
                <Plus className="mr-2 h-4 w-4" />
                Add Testimonial
              </Button>
            </CardContent>
          </Card>

          {/* FAQ */}
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>Update common questions and answers about this service</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {faq.map((item, index) => (
                <div key={index} className="p-4 border rounded-md space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">FAQ {index + 1}</h4>
                    <Button variant="ghost" size="sm" onClick={() => removeFAQ(index)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <Input
                      value={item.question}
                      onChange={(e) => updateFAQ(index, "question", e.target.value)}
                      placeholder="Question..."
                    />
                    <Textarea
                      value={item.answer}
                      onChange={(e) => updateFAQ(index, "answer", e.target.value)}
                      placeholder="Answer..."
                      rows={3}
                    />
                  </div>
                </div>
              ))}
              <Button onClick={addFAQ} variant="outline" className="w-full bg-transparent">
                <Plus className="mr-2 h-4 w-4" />
                Add FAQ
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          {/* Service Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Service Settings</CardTitle>
              <CardDescription>Update service details and properties</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Valuation">Valuation</SelectItem>
                    <SelectItem value="Survey">Survey</SelectItem>
                    <SelectItem value="Consultation">Consultation</SelectItem>
                    <SelectItem value="Legal">Legal</SelectItem>
                    <SelectItem value="Management">Management</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="estimatedDuration">Estimated Duration</Label>
                <Input
                  id="estimatedDuration"
                  value={estimatedDuration}
                  onChange={(e) => setEstimatedDuration(e.target.value)}
                  placeholder="e.g., 3-5 business days"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="complexity">Complexity</Label>
                <Select
                  value={complexity}
                  onValueChange={(value: "Simple" | "Modéré" | "Complexe") => setComplexity(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select complexity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Simple">Simple</SelectItem>
                    <SelectItem value="Modéré">Modéré</SelectItem>
                    <SelectItem value="Complexe">Complexe</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="featured"
                  checked={featured}
                  onCheckedChange={(checked) => setFeatured(checked as boolean)}
                />
                <Label htmlFor="featured">Featured Service</Label>
              </div>
            </CardContent>
          </Card>

          <div className="flex gap-2">
            <Button onClick={handleSave} className="flex-1" disabled={isLoading}>
              <Save className="mr-2 h-4 w-4" />
              {isLoading ? "Updating..." : "Update Service"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
