"use client";

import { Check } from "lucide-react";
import { useLanguage } from "@/app/translations/language-context";
interface Step {
  id: string;
  name: string;
  description: string;
}

interface StepSidebarProps {
  steps: Step[];
  currentStep: number;
  goToStep: (step: number) => void;
}

export function StepSidebar({ steps, currentStep, goToStep }: StepSidebarProps) {
  const { t } = useLanguage();

  return (
    <div className="w-full md:w-80 bg-sidebar text-sidebar-foreground p-6 md:p-8">

      <div className="space-y-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold">{t('sidebar.step')} {currentStep + 1}</h2>
          <p className="text-sm text-sidebar-accent-foreground mt-1">
            {steps[currentStep].description}
          </p>
        </div>

        <div className="relative">
          {/* Vertical line */}
            <div
            className="
              absolute
              md:left-4 md:top-4 md:bottom-4 md:w-0.5 md:h-auto md:bg-primary md:-translate-x-1/2
              left-4 top-1/2 right-4 h-0.5 w-auto bg-primary -translate-y-2
              md:translate-y-0
              md:block
              "
            style={{
              // Show vertical on md+, horizontal on small screens
              display: 'block',
            }}
            ></div>

          {/* Steps */}
          <div className="space-y-6 flex flex-row justify-between md:flex-col">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className="flex items-start space-x-3 cursor-pointer relative"
                onClick={() => index <= currentStep && goToStep(index)}
              >
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 flex-shrink-0 mt-0.5 z-10
                    ${index < currentStep
                      ? "bg-primary border-primary text-white"
                      : index === currentStep
                        ? " border-primary border-2  bg-white mt-0.5  text-black"
                        : " border-white border-2  bg-white mt-0.5  text-black"
                    }`}
                >
                  {index < currentStep ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                <div>
                  <p
                    className={`hidden md:flex font-medium ${
                      index <= currentStep ? "text-black" : "text-gray-500"
                    }`}
                  >
                    {step.name}
                  </p>
                  {index === currentStep && (
                    <p className="hidden md:flex text-sm text-primary font-medium mt-1">
                      {t('sidebar.currentStep')}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
