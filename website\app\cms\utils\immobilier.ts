import { immobilierData } from '../data/immobilier';

/**
 * Récupère toutes les données de la section immobilier
 * @returns Données complètes de la section immobilier
 */
export function getImmobilierContent() {
  return immobilierData;
}

/**
 * Récupère les métadonnées de la page immobilier
 * @returns Métadonnées pour le SEO
 */
export function getImmobilierMetadata() {
  return immobilierData.metadata;
}

/**
 * Récupère les données de la section hero
 * @returns Données de la section hero
 */
export function getImmobilierHero() {
  return immobilierData.hero;
}

/**
 * Récupère les données de la section à propos
 * @returns Données de la section à propos
 */
export function getImmobilierAbout() {
  return immobilierData.about;
}

/**
 * Récupère la liste des services immobiliers
 * @returns Liste des services avec leurs détails
 */
export function getImmobilierServices() {
  return immobilierData.services;
}

/**
 * Récupère un service spécifique par son ID
 * @param serviceId - L'ID du service à récupérer
 * @returns Le service correspondant ou undefined
 */
export function getImmobilierServiceById(serviceId: string) {
  return immobilierData.services.items.find(service => service.id === serviceId);
}

/**
 * Récupère les avantages de Charlie Oscar Consulting
 * @returns Liste des avantages
 */
export function getImmobilierAdvantages() {
  return immobilierData.advantages;
}

/**
 * Récupère les données de la section CTA
 * @returns Données de la section call-to-action
 */
export function getImmobilierCTA() {
  return immobilierData.cta;
}

/**
 * Récupère tous les services avec pagination
 * @param page - Numéro de page (commence à 1)
 * @param limit - Nombre d'éléments par page
 * @returns Services paginés
 */
export function getImmobilierServicesPaginated(page: number = 1, limit: number = 6) {
  const services = immobilierData.services.items;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  return {
    services: services.slice(startIndex, endIndex),
    totalServices: services.length,
    currentPage: page,
    totalPages: Math.ceil(services.length / limit),
    hasNextPage: endIndex < services.length,
    hasPrevPage: page > 1
  };
}

/**
 * Recherche des services par terme
 * @param searchTerm - Terme de recherche
 * @returns Services correspondant au terme de recherche
 */
export function searchImmobilierServices(searchTerm: string) {
  const services = immobilierData.services.items;
  const term = searchTerm.toLowerCase();
  
  return services.filter(service => 
    service.title.toLowerCase().includes(term) ||
    service.description.toLowerCase().includes(term)
  );
}

/**
 * Récupère les services par catégorie/couleur
 * @param color - Couleur/catégorie du service
 * @returns Services de la catégorie spécifiée
 */
export function getImmobilierServicesByCategory(color: string) {
  return immobilierData.services.items.filter(service => service.color === color);
}

/**
 * Récupère tous les services détaillés pour la page services
 * @returns Liste complète des services détaillés
 */
export function getDetailedImmobilierServices() {
  return immobilierData.detailedServices;
}

/**
 * Récupère un service détaillé par son ID
 * @param serviceId - L'ID du service à récupérer
 * @returns Le service détaillé correspondant ou undefined
 */
export function getDetailedImmobilierServiceById(serviceId: string) {
  return immobilierData.detailedServices.find(service => service.id === serviceId);
}
