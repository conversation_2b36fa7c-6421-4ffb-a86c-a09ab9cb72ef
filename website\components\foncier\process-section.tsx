"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Search, MapPin, FileText, Award, ArrowRight, CheckCircle, Clock } from "lucide-react";
import { getFoncierContent } from "@/app/cms/utils/foncier";

export function ProcessSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeStep, setActiveStep] = useState(1);
  const sectionRef = useRef<HTMLElement>(null);
  const processContent = getFoncierContent().process;

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep(prev => prev === 4 ? 1 : prev + 1);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const iconMap = { Search, MapPin, FileText, Award };
  const steps = processContent?.steps?.map((step: any) => ({
    ...step,
    icon: iconMap[step.icon] || FileText
  })) || [];

  return (
    <section ref={sectionRef} id="processus" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
              Notre méthode
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {processContent?.title || "Comment ça marche ?"}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {processContent?.subtitle || "Notre processus éprouvé en 4 étapes vous garantit l'obtention de votre titre foncier dans les meilleures conditions."}
            </p>
          </div>

          {/* Timeline */}
          <div className={`mb-16 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            {/* Desktop Timeline */}
            <div className="hidden lg:block">
              <div className="relative ">
                {/* Timeline Line */}
                <div className="absolute top-1/2 my-5 left-0 right-0 h-1 bg-gray-200 transform -translate-y-1/2"></div>
                <div 
                  className="absolute top-1/2 left-0 my-5 h-1 bg-primary transform -translate-y-1/2 transition-all duration-1000"
                  style={{ width: `${(activeStep / 4) * 100}%` }}
                ></div>

                {/* Steps */}
                <div className="relative flex justify-between">
                  {steps.map((step, index) => {
                    const Icon = step.icon;
                    const isActive = activeStep >= step.id;
                    const isCurrent = activeStep === step.id;
                    
                    return (
                      <div
                        key={step.id}
                        className="flex flex-col items-center cursor-pointer group"
                        onClick={() => setActiveStep(step.id)}
                      >
                        {/* Step Circle */}
                        <div className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 ${
                          isActive 
                            ? 'bg-primary text-white shadow-lg scale-110' 
                            : 'bg-white border-2 border-gray-300 text-gray-400 group-hover:border-primary group-hover:text-primary'
                        }`}>
                          <Icon className="w-7 h-7" />
                        </div>

                        {/* Step Info */}
                        <div className="mt-4 text-center max-w-xs">
                          <h3 className={`font-bold text-md transition-colors duration-300 ${
                            isCurrent ? 'text-primary' : 'text-gray-900'
                          }`}>
                            {step.title}
                          </h3>
                          <p className="text-sm text-gray-600 mt-2 leading-relaxed">
                            {step.description.slice(0, 100)}{step.description.length > 100 ? '...' : ''}
                          </p>
                          {/* <div className="flex items-center justify-center mt-2 text-xs text-gray-500">
                            <Clock className="w-3 h-3 mr-1" />
                            {step.duration}
                          </div> */}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Mobile Timeline */}
            <div className="lg:hidden space-y-6">
              {steps.map((step, index) => {
                const Icon = step.icon;
                const isActive = activeStep >= step.id;
                const isCurrent = activeStep === step.id;
                
                return (
                  <div
                    key={step.id}
                    className={`relative transition-all duration-300 ${
                      isCurrent ? 'scale-105' : ''
                    }`}
                  >
                    <Card className={`border-2 ${isCurrent ? step.borderColor : 'border-gray-100'}`}>
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 ${
                            isActive ? 'bg-primary text-white' : 'bg-gray-100 text-gray-400'
                          }`}>
                            <Icon className="w-6 h-6" />
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <h3 className={`font-bold text-lg ${isCurrent ? 'text-primary' : 'text-gray-900'}`}>
                                {step.title}
                              </h3>
                              {/* <span className="text-xs text-gray-500 flex items-center">
                                <Clock className="w-3 h-3 mr-1" />
                                {step.duration}
                              </span> */}
                            </div>
                            <p className="text-gray-600 text-sm leading-relaxed">
                              {step.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Connection Line */}
                    {index < steps.length - 1 && (
                      <div className="flex justify-center">
                        <div className={`w-1 h-6 ${isActive ? 'bg-primary' : 'bg-gray-200'}`}></div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Active Step Details */}
          <div className={`transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            {steps.map((step) => {
              if (step.id !== activeStep) return null;
              
              return (
                <Card key={step.id} className={`border-2 ${step.borderColor} ${step.bgColor}`}>
                  <CardContent className="p-8">
                    <div className="grid md:grid-cols-2 gap-8 items-center">
                      <div className="space-y-6">
                        <div className="space-y-3">
                          <div className="flex items-center space-x-3">
                            <div className={`w-12 h-12 bg-white rounded-lg flex items-center justify-center`}>
                              <step.icon className={`w-6 h-6 ${step.color}`} />
                            </div>
                            <div>
                              <h3 className="text-3xl font-bold text-gray-900">
                                Étape {step.id}: {step.title}
                              </h3>
                              {/* <div className="flex items-center text-sm text-gray-600">
                                <Clock className="w-4 h-4 mr-1" />
                                Durée: {step.duration}
                              </div> */}
                            </div>
                          </div>
                          
                          <p className="text-gray-700 leading-relaxed">
                            {step.description}
                          </p>
                        </div>

                        <div className="space-y-3">
                          <h4 className="font-semibold text-gray-900">Actions réalisées :</h4>
                          <ul className="space-y-2">
                            {step.details.map((detail, detailIndex) => (
                              <li key={detailIndex} className="flex items-start space-x-3 text-sm text-gray-700">
                                <CheckCircle className={`w-4 h-4 ${step.color} flex-shrink-0 mt-0.5`} />
                                <span>{detail}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="w-48 h-48 bg-white/50 rounded-full flex items-center justify-center mx-auto mb-6">
                          <step.icon className={`w-24 h-24 ${step.color}`} />
                        </div>
                        <p className="text-sm text-gray-600">
                          Étape {step.id} sur 4
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* CTA Section */}
          <div className={`text-center mt-16 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="bg-gray-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Prêt à commencer votre démarche ?
              </h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Nos experts vous accompagnent à chaque étape pour garantir 
                le succès de votre projet foncier.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="group">
                  <Link href="/formulaire" className="flex items-center">
                    Commencer maintenant
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                {/* <Button asChild variant="outline" size="lg">
                  <Link href="#ressources">
                    Télécharger le guide
                  </Link>
                </Button> */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
