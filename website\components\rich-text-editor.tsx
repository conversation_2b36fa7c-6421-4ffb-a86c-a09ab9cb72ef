"use client"

import { use<PERSON><PERSON>back, useMem<PERSON>, useState, useEffect } from "react"
import { createEditor, type Descendant, Editor, Element as SlateElement, Transforms } from "slate"
import { Slate, Editable, withReact, useSlate, type ReactEditor } from "slate-react"
import { withHistory } from "slate-history"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Quote,
  Heading1,
  Heading2,
  Heading3,
} from "lucide-react"
import { cn } from "@/lib/utils"

/* -------------------------------------------------------------------------- */
/*                                  Typings                                   */
/* -------------------------------------------------------------------------- */

type CustomElement = {
  type:
    | "paragraph"
    | "heading-one"
    | "heading-two"
    | "heading-three"
    | "block-quote"
    | "bulleted-list"
    | "numbered-list"
    | "list-item"
  align?: "left" | "center" | "right"
  children: CustomText[]
}

type CustomText = {
  text: string
  bold?: boolean
  italic?: boolean
  underline?: boolean
}

declare module "slate" {
  interface CustomTypes {
    Editor: ReactEditor
    Element: CustomElement
    Text: CustomText
  }
}

/* -------------------------------------------------------------------------- */
/*                              Helper constants                              */
/* -------------------------------------------------------------------------- */

const DEFAULT_VALUE: Descendant[] = [{ type: "paragraph", children: [{ text: "" }] }]

const LIST_TYPES = ["numbered-list", "bulleted-list"]
const TEXT_ALIGN_TYPES = ["left", "center", "right"]

/* -------------------------------------------------------------------------- */
/*                              Public component                              */
/* -------------------------------------------------------------------------- */

interface RichTextEditorProps {
  value?: Descendant[]
  onChange: (value: Descendant[]) => void
  placeholder?: string
}

export function RichTextEditor({ value, onChange, placeholder = "Start writing..." }: RichTextEditorProps) {
  /* -------------------------------- editor -------------------------------- */
  const editor = useMemo(() => withHistory(withReact(createEditor())), [])

  /* ------------------------------ local state ----------------------------- */
  const [internalValue, setInternalValue] = useState<Descendant[]>(() => {
    // Ensure we always have a valid initial value
    if (value && Array.isArray(value) && value.length > 0) {
      return value
    }
    return DEFAULT_VALUE
  })

  /* --------------------------- sync external value ------------------------ */
  useEffect(() => {
    if (value && Array.isArray(value) && value.length > 0) {
      setInternalValue(value)
    }
  }, [value])

  /* ----------------------------- render helpers --------------------------- */
  const renderElement = useCallback((props: any) => <Element {...props} />, [])
  const renderLeaf = useCallback((props: any) => <Leaf {...props} />, [])

  const handleChange = useCallback(
    (newValue: Descendant[]) => {
      setInternalValue(newValue)
      onChange(newValue)
    },
    [onChange],
  )

  return (
    <div className="border rounded-md">
      <Slate editor={editor} value={internalValue} onChange={handleChange}>
        <Toolbar />
        <div className="p-4">
          <Editable
            renderElement={renderElement}
            renderLeaf={renderLeaf}
            placeholder={placeholder}
            spellCheck
            className="min-h-[200px] outline-none focus:outline-none"
            style={{ minHeight: "200px" }}
          />
        </div>
      </Slate>
    </div>
  )
}

/* -------------------------------------------------------------------------- */
/*                               Editor logic                                 */
/* -------------------------------------------------------------------------- */

const toggleBlock = (editor: Editor, format: string) => {
  const isActive = isBlockActive(editor, format, TEXT_ALIGN_TYPES.includes(format) ? "align" : "type")
  const isList = LIST_TYPES.includes(format)

  Transforms.unwrapNodes(editor, {
    match: (n) =>
      !Editor.isEditor(n) &&
      SlateElement.isElement(n) &&
      LIST_TYPES.includes(n.type) &&
      !TEXT_ALIGN_TYPES.includes(format),
    split: true,
  })

  const newProperties: Partial<SlateElement> = TEXT_ALIGN_TYPES.includes(format)
    ? { align: isActive ? undefined : (format as any) }
    : { type: isActive ? "paragraph" : isList ? "list-item" : (format as any) }

  Transforms.setNodes<SlateElement>(editor, newProperties)

  if (!isActive && isList) {
    const block = { type: format as any, children: [] }
    Transforms.wrapNodes(editor, block)
  }
}

const toggleMark = (editor: Editor, format: string) => {
  const isActive = isMarkActive(editor, format)
  if (isActive) Editor.removeMark(editor, format)
  else Editor.addMark(editor, format, true)
}

const isBlockActive = (editor: Editor, format: string, attr = "type") => {
  const { selection } = editor
  if (!selection) return false
  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: (n) => !Editor.isEditor(n) && SlateElement.isElement(n) && (n as any)[attr] === format,
    }),
  )
  return !!match
}

const isMarkActive = (editor: Editor, format: string) => {
  const marks = Editor.marks(editor) as any
  return marks ? marks[format] === true : false
}

/* -------------------------------------------------------------------------- */
/*                        Renderers (Element & Leaf)                          */
/* -------------------------------------------------------------------------- */

const Element = ({ attributes, children, element }: any) => {
  const style = { textAlign: element.align }
  switch (element.type) {
    case "heading-one":
      return (
        <h1 style={style} {...attributes} className="text-3xl font-bold mb-4">
          {children}
        </h1>
      )
    case "heading-two":
      return (
        <h2 style={style} {...attributes} className="text-2xl font-semibold mb-3">
          {children}
        </h2>
      )
    case "heading-three":
      return (
        <h3 style={style} {...attributes} className="text-xl font-medium mb-2">
          {children}
        </h3>
      )
    case "block-quote":
      return (
        <blockquote style={style} {...attributes} className="border-l-4 border-gray-300 pl-4 italic my-4">
          {children}
        </blockquote>
      )
    case "bulleted-list":
      return (
        <ul style={style} {...attributes} className="list-disc list-inside my-4 space-y-1">
          {children}
        </ul>
      )
    case "numbered-list":
      return (
        <ol style={style} {...attributes} className="list-decimal list-inside my-4 space-y-1">
          {children}
        </ol>
      )
    case "list-item":
      return (
        <li style={style} {...attributes} className="ml-4">
          {children}
        </li>
      )
    default:
      return (
        <p style={style} {...attributes} className="mb-2">
          {children}
        </p>
      )
  }
}

const Leaf = ({ attributes, children, leaf }: any) => {
  if (leaf.bold) children = <strong>{children}</strong>
  if (leaf.italic) children = <em>{children}</em>
  if (leaf.underline) children = <u>{children}</u>
  return <span {...attributes}>{children}</span>
}

/* -------------------------------------------------------------------------- */
/*                                  Toolbar                                   */
/* -------------------------------------------------------------------------- */

const Toolbar = () => (
  <div className="border-b p-2 flex flex-wrap items-center gap-1">
    <MarkButton format="bold" icon={Bold} />
    <MarkButton format="italic" icon={Italic} />
    <MarkButton format="underline" icon={Underline} />
    <Separator orientation="vertical" className="h-6" />
    <BlockButton format="heading-one" icon={Heading1} />
    <BlockButton format="heading-two" icon={Heading2} />
    <BlockButton format="heading-three" icon={Heading3} />
    <Separator orientation="vertical" className="h-6" />
    <BlockButton format="block-quote" icon={Quote} />
    <BlockButton format="numbered-list" icon={ListOrdered} />
    <BlockButton format="bulleted-list" icon={List} />
    <Separator orientation="vertical" className="h-6" />
    <BlockButton format="left" icon={AlignLeft} />
    <BlockButton format="center" icon={AlignCenter} />
    <BlockButton format="right" icon={AlignRight} />
  </div>
)

const BlockButton = ({ format, icon: Icon }: { format: string; icon: any }) => {
  const editor = useSlate()
  const active = isBlockActive(editor, format, TEXT_ALIGN_TYPES.includes(format) ? "align" : "type")
  return (
    <Button
      variant="ghost"
      size="sm"
      className={cn("h-8 w-8 p-0", active && "bg-muted")}
      onMouseDown={(e) => {
        e.preventDefault()
        toggleBlock(editor, format)
      }}
    >
      <Icon className="h-4 w-4" />
    </Button>
  )
}

const MarkButton = ({ format, icon: Icon }: { format: string; icon: any }) => {
  const editor = useSlate()
  const active = isMarkActive(editor, format)
  return (
    <Button
      variant="ghost"
      size="sm"
      className={cn("h-8 w-8 p-0", active && "bg-muted")}
      onMouseDown={(e) => {
        e.preventDefault()
        toggleMark(editor, format)
      }}
    >
      <Icon className="h-4 w-4" />
    </Button>
  )
}
