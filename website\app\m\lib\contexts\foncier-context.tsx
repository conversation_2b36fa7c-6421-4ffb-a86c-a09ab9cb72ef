"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"

export interface FoncierService {
  id?: string
  title: string
  shortDescription: string
  detailedDescription: string
  category: string
  estimatedDuration: string
  complexity: "Simple" | "Modéré" | "Complexe"
  featured: boolean
  prestations: Array<{
    title: string
    description: string
  }>
  advantages: Array<{
    title: string
    description: string
  }>
  whenToUse: Array<{
    title: string
    description: string
  }>
  testimonials: Array<{
    name: string
    role: string
    location: string
    content: string
    rating: number
  }>
  faq: Array<{
    question: string
    answer: string
  }>
  createdAt?: string
  updatedAt?: string
  status?: "active" | "inactive"
}

interface FoncierContextType {
  services: FoncierService[]
  createService: (serviceData: Omit<FoncierService, "id">) => Promise<FoncierService>
  updateService: (id: string, serviceData: Partial<FoncierService>) => Promise<FoncierService>
  deleteService: (id: string) => Promise<void>
  getService: (id: string) => FoncierService | undefined
  isLoading: boolean
}

const FoncierContext = createContext<FoncierContextType | undefined>(undefined)

export function FoncierProvider({ children }: { children: React.ReactNode }) {
  const [services, setServices] = useState<FoncierService[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const createService = async (serviceData: Omit<FoncierService, "id">): Promise<FoncierService> => {
    setIsLoading(true)

    try {
      // Create the exact JSON structure as per API documentation
      const requestBody = {
        title: serviceData.title,
        shortDescription: serviceData.shortDescription,
        detailedDescription: serviceData.detailedDescription,
        category: serviceData.category,
        estimatedDuration: serviceData.estimatedDuration,
        complexity: serviceData.complexity,
        featured: serviceData.featured,
        prestations: serviceData.prestations,
        advantages: serviceData.advantages,
        whenToUse: serviceData.whenToUse,
        testimonials: serviceData.testimonials,
        faq: serviceData.faq,
      }

      console.log("Foncier Service POST /api/foncier-service")
      console.log("Content-Type: application/json")
      console.log("Request Body:", JSON.stringify(requestBody, null, 2))

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const newService: FoncierService = {
        ...serviceData,
        id: Math.random().toString(36).substr(2, 9),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: "active",
      }

      setServices((prev) => [...prev, newService])
      return newService
    } finally {
      setIsLoading(false)
    }
  }

  const updateService = async (id: string, serviceData: Partial<FoncierService>): Promise<FoncierService> => {
    setIsLoading(true)

    try {
      const existingService = services.find((s) => s.id === id)
      if (!existingService) throw new Error("Service not found")

      const updatedService = {
        ...existingService,
        ...serviceData,
        updatedAt: new Date().toISOString(),
      }

      setServices((prev) => prev.map((s) => (s.id === id ? updatedService : s)))
      return updatedService
    } finally {
      setIsLoading(false)
    }
  }

  const deleteService = async (id: string): Promise<void> => {
    setIsLoading(true)

    try {
      await new Promise((resolve) => setTimeout(resolve, 500))
      setServices((prev) => prev.filter((s) => s.id !== id))
    } finally {
      setIsLoading(false)
    }
  }

  const getService = (id: string): FoncierService | undefined => {
    return services.find((s) => s.id === id)
  }

  return (
    <FoncierContext.Provider
      value={{
        services,
        createService,
        updateService,
        deleteService,
        getService,
        isLoading,
      }}
    >
      {children}
    </FoncierContext.Provider>
  )
}

export function useFoncier() {
  const context = useContext(FoncierContext)
  if (context === undefined) {
    throw new Error("useFoncier must be used within a FoncierProvider")
  }
  return context
}
