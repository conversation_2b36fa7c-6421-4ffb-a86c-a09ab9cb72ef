"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Plus, Search, MoreHorizontal, Edit, Trash2, Eye, Download, Grid, List } from "lucide-react"
import Image from "next/image"
import { useMedia, type MediaFile } from "@/app/m/lib/contexts/media-context"
import { MediaUploadModal } from "@/components/media-upload-modal"

export default function MediaPage() {
  const { files, updateFile, deleteFile, isLoading } = useMedia()
  const [searchTerm, setSearchTerm] = useState("")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingFile, setEditingFile] = useState<MediaFile | null>(null)

  const filteredFiles = files.filter(
    (file) =>
      file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.type.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const getTypeColor = (type: MediaFile["type"]) => {
    switch (type) {
      case "image":
        return "bg-blue-100 text-blue-800"
      case "video":
        return "bg-purple-100 text-purple-800"
      case "document":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const handleEditFile = (file: MediaFile) => {
    setEditingFile({ ...file })
    setIsEditModalOpen(true)
  }

  const handleUpdateFile = async () => {
    if (!editingFile?.id) return

    try {
      await updateFile(editingFile.id, {
        name: editingFile.name,
        alt: editingFile.alt,
        caption: editingFile.caption,
      })
      setIsEditModalOpen(false)
      setEditingFile(null)
    } catch (error) {
      console.error("Error updating file:", error)
    }
  }

  const handleDeleteFile = async (fileId: string) => {
    if (window.confirm("Are you sure you want to delete this file?")) {
      try {
        await deleteFile(fileId)
      } catch (error) {
        console.error("Error deleting file:", error)
      }
    }
  }

  const handleDownload = (file: MediaFile) => {
    if (file.url) {
      const link = document.createElement("a")
      link.href = file.url
      link.download = file.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Media Library</h1>
          <p className="text-muted-foreground">Manage your images, videos, and documents</p>
        </div>
        <Button onClick={() => setIsUploadModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Upload Media
        </Button>
      </div>

      {/* Upload Modal */}
      <MediaUploadModal isOpen={isUploadModalOpen} onClose={() => setIsUploadModalOpen(false)} />

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Media File</DialogTitle>
            <DialogDescription>Update file information and metadata</DialogDescription>
          </DialogHeader>
          {editingFile && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">File Name</Label>
                <Input
                  id="edit-name"
                  value={editingFile.name}
                  onChange={(e) => setEditingFile((prev) => (prev ? { ...prev, name: e.target.value } : null))}
                  placeholder="Enter file name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-alt">Alt Text</Label>
                <Input
                  id="edit-alt"
                  value={editingFile.alt || ""}
                  onChange={(e) => setEditingFile((prev) => (prev ? { ...prev, alt: e.target.value } : null))}
                  placeholder="Enter alt text for accessibility"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-caption">Caption/Description</Label>
                <Textarea
                  id="edit-caption"
                  value={editingFile.caption || ""}
                  onChange={(e) => setEditingFile((prev) => (prev ? { ...prev, caption: e.target.value } : null))}
                  placeholder="Enter caption or description"
                  rows={3}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateFile} disabled={isLoading}>
              <Edit className="mr-2 h-4 w-4" />
              Update File
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Card>
        <CardHeader>
          <CardTitle>All Media</CardTitle>
          <CardDescription>Browse and manage your media files</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search media..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {viewMode === "grid" ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {filteredFiles.map((file) => (
                <Card key={file.id} className="group hover:shadow-md transition-shadow">
                  <CardContent className="p-3">
                    <div className="aspect-square relative mb-2 bg-muted rounded-md overflow-hidden">
                      {file.type === "image" ? (
                        <Image
                          src={file.url || "/placeholder.svg"}
                          alt={file.alt || file.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <div className="text-center">
                            <div className="text-2xl mb-1">{file.type === "video" ? "🎥" : "📄"}</div>
                            <p className="text-xs text-muted-foreground">{file.type}</p>
                          </div>
                        </div>
                      )}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="secondary" size="sm" className="h-6 w-6 p-0">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditFile(file)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDownload(file)}>
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => file.id && handleDeleteFile(file.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <p className="text-xs font-medium truncate">{file.name}</p>
                      <div className="flex items-center justify-between">
                        <Badge className={getTypeColor(file.type)} variant="secondary">
                          {file.type}
                        </Badge>
                        <p className="text-xs text-muted-foreground">{formatFileSize(file.size)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredFiles.map((file) => (
                <Card key={file.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
                        {file.type === "image" ? (
                          <Image
                            src={file.url || "/placeholder.svg"}
                            alt={file.alt || file.name}
                            width={48}
                            height={48}
                            className="object-cover rounded-md"
                          />
                        ) : (
                          <div className="text-lg">{file.type === "video" ? "🎥" : "📄"}</div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{file.name}</p>
                            <p className="text-sm text-muted-foreground">
                              Uploaded by {file.uploadedBy} •{" "}
                              {file.uploadedAt ? new Date(file.uploadedAt).toLocaleDateString() : ""}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className={getTypeColor(file.type)}>{file.type}</Badge>
                            <p className="text-sm text-muted-foreground">{formatFileSize(file.size)}</p>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEditFile(file)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDownload(file)}>
                                  <Download className="mr-2 h-4 w-4" />
                                  Download
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => file.id && handleDeleteFile(file.id)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
