"use client";

import React, { useState, useRef, useEffect } from "react";
import { useLanguage } from "@/app/translations/language-context";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useFormContext } from "./form-context";
import { useOnlineStatus } from "@/app/contexts/online-status-context";
import { FormField } from "./form-field";
import { personalInfoSchema } from "../validation/schemas";
import useFormValidation from "../validation/useFormValidation";
import { generateReferenceNumber, hasActiveUploads } from "../utils/file-upload";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
// Removed FileUpload import as we're using a custom implementation

interface StepPersonalProps {
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

export function StepPersonal({
  onNext,
  isFirstStep,
}: StepPersonalProps) {
  const { t } = useLanguage();
  const { formData, updateFormData } = useFormContext();

  // We'll generate the reference number when the name is entered instead of on component mount

  // Initialize form validation
  const {
    validate,
    getFieldError,
    touchField,
    hasFieldError,
    validateField,
  } = useFormValidation(formData, personalInfoSchema);

  // Handle field blur to validate individual fields
  const handleFieldBlur = (fieldName: string) => {
    touchField(fieldName);
    validateField(fieldName);
  };

  // Handle next button click
  const handleNext = () => {
    // Check if any files are currently uploading
    if (hasActiveUploads(formData)) {
      console.log('Cannot proceed while files are uploading');
      return;
    }

    // Validate the form
    const isValid = validate();

    if (isValid) {
      onNext();
    } else {
      // Focus the first field with an error
      const firstErrorField = document.querySelector('[aria-invalid="true"]') as HTMLElement;
      if (firstErrorField) {
        firstErrorField.focus();
      }
    }
  };

  const { isOnline } = useOnlineStatus();

  // Create a ref for the file input
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // State for file preview and error
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [fileError, setFileError] = useState<string | null>(null);

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;

    // Reset error
    setFileError(null);

    if (file) {
      console.log('StepPersonal: ID document selected:', file.name, 'Size:', file.size, 'Type:', file.type);

      // Check file size (2MB limit)
      if (file.size > 2 * 1024 * 1024) {
        console.warn('StepPersonal: File too large:', file.size);
        setFileError(t('common.fileSize'));
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
        return;
      }

      // Update form data with the file
      updateFormData({ idDocument: file });

      // Create preview for image files
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (event) => {
          setFilePreview(event.target?.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setFilePreview(null);
      }

      // Always upload the file immediately when selected
      console.log('StepPersonal: Uploading file immediately');

      // Update status to uploading
      const newUploadStatus = { ...(formData.uploadStatus || {}) };
      newUploadStatus[file.name] = 'uploading';
      updateFormData({ uploadStatus: newUploadStatus });

      // Import the upload function
      import('../utils/file-upload').then(({ uploadFileToGoogleDrive }) => {
        // Trigger the upload
        uploadFileToGoogleDrive(file, formData, updateFormData)
          .then(result => {
            console.log('StepPersonal: ID document upload result:', result);
          })
          .catch(error => {
            console.error('StepPersonal: Error uploading ID document:', error);
          });
      });
    } else {
      // Clear file and preview if no file selected
      updateFormData({ idDocument: null });
      setFilePreview(null);
    }
  };

  return (
    <div className="space-y-8" data-testid="step-welcome-personal">
      <div className="mb-6">
        <div className="flex items-center justify-center mb-4">
          <div className="w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center">
            <span className="text-2xl">👋</span>
          </div>
        </div>
        <h2 className="text-2xl font-bold text-center mb-2">{t('stepPersonal.title')}</h2>
        <p className="text-muted-foreground text-center" data-testid="welcome-intro-text">
          {t('stepPersonal.intro')}
        </p>
      </div>

      {/* Data Protection */}
      <div className="bg-accent/10 p-4 rounded-md border border-accent/20">
        <h3 className="font-medium mb-2">{t('stepPersonal.dataProtection.title')}</h3>
        <p className="text-sm text-muted-foreground" data-testid="data-protection-text">
          {t('stepPersonal.dataProtection.description')}
        </p>
        <div className="mt-4 flex items-start space-x-2">
          <Checkbox
            id="consent"
            checked={formData.consent}
            onCheckedChange={(checked) => {
              updateFormData({ consent: checked as boolean });
              touchField('consent');
              validateField('consent');
            }}
            data-testid="consent-checkbox"
            className={`h-5 w-5 border-2 ${
              getFieldError('consent')
                ? "border-red-500 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                : "border-input data-[state=checked]:bg-primary data-[state=checked]:border-primary"
            }`}
          />
          <div className="grid gap-1.5 leading-none">
            <label
              htmlFor="consent"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {t('stepPersonal.dataProtection.consent')}{t('common.required')}
            </label>
            {getFieldError('consent') && (
              <div className="text-red-500 text-sm flex items-start mt-1" role="alert">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <span>{/*@ts-ignore */
                        t(getFieldError('consent'))
                        }
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Personal Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">{t('stepPersonal.personalInfo.title')}</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Full Name */}
          <FormField
            id="fullName"
            label={t('stepPersonal.personalInfo.fullName.label')}
            value={formData.fullName || ""}
            onChange={(value) => {
              updateFormData({ fullName: value });

              // Generate a reference number when the name is entered and valid (at least 3 characters)
              if (value && value.split(" ").length > 1) {
                // wait for 10 seconds
                const referenceNum = generateReferenceNumber(value);
                console.log('Generated/updated reference number based on name:', referenceNum);

                // Update form data with the reference number
                updateFormData({ referenceNumber: referenceNum });

                // Store it in local storage for immediate availability
                if (typeof window !== 'undefined') {
                  try {
                    // Get existing data or create new object
                    const savedData = localStorage.getItem('formData');
                    const parsedData = savedData ? JSON.parse(savedData) : {};

                    // Update with new reference number
                    parsedData.referenceNumber = referenceNum;

                    // Save back to local storage
                    localStorage.setItem('formData', JSON.stringify(parsedData));
                    console.log('Reference number saved to local storage:', referenceNum);
                  } catch (error) {
                    console.error('Error updating reference number in local storage:', error);
                  }
                }
              }
            }}
            onBlur={() => handleFieldBlur('fullName')}
            placeholder={t('stepPersonal.personalInfo.fullName.placeholder')}
            required={true}
            error={getFieldError('fullName')}
            data-testid="personal-fullname-input"
          />

          {/* Gender */}
          <div className="space-y-2">
            <Label className="flex items-center">
              {t('stepPersonal.personalInfo.gender.label')}
              <span className="text-red-500 ml-1">{t('common.required')}</span>
            </Label>
            <RadioGroup
              value={formData.gender}
              onValueChange={(value) => {
                updateFormData({ gender: value as "Masculin" | "Féminin" });
                touchField('gender');
                validateField('gender');
              }}
              data-testid="personal-gender-radio"
              className="flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="Masculin"
                  id="gender-male"
                  className={`h-5 w-5 border-2 ${getFieldError('gender') ? "border-red-500" : "border-input"}`}
                />
                <Label htmlFor="gender-male">{t('stepPersonal.personalInfo.gender.male')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="Féminin"
                  id="gender-female"
                  className={`h-5 w-5 border-2 ${getFieldError('gender') ? "border-red-500" : "border-input"}`}
                />
                <Label htmlFor="gender-female">{t('stepPersonal.personalInfo.gender.female')}</Label>
              </div>
            </RadioGroup>
            {getFieldError('gender') && (
              <div className="text-red-500 text-sm flex items-start mt-1" role="alert">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <span>{/*@ts-ignore */
                        t(getFieldError('gender'))
                        }
                </span>
              </div>
            )}
          </div>

          {/* Birth Date */}
          <div className="space-y-2">
            <Label htmlFor="birthDate" className="flex items-center">
              {t('stepPersonal.personalInfo.birthDate.label')}
              {<span className="text-red-500 ml-1">{t('common.required')}</span>}
            </Label>
            <Input
              id="birthDate"
              type="date"
              value={formData.birthDate ? formData.birthDate.split('T')[0] : ""}
              onChange={(e) => updateFormData({ birthDate: e.target.value ? new Date(e.target.value).toISOString() : undefined })}
              onBlur={() => handleFieldBlur('birthDate')}
              data-testid="personal-birthdate-input"
              className={`w-full border-2 ${
                getFieldError('birthDate')
                  ? "border-red-500 focus:border-red-500"
                  : "border-input focus:border-primary"
              } bg-background`}
              aria-invalid={!!getFieldError('birthDate')}
            />
            {getFieldError('birthDate') && (
              <div className="text-red-500 text-sm flex items-start mt-1" role="alert">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <span>{getFieldError('birthDate')}</span>
              </div>
            )}
          </div>

          {/* Birth Place */}
          <FormField
            id="birthPlace"
            label={t('stepPersonal.personalInfo.birthPlace.label')}
            value={formData.birthPlace || ""}
            onChange={(value) => updateFormData({ birthPlace: value })}
            onBlur={() => handleFieldBlur('birthPlace')}
            placeholder={t('stepPersonal.personalInfo.birthPlace.placeholder')}
            required={true}
            error={getFieldError('birthPlace')}
            data-testid="personal-birthplace-input"
          />

          {/* Nationality */}
          <FormField
            id="nationality"
            label={t('stepPersonal.personalInfo.nationality.label')}
            value={formData.nationality || ""}
            onChange={(value) => updateFormData({ nationality: value })}
            onBlur={() => handleFieldBlur('nationality')}
            placeholder={t('stepPersonal.personalInfo.nationality.placeholder')}
            required={true}
            error={getFieldError('nationality')}
            data-testid="personal-nationality-input"
          />

          {/* Profession */}
          <FormField
            id="profession"
            label={t('stepPersonal.personalInfo.profession.label')}
            value={formData.profession || ""}
            onChange={(value) => updateFormData({ profession: value })}
            onBlur={() => handleFieldBlur('profession')}
            placeholder={t('stepPersonal.personalInfo.profession.placeholder')}
            error={getFieldError('profession')}
            data-testid="personal-profession-input"
          />
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">{t('stepPersonal.contactInfo.title')}</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Primary Phone */}
          <FormField
            id="primaryPhone"
            label={t('stepPersonal.contactInfo.primaryPhone.label')}
            value={formData.primaryPhone || ""}
            onChange={(value) => updateFormData({ primaryPhone: value })}
            onBlur={() => handleFieldBlur('primaryPhone')}
            placeholder={t('stepPersonal.contactInfo.primaryPhone.placeholder')}
            required={true}
            error={getFieldError('primaryPhone')}
            data-testid="personal-phone-primary-input"
          />

          {/* Secondary Phone */}
          <FormField
            id="secondaryPhone"
            label={t('stepPersonal.contactInfo.secondaryPhone.label')}
            value={formData.secondaryPhone || ""}
            onChange={(value) => updateFormData({ secondaryPhone: value })}
            onBlur={() => handleFieldBlur('secondaryPhone')}
            placeholder={t('stepPersonal.contactInfo.secondaryPhone.placeholder')}
            error={getFieldError('secondaryPhone')}
            data-testid="personal-phone-secondary-input"
          />

          {/* Email */}
          <FormField
            id="email"
            label={t('stepPersonal.contactInfo.email.label')}
            value={formData.email || ""}
            onChange={(value) => updateFormData({ email: value })}
            onBlur={() => handleFieldBlur('email')}
            placeholder={t('stepPersonal.contactInfo.email.placeholder')}
            type="email"
            error={getFieldError('email')}
            data-testid="personal-email-input"
            className="md:col-span-2"
          />

          {/* Address */}
          <FormField
            id="address"
            label={t('stepPersonal.contactInfo.address.label')}
            value={formData.address || ""}
            onChange={(value) => updateFormData({ address: value })}
            onBlur={() => handleFieldBlur('address')}
            placeholder={t('stepPersonal.contactInfo.address.placeholder')}
            required={true}
            error={getFieldError('address')}
            data-testid="personal-address-textarea"
            multiline={true}
            className="md:col-span-2"
          />

          {/* ID Document Upload */}
          <div className="md:col-span-2 space-y-2">
            <p className="text-sm font-medium">
              {t('stepPersonal.contactInfo.idDocument.label')}
              <span className="text-red-500 ml-1">*</span>
            </p>

            <input
              type="file"
              disabled={!isOnline || !formData.fullName || formData.fullName.trim().length < 3 || !formData.referenceNumber}
              accept=".pdf,.jpg,.png,.jpeg"
              className="w-full"
              onChange={handleFileUpload}
              data-testid="personal-id-upload"
              ref={fileInputRef}
            />

            {fileError && (
              <div className="flex items-center mt-2 text-red-500 text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span>{fileError}</span>
              </div>
            )}
            {
              !isOnline && (
                <div className="flex items-center mt-2 text-red-500 text-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"></svg>
                  <span>{t('common.fileUploadOffline')}</span>
                </div>
              )
            }
            {
              (!formData.fullName || formData.fullName.trim().length < 3) && (
                <div className="flex items-center mt-2 text-amber-500 text-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{t('stepPersonal.contactInfo.idDocument.nameRequired')}</span>
                </div>
              )
            }
            {filePreview && (
              <div className="mt-2">
                <p className="text-xs font-medium mb-1">{t('common.filePreview')}</p>
                <img
                  src={filePreview}
                  alt={t('stepPersonal.contactInfo.idDocument.label')}
                  className="max-h-32 max-w-full object-contain border rounded"
                />
              </div>
            )}

            {formData.idDocument && !filePreview && (
              <div className="mt-2 flex items-center text-sm text-green-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{t('common.fileSelected')} {formData.idDocument.name}</span>
              </div>
            )}

            {/* Upload Status */}
            {formData.idDocument && formData.uploadStatus && formData.uploadStatus[formData.idDocument.name] && (
              <div className="mt-2">
                {formData.uploadStatus[formData.idDocument.name] === 'uploading' && (
                  <div className="flex items-center text-blue-500 text-sm">
                    <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>{t('common.uploading')}</span>
                  </div>
                )}

                {formData.uploadStatus[formData.idDocument.name] === 'success' && (
                  <div className="flex items-center text-green-500 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>{t('common.uploaded')}</span>
                  </div>
                )}

                {formData.uploadStatus[formData.idDocument.name] === 'error' && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-red-500 text-sm">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>{t('common.uploadFailed')}</span>
                    </div>

                    <button
                      type="button"
                      onClick={() => {
                        const file = formData.idDocument;
                        if (file) {
                          // Import the upload function
                          import('../utils/file-upload').then(({ uploadFileToGoogleDrive }) => {
                            uploadFileToGoogleDrive(file, formData, updateFormData);
                          });
                        }
                      }}
                      className="text-xs text-blue-500 hover:text-blue-700 ml-2"
                    >
                      {t('common.retry')}
                    </button>
                  </div>
                )}
              </div>
            )}

            <p className="text-xs text-gray-500 mt-2">
              {t('stepPersonal.contactInfo.idDocument.description')} ({t('common.fileFormats')})
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-end pt-4">
        <Button
          onClick={handleNext}
          data-testid="personal-next-button"
          className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
          disabled={hasActiveUploads(formData)}
        >
          {hasActiveUploads(formData) ? t('common.waitForUploads') : t('common.next')}
        </Button>
      </div>
    </div>
  );
}
