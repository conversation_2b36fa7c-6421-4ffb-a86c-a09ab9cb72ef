"use client";

/**
 * Client-side file upload utilities for form submissions
 *
 * This module provides utilities for uploading files from the client side to Google Drive
 * via the server API. It handles tracking upload status, managing folder creation,
 * and updating form data with upload results.
 *
 * @module file-upload
 * @requires ../components/form-context
 */

import type { FormData } from "../components/form-context";

/**
 * Interface for file upload result from the server API
 *
 * This interface represents the result of a file upload operation returned by the server API.
 * It includes information about the uploaded file and any errors that occurred.
 *
 * @interface FileUploadResult
 */
export interface FileUploadResult {
  /**
   * Whether the upload was successful
   *
   * @type {boolean}
   * @memberof FileUploadResult
   */
  success: boolean;

  /**
   * The ID of the uploaded file in Google Drive
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  fileId?: string;

  /**
   * The name of the uploaded file
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  fileName?: string;

  /**
   * The URL to view the file in Google Drive
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  webViewLink?: string;

  /**
   * The URL to download the file content
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  webContentLink?: string;

  /**
   * A direct sharing link that works for public access
   * Format: https://drive.google.com/file/d/{fileId}/view?usp=sharing
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  directLink?: string;

  /**
   * Information about the folder the file was uploaded to
   *
   * @type {Object}
   * @property {string} id - The ID of the folder
   * @property {string} name - The name of the folder
   * @property {string} [webViewLink] - The URL to view the folder
   * @memberof FileUploadResult
   */
  folder?: {
    id: string;
    name: string;
    webViewLink?: string;
  };

  /**
   * Error message if the upload failed
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  error?: string;
}

/**
 * Interface for tracking file upload status in the form data
 *
 * This interface represents the current status of a file upload operation
 * and is used to track and display the status in the UI.
 *
 * @interface FileUploadStatus
 */
export interface FileUploadStatus {
  /**
   * The current status of the upload
   * - 'idle': Initial state, not yet started
   * - 'uploading': Upload in progress
   * - 'success': Upload completed successfully
   * - 'error': Upload failed
   *
   * @type {('uploading' | 'success' | 'error' | 'idle')}
   * @memberof FileUploadStatus
   */
  status: 'uploading' | 'success' | 'error' | 'idle';

  /**
   * The ID of the uploaded file in Google Drive
   * Only present if status is 'success'
   *
   * @type {string}
   * @memberof FileUploadStatus
   */
  fileId?: string;

  /**
   * The name of the file being uploaded
   *
   * @type {string}
   * @memberof FileUploadStatus
   */
  fileName: string;

  /**
   * Error message if the upload failed
   * Only present if status is 'error'
   *
   * @type {string}
   * @memberof FileUploadStatus
   */
  error?: string;

  /**
   * The URL to view the file in Google Drive
   * Only present if status is 'success'
   *
   * @type {string}
   * @memberof FileUploadStatus
   */
  webViewLink?: string;

  /**
   * The URL to download the file content
   * Only present if status is 'success'
   *
   * @type {string}
   * @memberof FileUploadStatus
   */
  webContentLink?: string;
}

/**
 * Generate a consistent reference number
 *
 * This function generates a reference number in the format CAPITALIZED_USER_NAME-ddMMYY:HHmm
 * For example: MICHEAL-271003:1430 indicating the name, day, month, year, hour, minute
 *
 * @param {string} fullName - The user's full name (optional)
 * @returns {string} A reference number in the format CAPITALIZED_USER_NAME-ddMMYY:HHmm
 */
export function generateReferenceNumber(fullName?: string): string {
  // Get the current date and time
  const now = new Date();

  // Format the date and time components
  const day = String(now.getDate()).padStart(2, '0');
  const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const year = String(now.getFullYear()).slice(-2); // Last two digits of year
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');

  // Format the date part: ddMMYY
  const datePart = `${day}${month}${year}`;

  // Format the time part: HHmm
  const timePart = `${hours}${minutes}`;

  // Process the name part
  let namePart = 'USER';
  if (fullName && fullName.trim()) {
    // Extract the first name (or use the full name if no spaces)
    const firstName = fullName.split(' ')[0];
    // Convert to uppercase and remove special characters
    namePart = firstName.toUpperCase().replace(/[^A-Z]/g, '');
  }

  // Combine all parts into the final reference number
  // Use underscore instead of colon to avoid file path issues
  return `${namePart}-${datePart}_${timePart}`;
}

/**
 * Generate a unique folder name based on the user's name
 *
 * This function creates a unique folder name by sanitizing the user's full name
 * (removing special characters and spaces) and appending a timestamp to ensure uniqueness.
 * The resulting folder name is suitable for use in Google Drive and file systems.
 *
 * @param {string} fullName - The user's full name
 * @returns {string} A unique folder name suitable for use in Google Drive
 *
 * @example
 * // Generate a folder name for a user
 * const folderName = generateUniqueFolderName('John Doe');
 * // Returns something like: 'John_Doe_123456'
 *
 * @example
 * // Generate a folder name with special characters
 * const folderName = generateUniqueFolderName('María López-García');
 * // Returns something like: 'Mar_a_L_pez_Garc_a_123456'
 */
export function generateUniqueFolderName(fullName: string): string {
  // Remove special characters and spaces
  const sanitizedName = fullName.replace(/[^a-zA-Z0-9]/g, '_');

  // Add a timestamp to ensure uniqueness
  const timestamp = Date.now().toString().slice(-6);

  return `${sanitizedName}_${timestamp}`;
}

/**
 * Upload a file to Google Drive via the server API
 *
 * This function uploads a file to Google Drive via the server API and updates the form data
 * with the upload status and results. It handles creating a folder for the user if needed,
 * tracking the upload status, and handling errors.
 *
 * @param {File | null} file - The file to upload, or null if no file is selected
 * @param {FormData} formData - The current form data state
 * @param {function} updateFormData - Function to update the form data state
 * @returns {Promise<FileUploadResult>} A promise that resolves to the result of the upload operation
 *
 * @throws {Error} The function handles errors internally and returns a FileUploadResult with success=false
 *
 * @example
 * // Upload a file from a file input
 * const fileInput = document.getElementById('fileInput');
 * const file = fileInput.files[0];
 * const result = await uploadFileToGoogleDrive(file, formData, updateFormData);
 *
 * if (result.success) {
 *   console.log(`File uploaded successfully: ${result.fileName}`);
 * } else {
 *   console.error(`Failed to upload file: ${result.error}`);
 * }
 */
export async function uploadFileToGoogleDrive(
  file: File | null,
  formData: FormData,
  updateFormData: (newData: Partial<FormData>) => void
): Promise<FileUploadResult> {
  console.log('Starting file upload process for:', file?.name);

  // Return early if file is null
  if (!file) {
    console.error('No file provided for upload');
    return {
      success: false,
      error: 'No file provided'
    };
  }
  try {
    // Update status to uploading
    console.log('Setting upload status to uploading for:', file.name);
    const newUploadStatus = { ...(formData.uploadStatus || {}) };
    newUploadStatus[file.name] = 'uploading';
    updateFormData({ uploadStatus: newUploadStatus });

    // Create form data for the API request
    const apiFormData = new FormData();
    apiFormData.append('file', file);

    // If we already have a folder ID, use it
    if (formData.userFolderId) {
      console.log('Using existing folder ID:', formData.userFolderId);
      apiFormData.append('folderId', formData.userFolderId);
    }
    // If we have a reference number, use it directly as the folder name
    else if (formData.referenceNumber) {
      // Use the reference number directly as the folder name for consistency
      const folderName = formData.referenceNumber;
      console.log('Creating new folder with reference number as name:', folderName);
      apiFormData.append('folderName', folderName);
    }
    // Check if reference number exists in local storage
    else if (typeof window !== 'undefined') {
      try {
        const savedData = localStorage.getItem('formData');
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData.referenceNumber) {
            // Use the reference number from local storage
            const folderName = parsedData.referenceNumber;
            console.log('Creating folder with reference number from local storage:', folderName);
            apiFormData.append('folderName', folderName);

            // Update form data with reference number from local storage
            updateFormData({ referenceNumber: parsedData.referenceNumber });
          }
        }
      } catch (error) {
        console.error('Error reading reference number from local storage:', error);
      }
    }
    // Otherwise, create a new folder if we have a name
    else if (formData.fullName) {
      const folderName = generateUniqueFolderName(formData.fullName);
      console.log('Creating new folder with name:', folderName);
      apiFormData.append('folderName', folderName);
    } else {
      // If no folder ID or user name, use a temporary folder name based on timestamp
      const tempFolderName = `temp-user-${Date.now()}`;
      console.log('No user name available, creating temporary folder:', tempFolderName);
      apiFormData.append('folderName', tempFolderName);
    }

    console.log('Sending API request to /api/formulaire/upload');
    // Send the request to the API
    const response = await fetch('/api/formulaire/upload', {
      method: 'POST',
      body: apiFormData,
    });

    if (!response.ok) {
      console.error(`Upload failed with status: ${response.status} ${response.statusText}`);
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    console.log('API response received, parsing JSON');
    const result: FileUploadResult = await response.json();
    console.log('Upload result:', result);

    // Debug: Log the complete result object
    console.log('Complete upload result:', JSON.stringify(result, null, 2));

    // Update form data with the result
    const newUploadedFileIds = { ...(formData.uploadedFileIds || {}) };
    const newUploadedFileUrls = { ...(formData.uploadedFileUrls || {}) };
    const newUploadedFileNames = { ...(formData.uploadedFileNames || {}) };

    console.log('Current uploadedFileUrls:', JSON.stringify(formData.uploadedFileUrls, null, 2));

    if (result.fileId) {
      console.log('Saving file ID:', result.fileId);
      newUploadedFileIds[file.name] = result.fileId;

      // Save file URL if available - prefer directLink over webViewLink
      if (result.directLink) {
        console.log('Saving direct link URL:', result.directLink);
        newUploadedFileUrls[file.name] = result.directLink;

        // Also save the URL with a simpler key (just the file name without path)
        const simpleFileName = file.name.split('/').pop() || file.name;
        if (simpleFileName !== file.name) {
          console.log('Also saving URL with simple file name:', simpleFileName);
          newUploadedFileUrls[simpleFileName] = result.directLink;
        }
      } else if (result.webViewLink) {
        console.log('No direct link available, saving webViewLink:', result.webViewLink);
        newUploadedFileUrls[file.name] = result.webViewLink;

        // Also save the URL with a simpler key (just the file name without path)
        const simpleFileName = file.name.split('/').pop() || file.name;
        if (simpleFileName !== file.name) {
          console.log('Also saving URL with simple file name:', simpleFileName);
          newUploadedFileUrls[simpleFileName] = result.webViewLink;
        }
      } else {
        console.warn('No file URLs available in result for file:', file.name);
      }

      // Save original file name mapping to unique file name
      if (result.fileName) {
        console.log('Saving file name mapping:', file.name, '->', result.fileName);
        newUploadedFileNames[file.name] = result.fileName;
      }
    }

    console.log('Updated uploadedFileUrls:', JSON.stringify(newUploadedFileUrls, null, 2));

    // Update upload status
    const updatedUploadStatus = { ...(formData.uploadStatus || {}) };
    updatedUploadStatus[file.name] = result.success ? 'success' : 'error';
    console.log('Setting upload status to:', updatedUploadStatus[file.name]);

    // Update upload errors if any
    const updatedUploadErrors = { ...(formData.uploadErrors || {}) };
    if (result.error) {
      console.error('Upload error:', result.error);
      updatedUploadErrors[file.name] = result.error;
    } else {
      delete updatedUploadErrors[file.name];
    }

    // If this is the first upload and a folder was created, save the folder info
    if (result.folder && !formData.userFolderId) {
      console.log('Saving folder info:', result.folder);

      // Debug: Log the data being saved
      console.log('Saving uploadedFileUrls:', JSON.stringify(newUploadedFileUrls, null, 2));

      // Create a deep copy to ensure we're not losing any properties
      const updatedData = {
        userFolderId: result.folder.id,
        userFolderName: result.folder.name,
        userFolderLink: result.folder.webViewLink,
        uploadedFileIds: { ...newUploadedFileIds },
        uploadedFileUrls: { ...newUploadedFileUrls },
        uploadedFileNames: { ...newUploadedFileNames },
        uploadStatus: { ...updatedUploadStatus },
        uploadErrors: { ...updatedUploadErrors }
      };

      updateFormData(updatedData);

      // Debug: Log the form data after update
      console.log('Form data after update should include:', JSON.stringify(updatedData.uploadedFileUrls, null, 2));
    } else {
      console.log('Updating form data with upload results');

      // Debug: Log the data being saved
      console.log('Saving uploadedFileUrls:', JSON.stringify(newUploadedFileUrls, null, 2));

      // Create a deep copy to ensure we're not losing any properties
      const updatedData = {
        uploadedFileIds: { ...newUploadedFileIds },
        uploadedFileUrls: { ...newUploadedFileUrls },
        uploadedFileNames: { ...newUploadedFileNames },
        uploadStatus: { ...updatedUploadStatus },
        uploadErrors: { ...updatedUploadErrors }
      };

      updateFormData(updatedData);

      // Debug: Log the form data after update
      console.log('Form data after update should include:', JSON.stringify(updatedData.uploadedFileUrls, null, 2));
    }

    console.log('Upload completed successfully');
    return result;
  } catch (error) {
    console.error('Error uploading file:', error);

    // Update upload status to error
    console.log('Setting upload status to error due to exception');
    const updatedUploadStatus = { ...(formData.uploadStatus || {}) };
    updatedUploadStatus[file.name] = 'error';

    // Update upload errors
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error message:', errorMessage);
    const updatedUploadErrors = { ...(formData.uploadErrors || {}) };
    updatedUploadErrors[file.name] = errorMessage;

    console.log('Updating form data with error information');
    updateFormData({
      uploadStatus: updatedUploadStatus,
      uploadErrors: updatedUploadErrors
    });

    return {
      success: false,
      fileName: file.name,
      error: errorMessage
    };
  }
}

/**
 * Retry a failed file upload
 *
 * This function resets the upload status for a file that previously failed to upload
 * and attempts to upload it again. It's used to provide a retry mechanism in the UI
 * for files that failed to upload due to temporary issues.
 *
 * @param {File | null} file - The file to retry uploading, or null if no file is selected
 * @param {FormData} formData - The current form data state
 * @param {function} updateFormData - Function to update the form data state
 * @returns {Promise<FileUploadResult>} A promise that resolves to the result of the retry operation
 *
 * @throws {Error} The function handles errors internally and returns a FileUploadResult with success=false
 *
 * @example
 * // Retry uploading a file that previously failed
 * const result = await retryFileUpload(failedFile, formData, updateFormData);
 *
 * if (result.success) {
 *   console.log(`File upload retry successful: ${result.fileName}`);
 * } else {
 *   console.error(`File upload retry failed: ${result.error}`);
 * }
 */
export async function retryFileUpload(
  file: File | null,
  formData: FormData,
  updateFormData: (newData: Partial<FormData>) => void
): Promise<FileUploadResult> {
  // Return early if file is null
  if (!file) {
    return {
      success: false,
      error: 'No file provided'
    };
  }
  // Reset the status for this file
  const updatedUploadStatus = { ...(formData.uploadStatus || {}) };
  updatedUploadStatus[file.name] = 'idle';

  const updatedUploadErrors = { ...(formData.uploadErrors || {}) };
  delete updatedUploadErrors[file.name];

  updateFormData({
    uploadStatus: updatedUploadStatus,
    uploadErrors: updatedUploadErrors
  });

  // Retry the upload
  return uploadFileToGoogleDrive(file, formData, updateFormData);
}

/**
 * Check if any files are currently being uploaded
 *
 * This function checks the form data's uploadStatus object to determine if any files
 * are currently in the 'uploading' state. It's used to disable navigation buttons
 * during active uploads to prevent users from navigating away while uploads are in progress.
 *
 * @param {FormData} formData - The current form data state
 * @returns {boolean} True if any files are currently uploading, false otherwise
 *
 * @example
 * // Check if uploads are in progress before allowing navigation
 * const isUploading = hasActiveUploads(formData);
 * if (isUploading) {
 *   // Disable navigation or show a warning
 *   console.log('Please wait for uploads to complete before proceeding');
 * } else {
 *   // Allow navigation
 *   goToNextStep();
 * }
 */
export function hasActiveUploads(formData: FormData): boolean {
  if (!formData.uploadStatus) {
    return false;
  }

  // Check if any file has 'uploading' status
  return Object.values(formData.uploadStatus).some(status => status === 'uploading');
}
