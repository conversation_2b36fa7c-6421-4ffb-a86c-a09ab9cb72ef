"use client";

import { useState } from "react";
//@ts-ignore
import { StepPersonal } from "./step-personal";
//@ts-ignore
import { StepEmergencyProcedure } from "./step-emergency-procedure";
//@ts-ignore
import { StepDocumentsLocation } from "./step-documents-location";
//@ts-ignore
import { StepSummary } from "./step-summary";
//@ts-ignore
import { FormProvider } from "./form-context";
//@ts-ignore
import { StepSidebar } from "./step-sidebar";
import { useLanguage } from "@/app/translations/language-context";
import { UploadNotification } from "./upload-notification";

export function MultiStepForm() {
  const { t } = useLanguage();
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      id: "step-welcome-personal",
      name: t('steps.personal.name'),
      description: t('steps.personal.description'),
      component: StepPersonal,
    },
    {
      id: "step-emergency-procedure",
      name: t('steps.emergency.name'),
      description: t('steps.emergency.description'),
      component: StepEmergencyProcedure,
    },
    {
      id: "step-documents-location",
      name: t('steps.documents.name'),
      description: t('steps.documents.description'),
      component: StepDocumentsLocation,
    },
    {
      id: "step-summary",
      name: t('steps.summary.name'),
      description: t('steps.summary.description'),
      component: StepSummary,
    },
  ];

  const goToNextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 0 && step < steps.length) {
      setCurrentStep(step);
      window.scrollTo(0, 0);
    }
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <FormProvider>
      <div className="flex flex-col md:flex-row rounded-xl overflow-hidden shadow-lg max-w-6xl mx-auto bg-card">
        {/* Sidebar */}
        <StepSidebar
          steps={steps}
          currentStep={currentStep}
          goToStep={goToStep}
        />

        {/* Main content */}
        <div className="flex-1 p-6 md:p-8 lg:p-10 bg-background text-foreground">
          <h2 className="text-2xl font-semibold mb-4"></h2>
          <p className="mb-6 text-base text-gray-700 bg-primary/10 border-l-4 border-primary px-4 py-3 rounded">
            {t('fill')}
          </p>
          <CurrentStepComponent
            onNext={goToNextStep}
            onPrevious={goToPreviousStep}
            isFirstStep={currentStep === 0}
            isLastStep={currentStep === steps.length - 1}
          />
        </div>
      </div>

      {/* Upload notification */}
      <UploadNotification />
    </FormProvider>
  );
}
