import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"
import { AuthProvider } from "@/app/m/lib/auth-context"
import { Blog<PERSON>rovider } from "@/app/m/lib/contexts/blog-context"
import { FoncierProvider } from "@/app/m/lib/contexts/foncier-context"
import { MediaProvider } from "@/app/m/lib/contexts/media-context"
import { UserProvider } from "@/app/m/lib/contexts/user-context"
import { Rethink_Sans } from "next/font/google"

const rethinkSans = Rethink_Sans({ subsets: ["latin"], variable: "--font-rethink" })

export const metadata: Metadata = {
  title: "Charlie Oscar Consulting - CMS Dashboard",
  description: "Content Management System for Charlie Oscar Consulting",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={rethinkSans.className}>
        <AuthProvider>
          <UserProvider>
            <BlogProvider>
              <FoncierProvider>
                <MediaProvider>{children}</MediaProvider>
              </FoncierProvider>
            </BlogProvider>
          </UserProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
