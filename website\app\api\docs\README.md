# API Documentation with OpenAPI

This directory contains the API documentation setup for <PERSON>.

## Overview

The API documentation is built using the OpenAPI specification, which provides a standardized way to document APIs. The documentation is displayed using a custom UI built with React and Tailwind CSS, providing an interactive interface for exploring the API.

## Files

- `openapi.ts`: Contains the OpenAPI schema that defines the API endpoints, request/response formats, and other metadata.
- `page.tsx`: The Next.js page component that renders the custom API documentation UI.
- `layout.tsx`: The layout component for the API documentation page.

## How to Access

The API documentation is available at `/api/docs` in the application.

## Adding New Endpoints

To add a new endpoint to the API documentation:

1. Open `openapi.ts`
2. Add a new path entry to the `paths` object
3. Define the HTTP methods, parameters, request body, and responses for the endpoint
4. If needed, add new schema definitions to the `components.schemas` object

Example:

```typescript
// Add a new path
'/your-endpoint': {
  post: {
    tags: ['your-tag'],
    summary: 'Short description',
    description: 'Longer description of what the endpoint does',
    operationId: 'uniqueOperationId',
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              // Define request properties
            },
            required: ['requiredProperty'],
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Success response description',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/YourResponseSchema',
            },
          },
        },
      },
      // Define other response codes
    },
  },
},

// Add a new schema if needed
YourResponseSchema: {
  type: 'object',
  properties: {
    // Define response properties
  },
  required: ['requiredProperty'],
},
```

## Best Practices

1. **Keep documentation in sync with code**: Update the API documentation whenever you change an API endpoint.
2. **Use descriptive summaries and descriptions**: Make it easy for developers to understand what each endpoint does.
3. **Include examples**: Add example values to help developers understand the expected format.
4. **Document error responses**: Include information about possible error responses and their meanings.
5. **Use tags to organize endpoints**: Group related endpoints under the same tag.
6. **Use operation IDs**: Provide unique operation IDs for each endpoint to make them easier to reference.

## References

- [OpenAPI Specification](https://swagger.io/specification/)
- [React](https://reactjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Next.js](https://nextjs.org/)
