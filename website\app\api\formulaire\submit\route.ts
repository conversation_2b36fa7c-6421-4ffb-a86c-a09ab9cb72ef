import { NextRequest, NextResponse } from 'next/server';
import { uploadToGoogleDrive, getMimeType } from '@/app/api/formulaire/utils/google-drive';
import { sendConfirmationEmail } from '@/app/api/formulaire/utils/send-confirmation-email';

// Define the response type
interface SubmitResponse {
  success: boolean;
  referenceNumber?: string;
  message?: string;
  error?: string;
  fileId?: string;
  fileName?: string;
  webViewLink?: string;
  webContentLink?: string;
  emailSent?: boolean;
  folder?: {
    id: string;
    name: string;
    webViewLink?: string;
  };
}

/**
 * POST handler for form submissions
 * Receives form data including files and uploads them directly to Google Drive
 * without saving to the local filesystem (compatible with serverless environments)
 */
export async function POST(request: NextRequest): Promise<NextResponse<SubmitResponse>> {
  try {
    // Parse the form data
    const formData = await request.formData();

    // Get the reference number from the form data or generate a new one
    const formDataJson = formData.get('formData');
    let referenceNumber;

    if (formDataJson && typeof formDataJson === 'string') {
      try {
        const formDataObj = JSON.parse(formDataJson);
        referenceNumber = formDataObj.referenceNumber;
        console.log('API: Found reference number in form data:', referenceNumber);

        // Check if the form has already been submitted
        if (formDataObj.pdfSubmitted) {
          console.error('API: Form has already been submitted');
          return NextResponse.json({
            success: false,
            error: 'This form has already been submitted. Please refresh the page to start a new submission.'
          }, { status: 400 });
        }
      } catch (error) {
        console.error('API: Error parsing form data JSON:', error);
      }
    }

    // Check if a folder name was provided, which should be the reference number
    const folderName = formData.get('folderName') as string | null;
    if (!referenceNumber && folderName) {
      console.log('API: Using folder name as reference number:', folderName);
      referenceNumber = folderName;
    }

    // If still no reference number, generate a new one
    if (!referenceNumber) {
      // Get the form data object to extract the user's name
      const formDataObj = typeof formDataJson === 'string' ? JSON.parse(formDataJson) : {};
      const fullName = formDataObj.fullName || '';

      // Generate a reference number in the format CAPITALIZED_USER_NAME-ddMMYY_HHmm
      // Using underscore instead of colon to avoid file path issues
      const now = new Date();
      const day = String(now.getDate()).padStart(2, '0');
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const year = String(now.getFullYear()).slice(-2);
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');

      // Process the name part
      let namePart = 'USER';
      if (fullName && fullName.trim()) {
        const firstName = fullName.split(' ')[0];
        namePart = firstName.toUpperCase().replace(/[^A-Z]/g, '');
      }

      // Create the reference number
      referenceNumber = `${namePart}-${day}${month}${year}_${hours}${minutes}`;
      console.log('Generated new reference number on server:', referenceNumber);
    } else {
      console.log('Using reference number from form data:', referenceNumber);
    }

    // Validate the JSON data
    if (!formDataJson || typeof formDataJson !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Form data is missing or invalid'
      }, { status: 400 });
    }

    // Parse the JSON data if not already parsed
    const formDataObj = typeof formDataJson === 'string' ? JSON.parse(formDataJson) : formDataJson;

    // Update the form data object with the pdfSubmitted flag to prevent duplicate submissions
    formDataObj.pdfSubmitted = true;

    // Process the PDF file
    const pdfFile = formData.get('pdfFile') as File | null;

    // We don't need to process other files as they've already been uploaded when selected
    console.log('Not processing other files as they have already been uploaded');

    // Upload the PDF to Google Drive if available
    let googleDriveResult = null;

    if (pdfFile) {
      console.log('Uploading PDF to Google Drive:', pdfFile.name);

      // Always create a new folder for the final submission using the reference number
      const folderName = referenceNumber;
      console.log('Creating new user folder with name:', folderName);

      // Prepare upload options to create a new folder
      const uploadOptions = {
        createFolder: folderName,
        makePublic: false, // Keep the final PDF private
        fields: 'id,name,webViewLink,webContentLink'
      };

      // Upload the PDF to Google Drive in a new folder
      const pdfBuffer = Buffer.from(await pdfFile.arrayBuffer());
      googleDriveResult = await uploadToGoogleDrive(
        `${referenceNumber}.pdf`, // Use reference number in the filename
        pdfFile.type || getMimeType(pdfFile.name),
        pdfBuffer,
        uploadOptions
      );

      console.log('PDF upload result:', googleDriveResult);

      // If a folder was created, log its ID
      if (googleDriveResult.folder && googleDriveResult.folder.id) {
        console.log('User folder created with ID:', googleDriveResult.folder.id);
      } else {
        // Log the error but continue with the submission
        console.error('Failed to create user folder for PDF, but continuing with submission');
        console.log('PDF was uploaded to the default folder instead');
      }
    }

    // We don't need to upload other files to Google Drive as they've already been uploaded to the global folder
    console.log('Not uploading additional files to Google Drive as they have already been uploaded to the global folder');

    // Try to send a confirmation email if an email address is available
    let emailResult = null;
    try {
      // Extract email from form data - handle different possible structures
      let userEmail: string | null = null;

      // Log form data structure to help with debugging
      console.log('Form data structure for email extraction:', JSON.stringify({
        hasEmail: !!formDataObj.email,
        hasContactInfo: !!formDataObj.contactInfo,
        hasPersonalInfo: !!formDataObj.personalInfo,
        hasPersonal: !!formDataObj.personal
      }));

      // Try different possible locations for the email in the form data
      const possibleEmailPaths = [
        // Direct paths
        'email',
        'contactInfo.email',
        'personalInfo.contactInfo.email',
        'personal.email',
        'personalInfo.email',
        // Additional paths that might exist
        'contactInformation.email',
        'userInfo.email',
        'userInfo.contactInfo.email',
        'contact.email',
        'user.email'
      ];

      // Helper function to safely get nested properties
      const getNestedProperty = (obj: any, path: string) => {
        const parts = path.split('.');
        let current = obj;

        for (const part of parts) {
          if (current === null || current === undefined || typeof current !== 'object') {
            return undefined;
          }
          current = current[part];
        }

        return current;
      };

      // Try each path until we find a valid email
      for (const path of possibleEmailPaths) {
        const potentialEmail = getNestedProperty(formDataObj, path);

        if (potentialEmail && typeof potentialEmail === 'string' && potentialEmail.includes('@')) {
          userEmail = potentialEmail;
          console.log(`Found email at path ${path}:`, userEmail);
          break;
        }
      }

      // If still no email found, try to search for any property that looks like an email
      if (!userEmail) {
        const searchForEmail = (obj: any, prefix = '') => {
          if (!obj || typeof obj !== 'object') return;

          Object.entries(obj).forEach(([key, value]) => {
            const currentPath = prefix ? `${prefix}.${key}` : key;

            if (typeof value === 'string' && value.includes('@') && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              console.log(`Found potential email in unexpected location ${currentPath}:`, value);
              if (!userEmail) userEmail = value;
            } else if (typeof value === 'object' && value !== null) {
              searchForEmail(value, currentPath);
            }
          });
        };

        searchForEmail(formDataObj);
      }

      // Extract language preference from different possible locations
      let language: 'fr' | 'en' = 'fr'; // Default to French

      // Use the same nested property helper for language detection
      const possibleLanguagePaths = [
        'language',
        'preferences.language',
        'settings.language',
        'userPreferences.language',
        'locale',
        'preferences.locale',
        'settings.locale'
      ];

      // Try each path until we find a language setting
      for (const path of possibleLanguagePaths) {
        const potentialLanguage = getNestedProperty(formDataObj, path);

        if (potentialLanguage && typeof potentialLanguage === 'string') {
          // Normalize language value
          const normalizedLang = potentialLanguage.toLowerCase().trim();

          if (normalizedLang === 'en' ||
              normalizedLang === 'english' ||
              normalizedLang === 'anglais' ||
              normalizedLang.startsWith('en-')) {
            language = 'en';
            console.log(`Found English language preference at path ${path}`);
            break;
          } else if (normalizedLang === 'fr' ||
                    normalizedLang === 'french' ||
                    normalizedLang === 'français' ||
                    normalizedLang === 'francais' ||
                    normalizedLang.startsWith('fr-')) {
            language = 'fr';
            console.log(`Found French language preference at path ${path}`);
            break;
          }
        }
      }

      console.log(`Using language: ${language} for email communication`);

      if (userEmail) {
        console.log(`Sending confirmation email to ${userEmail} with reference ${referenceNumber}`);

        // Send confirmation email with a timeout to ensure it doesn't block form submission
        try {
          // Create a promise that resolves after a timeout
          const timeoutPromise = new Promise<any>((resolve) => {
            setTimeout(() => {
              console.warn('Email sending took too long, continuing with form submission');
              resolve({
                success: false,
                message: 'Email sending timed out but form was submitted successfully',
                error: 'Operation timed out'
              });
            }, 5000); // 5 second timeout for the entire email process
          });

          // Race the email sending against the timeout
          emailResult = await Promise.race([
            sendConfirmationEmail({
              email: userEmail,
              referenceNumber,
              language
            }),
            timeoutPromise
          ]);

          console.log('Email sending result:', emailResult);
        } catch (emailSendingError) {
          console.error('Error in email sending process:', emailSendingError);
          emailResult = {
            success: false,
            message: 'Error in email sending process',
            error: emailSendingError instanceof Error ? emailSendingError.message : 'Unknown error'
          };
        }
      } else {
        console.log('No email address found in form data, skipping confirmation email');
      }
    } catch (emailError) {
      // Log the error but don't fail the submission
      console.error('Error sending confirmation email:', emailError);
    }

    // Return success response with Google Drive information
    return NextResponse.json({
      success: true,
      referenceNumber,
      message: `Form submitted successfully with reference number: ${referenceNumber}`,
      emailSent: emailResult?.success || false,
      ...(googleDriveResult || {})
    }, { status: 200 });

  } catch (error) {
    console.error('Error processing form submission:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    }, { status: 500 });
  }
}
