import { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { getImmobilierServiceBySlug, getAllImmobilierServices } from "@/lib/immobilier-services-data";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Home, ChevronRight, CheckCircle, Target, Award } from "lucide-react";

interface PageProps {
  params: {
    slug: string;
  };
}

export async function generateStaticParams() {
  const services = getAllImmobilierServices();
  return services.map((service) => ({
    slug: service.slug,
  }));
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const service = getImmobilierServiceBySlug(params.slug);
  
  if (!service) {
    return {
      title: "Service non trouvé | <PERSON> Consulting"
    };
  }

  return {
    title: `${service.title} | <PERSON> Consulting`,
    description: service.shortDescription,
    keywords: [
      service.title,
      service.category,
      "immobilier Cameroun",
      "Charlie Oscar"
    ],
    openGraph: {
      title: `${service.title} | Charlie Oscar Consulting`,
      description: service.shortDescription,
      type: "website",
      locale: "fr_FR",
      siteName: "Charlie Oscar Consulting"
    },
    alternates: {
      canonical: `/immobilier/services/${service.slug}`
    }
  };
}

export default function ImmobilierServicePage({ params }: PageProps) {
  const service = getImmobilierServiceBySlug(params.slug);

  if (!service) {
    notFound();
  }

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Simple':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Modéré':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Complexe':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-50 to-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
              <Link href="/" className="hover:text-primary transition-colors flex items-center">
                <Home className="w-4 h-4 mr-1" />
                Accueil
              </Link>
              <ChevronRight className="w-4 h-4" />
              <Link href="/immobilier" className="hover:text-primary transition-colors">
                Immobilier
              </Link>
              <ChevronRight className="w-4 h-4" />
              <Link href="/immobilier/services" className="hover:text-primary transition-colors">
                Services
              </Link>
              <ChevronRight className="w-4 h-4" />
              <span className="text-primary">{service.title}</span>
            </nav>

            <div className="space-y-8">
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Badge className="bg-primary/10 text-primary border-primary/20">
                    {service.category}
                  </Badge>
                  <Badge className={getComplexityColor(service.complexity)}>
                    {service.complexity}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {service.estimatedDuration}
                  </Badge>
                </div>
                
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                  {service.title}
                </h1>
                
                <p className="text-xl text-gray-600 leading-relaxed">
                  {service.shortDescription}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg" className="group">
                  <Link href="/formulaire" className="flex items-center">
                    Demander un devis
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                
                <Button asChild size="lg" variant="outline">
                  <Link href="/contact">
                    Consultation gratuite
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Description */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto space-y-12">
            {/* Detailed Description */}
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-gray-900">
                  Description du service
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed text-lg">
                  {service.detailedDescription}
                </p>
              </CardContent>
            </Card>

            {/* What it implies */}
            <Card className="border-l-4 border-l-blue-500 bg-blue-50">
              <CardHeader>
                <CardTitle className="flex items-center text-xl text-gray-900">
                  <Target className="w-6 h-6 text-blue-500 mr-3" />
                  Ce que cela implique
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {service.implications.map((implication, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-700">{implication}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Results */}
            <Card className="border-l-4 border-l-green-500 bg-green-50">
              <CardHeader>
                <CardTitle className="flex items-center text-xl text-gray-900">
                  <Award className="w-6 h-6 text-green-500 mr-3" />
                  Résultats de ce service
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {service.results.map((result, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-700">{result}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Intéressé par ce service ?
            </h2>
            
            <p className="text-xl text-primary-foreground/90">
              Contactez nos experts pour une consultation personnalisée et découvrez 
              comment nous pouvons vous aider avec votre projet immobilier.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" variant="secondary" className="group">
                <Link href="/formulaire" className="flex items-center">
                  Demander un devis pour ce service
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
                <Link href="/contact">
                  Parler à un expert
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Related Services */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center space-y-4 mb-12">
              <h2 className="text-3xl font-bold text-gray-900">
                Autres services immobiliers
              </h2>
              
              <p className="text-gray-600">
                Découvrez nos autres services qui pourraient vous intéresser
              </p>
            </div>

            <div className="text-center">
              <Button asChild size="lg" variant="outline">
                <Link href="/immobilier/services" className="flex items-center">
                  Voir tous nos services immobiliers
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
