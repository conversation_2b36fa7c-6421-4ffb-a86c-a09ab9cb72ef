"use client";

import React from "react";
import { useOnlineStatus } from "../contexts/online-status-context";
import { useLanguage } from "../translations/language-context";

interface OnlineStatusIndicatorProps {
  showText?: boolean;
  className?: string;
}

export function OnlineStatusIndicator({ 
  showText = true, 
  className = "" 
}: OnlineStatusIndicatorProps) {
  const { isOnline } = useOnlineStatus();
  const { t } = useLanguage();

  return (
    <div 
      className={`flex items-center ${className}`}
      data-testid="online-status-indicator"
    >
      <div 
        className={`h-2.5 w-2.5 rounded-full mr-1.5 ${
          isOnline ? "bg-green-500" : "bg-red-500"
        }`}
      />
      {showText && (
        <span className="text-sm">
          {isOnline 
            ? t('common.online') 
            : t('common.offline')}
        </span>
      )}
    </div>
  );
}
