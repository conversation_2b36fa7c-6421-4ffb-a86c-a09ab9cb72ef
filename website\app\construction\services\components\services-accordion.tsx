"use client";

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Star, Eye, Ruler, Cog, Users, Zap, Building2, CheckCircle, Hammer } from "lucide-react";

interface ServicesAccordionProps {
  servicesData: {
    badge: {
      icon: string;
      text: string;
    };
    title: string;
    description: string;
    categories: Array<{
      id: string;
      title: string;
      description: string;
      icon: string;
      color: string;
      content: {
        introduction: string;
        services: Array<{
          title: string;
          description: string;
          icon: string;
          features?: Array<{
            title: string;
            subtitle: string;
          }>;
        }>;
      };
    }>;
  };
  openAccordion: string;
  onValueChange: (value: string) => void;
}

const iconMap = {
  Eye,
  Ruler,
  Cog,
  Users,
  Zap,
  Building2,
  CheckCircle,
  Hammer,
  Star
};

export default function ServicesAccordion({ servicesData, openAccordion, onValueChange }: ServicesAccordionProps) {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center space-y-4 mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium border border-orange-200">
              <Star className="w-4 h-4 mr-2" />
              {servicesData.badge.text}
            </div>

            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {servicesData.title}
            </h2>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {servicesData.description}
            </p>
          </div>

          {/* Accordéons des Services */}
          <Accordion 
            type="single" 
            collapsible 
            className="space-y-4"
            value={openAccordion}
            onValueChange={onValueChange}
          >
            {servicesData.categories.map((category) => {
              const IconComponent = iconMap[category.icon as keyof typeof iconMap];
              const colorClasses: Record<string, string> = {
                orange: "border-orange-200 bg-orange-50",
                blue: "border-blue-200 bg-blue-50",
                green: "border-green-200 bg-green-50",
                purple: "border-purple-200 bg-purple-50",
                yellow: "border-yellow-200 bg-yellow-50"
              };

              return (
                <AccordionItem
                  key={category.id}
                  id={category.id}
                  value={category.id}
                  className={`border-2 rounded-lg ${colorClasses[category.color]}`}
                >
                  <AccordionTrigger className="px-6 py-4 hover:no-underline">
                    <div className="flex items-center space-x-4 text-left">
                      <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                        category.color === 'orange' ? 'bg-orange-100' :
                        category.color === 'blue' ? 'bg-blue-100' :
                        category.color === 'green' ? 'bg-green-100' :
                        category.color === 'purple' ? 'bg-purple-100' :
                        'bg-yellow-100'
                      }`}>
                        <IconComponent className={`w-6 h-6 ${
                          category.color === 'orange' ? 'text-orange-600' :
                          category.color === 'blue' ? 'text-blue-600' :
                          category.color === 'green' ? 'text-green-600' :
                          category.color === 'purple' ? 'text-purple-600' :
                          'text-yellow-600'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-gray-900 mb-1">
                          {category.title}
                        </h3>
                        <p className="text-gray-600 text-sm">
                          {category.description}
                        </p>
                      </div>
                    </div>
                  </AccordionTrigger>

                  <AccordionContent className="px-6 pb-6">
                    <div className="space-y-6 mt-4">
                      <p className="text-gray-700 leading-relaxed">
                        {category.content.introduction}
                      </p>
                      
                      <div className={`grid ${category.content.services.length > 1 ? 'md:grid-cols-2' : ''} gap-6`}>
                        {category.content.services.map((service, serviceIndex) => {
                          const ServiceIcon = iconMap[service.icon as keyof typeof iconMap];
                          
                          return (
                            <div key={serviceIndex} className={`bg-white rounded-lg p-6 border border-${category.color}-200`}>
                              <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                                <div className={`w-8 h-8 bg-${category.color}-100 rounded-lg flex items-center justify-center mr-3`}>
                                  <ServiceIcon className={`w-4 h-4 text-${category.color}-600`} />
                                </div>
                                {service.title}
                              </h4>
                              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                                {service.description}
                              </p>
                              
                              {service.features && (
                                <div className="grid md:grid-cols-3 gap-4 mt-4">
                                  {service.features.map((feature, featureIndex) => (
                                    <div key={featureIndex} className={`text-center p-3 bg-${category.color}-50 rounded-lg`}>
                                      <div className="text-sm font-medium text-gray-900">{feature.title}</div>
                                      <div className="text-xs text-gray-600 mt-1">{feature.subtitle}</div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              );
            })}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
