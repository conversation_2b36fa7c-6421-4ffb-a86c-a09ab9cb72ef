"use client";

import React, { useState } from "react";
import { Plus, Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import { DragDropZone } from "./drag-drop-zone";
import { FilePreview } from "./file-preview";
import { UploadProvider, useUploadContext } from "./upload-context";
import { useLanguage } from "@/app/translations/language-context";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { NotificationProvider, useNotification, NotificationType } from "./notification-popover";

// Helper function to create file previews
const createFilePreview = (file: File): Promise<string | null> => {
  return new Promise((resolve) => {
    if (file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = () => {
        resolve(reader.result as string);
      };
      reader.onerror = () => {
        resolve(null);
      };
      reader.readAsDataURL(file);
    } else {
      resolve(null);
    }
  });
};

function FileUploadFormContent() {
  const { t } = useLanguage();
  const {
    mainFile,
    relatedFiles,
    setMainFile,
    addRelatedFile,
    removeRelatedFile,
    updateFileName,
    clearAllFiles,
  } = useUploadContext();
  const { showNotification } = useNotification();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newFileName, setNewFileName] = useState("");
  const [showNewFileInput, setShowNewFileInput] = useState(false);
  const [mainFileError, setMainFileError] = useState<string | null>(null);
  const [relatedFileError, setRelatedFileError] = useState<string | null>(null);

  // Handle main PDF file selection
  const handleMainFileSelect = async (files: File[], errors?: string[]) => {
    // Clear previous errors
    setMainFileError(null);

    // If we have validation errors from the DragDropZone, display them
    if (errors && errors.length > 0) {
      setMainFileError(errors[0]); // Show the first error
      return;
    }

    if (files.length > 0) {
      const file = files[0];

      // Validate file size (this is now handled by DragDropZone)
      // Validate file type (this is now handled by DragDropZone)

      const preview = await createFilePreview(file);

      // Extract the file name without extension to use as default
      const fileNameWithoutExt = file.name.replace(/\.[^/.]+$/, "");

      // Validate file name (minimum 3 characters)
      let validationError;
      if (fileNameWithoutExt.trim().length < 3) {
        validationError = t('common.fileNameTooShort');
      }

      setMainFile({
        file,
        preview,
        status: "idle",
        name: fileNameWithoutExt, // Default to file name without extension
        validationError
      });
    }
  };

  // Handle related file selection
  const handleRelatedFileSelect = async (files: File[], errors?: string[]) => {
    // Clear previous errors
    setRelatedFileError(null);

    // If we have validation errors from the DragDropZone, display them
    if (errors && errors.length > 0) {
      setRelatedFileError(errors[0]); // Show the first error
      return;
    }

    if (files.length > 0) {
      const file = files[0];

      // Validate file size (this is now handled by DragDropZone)
      // Validate file type (this is now handled by DragDropZone)

      const preview = await createFilePreview(file);

      // Extract the file name without extension to use as default
      const fileNameWithoutExt = file.name.replace(/\.[^/.]+$/, "");

      // Use custom name or default to file name without extension
      const fileName = newFileName || fileNameWithoutExt;

      // Validate file name (minimum 3 characters)
      let validationError;
      if (fileName.trim().length < 3) {
        validationError = t('common.fileNameTooShort');
      }

      addRelatedFile({
        file,
        preview,
        status: "idle",
        name: fileName,
        validationError
      });

      // Reset the input
      setNewFileName("");
      setShowNewFileInput(false);
    }
  };

  // This function is no longer needed as we're using DragDropZone

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!mainFile) {
      showNotification(t('uploadPage.mainFileRequired'), 'warning');
      return;
    }

    // Check if main file has validation errors
    if (mainFile.validationError) {
      showNotification(t('uploadPage.mainFileValidationError'), 'warning');
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate a reference number with the FER prefix
      const now = new Date();
      const day = String(now.getDate()).padStart(2, '0');
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const year = String(now.getFullYear()).slice(-2);
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');

      // Format: FER-ddMMyy-HHmm (e.g., FER-271023-1430)
      const referenceNumber = `FER-${day}${month}${year}-${hours}${minutes}`;

      // Create a FormData object to send to the API
      const apiFormData = new FormData();

      // Add the main PDF file
      apiFormData.append('mainFile', mainFile.file);

      // Add all related files that don't have validation errors
      let validFileCount = 0;
      relatedFiles.forEach((file) => {
        if (!file.validationError) {
          apiFormData.append(`relatedFile_${validFileCount}`, file.file);
          apiFormData.append(`relatedFileName_${validFileCount}`, file.name || '');
          validFileCount++;
        }
      });

      // Create a JSON object with metadata
      const formDataObj = {
        referenceNumber,
        timestamp: new Date().toISOString(),
        mainFileName: mainFile.name || mainFile.file.name.replace(/\.[^/.]+$/, ""),
        totalFiles: 1 + validFileCount // Only count valid files
      };

      // Add the form data as JSON
      apiFormData.append('formData', JSON.stringify(formDataObj));

      // Store the reference number in localStorage for future use
      localStorage.setItem('referenceNumber', referenceNumber);

      // Send the request to the new batch upload API
      const response = await fetch('/api/formulaire/batch-upload', {
        method: 'POST',
        body: apiFormData,
      });

      if (!response.ok) {
        throw new Error(`Submission failed: ${response.statusText}`);
      }

      // Parse the response
      const result = await response.json();

      if (result.success) {
        // Show success message with reference number
        showNotification(t('uploadPage.uploadSuccessWithRef').replace('{ref}', referenceNumber), 'success');

        // Log the result for debugging
        console.log('Form submission result:', result);

        // Store the folder information if available
        if (result.folder) {
          localStorage.setItem('userFolder', JSON.stringify({
            id: result.folder.id,
            name: result.folder.name,
            webViewLink: result.folder.webViewLink
          }));
        }

        // Clear the form
        clearAllFiles();

        // Clear localStorage except for the user folder info
        localStorage.removeItem('relatedFiles');
        localStorage.removeItem('uploadedFilesForSubmission');
        localStorage.removeItem('referenceNumber');
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Error submitting form:', error);

      // Provide a more user-friendly error message
      let errorMessage = 'An unknown error occurred while uploading your files';

      if (error instanceof Error) {
        // Handle specific error types
        if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'A network error occurred. Please check your internet connection and try again.';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'The upload timed out. Please try again with smaller files or a better connection.';
        } else if (error.message.includes('413') || error.message.includes('Payload Too Large')) {
          errorMessage = 'The files are too large. Please reduce the file size and try again.';
        } else if (error.message.length > 0 && error.message.length < 100) {
          // Only use the error message if it's reasonably short (likely user-friendly)
          errorMessage = error.message;
        }
      }

      showNotification(errorMessage, 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Main PDF File Section */}
      <div className="mb-8">
        <h2 className="text-lg font-medium mb-2">{t('uploadPage.mainFile')}</h2>
        <p className="text-sm text-gray-600 mb-4">{t('uploadPage.mainFileDescription')}</p>

        {mainFile ? (
          <div className={`border rounded-md p-4 ${mainFile.validationError ? 'border-red-300 bg-red-50' : ''}`}>
            <FilePreview
              file={mainFile}
              onRemove={() => setMainFile(null)}
            />
            {mainFile.validationError && (
              <div className="mt-2 text-sm text-red-600">
                {mainFile.validationError}
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            <DragDropZone
              onFileSelect={handleMainFileSelect}
              accept=".pdf"
              multiple={false}
              maxSize={2 * 1024 * 1024} // 2MB
              className="cursor-pointer"
            >
              <div className="flex flex-col items-center justify-center py-6">
                <Upload className="h-10 w-10 text-gray-400 mb-2" />
                <p className="text-sm font-medium">{t('uploadPage.dropMainFile')}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {t('common.maxFileSize').replace('{maxSize}', '2')}
                </p>
              </div>
            </DragDropZone>
            {mainFileError && (
              <div className="text-sm text-red-600 p-2 bg-red-50 border border-red-200 rounded">
                {mainFileError}
              </div>
            )}
          </div>
        )}
      </div>

      <Separator className="my-6" />

      {/* Related Files Section */}
      <div className="mb-8">
        <h2 className="text-lg font-medium mb-2">{t('uploadPage.relatedFiles')}</h2>
        <p className="text-sm text-gray-600 mb-4">{t('uploadPage.relatedFilesDescription')}</p>

        {/* File list */}
        {relatedFiles.length > 0 && (
          <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            {relatedFiles.map((file, index) => (
              <div key={`${file.file.name}-${index}`} className={`border rounded-md p-4 ${file.validationError ? 'border-red-300 bg-red-50' : ''}`}>
                <div className="mb-3">
                  <Label htmlFor={`file-name-${index}`} className="text-sm font-medium">
                    {t('uploadPage.fileName')}
                  </Label>
                  <Input
                    id={`file-name-${index}`}
                    value={file.name || ''}
                    onChange={(e) => updateFileName('related', index, e.target.value)}
                    className={`mt-1 ${file.validationError ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''}`}
                    placeholder={t('uploadPage.fileNamePlaceholder')}
                  />
                  {file.validationError && (
                    <div className="mt-1 text-sm text-red-600">
                      {file.validationError}
                    </div>
                  )}
                </div>
                <FilePreview
                  file={file}
                  onRemove={() => removeRelatedFile(index)}
                />
              </div>
            ))}
          </div>
        )}

        {/* New file input */}
        {showNewFileInput && (
          <div className="mb-4 p-4 border border-dashed rounded-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="new-file-name" className="text-sm font-medium">
                  {t('uploadPage.fileName')}
                </Label>
                <Input
                  id="new-file-name"
                  value={newFileName}
                  onChange={(e) => setNewFileName(e.target.value)}
                  className="mt-1"
                  placeholder={t('uploadPage.fileNamePlaceholder')}
                />
              </div>

              <div>
                <Label className="text-sm font-medium mb-1 block">
                  {t('uploadPage.selectFile')}
                </Label>
                <div className="space-y-2">
                  <DragDropZone
                    onFileSelect={handleRelatedFileSelect}
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    multiple={false}
                    maxSize={2 * 1024 * 1024} // 2MB
                    className="cursor-pointer h-[100px]"
                  >
                    <div className="flex flex-col items-center justify-center h-full">
                      <Upload className="h-6 w-6 text-gray-400 mb-1" />
                      <p className="text-xs text-gray-500">
                        {t('common.maxFileSize').replace('{maxSize}', '2')}
                      </p>
                    </div>
                  </DragDropZone>
                  {relatedFileError && (
                    <div className="text-sm text-red-600 p-2 bg-red-50 border border-red-200 rounded">
                      {relatedFileError}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-3">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowNewFileInput(false)}
              >
                {t('common.cancel')}
              </Button>
            </div>
          </div>
        )}

        {/* Add file button */}
        {!showNewFileInput && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              type="button"
              variant="outline"
              className="border-dashed py-4 h-auto"
              onClick={() => setShowNewFileInput(true)}
            >
              <div className="flex items-center justify-center">
                <Plus className="h-5 w-5 text-gray-400 mr-2" />
                <p className="text-sm font-medium">{t('uploadPage.addFiles')}</p>
              </div>
            </Button>
          </div>
        )}
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={!mainFile || isSubmitting}
          className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
        >
          {isSubmitting ? t('common.uploading') : t('common.submit')}
        </Button>
      </div>
    </form>
  );
}

export function FileUploadForm() {
  return (
    <UploadProvider>
      <NotificationProvider>
        <FileUploadFormContent />
      </NotificationProvider>
    </UploadProvider>
  );
}
