import Link from "next/link";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

const sitemapLinks = [
  { title: "Accueil", href: "/" },
  { title: "À propos", href: "/a-propos" },
  { title: "Contact", href: "/contact" },
  { title: "Formulaire", href: "/formulaire" },
  { title: "Foncier", href: "/foncier" },
  { title: "Immobili<PERSON>", href: "/immobilier" },
  { title: "Construction", href: "/construction" },
  { title: "Blog", href: "/blog" },
  { title: "Nos projets", href: "/projets" },
  { title: "Mentions légales", href: "/mentions-legales" },
  { title: "Politique de confidentialité", href: "/confidentialite" },
  { title: "Conditions d'utilisation", href: "/conditions" },
  { title: "Cookies", href: "/cookies" },
];

export default function SitemapPage() {
  return (
    <div className="container mx-auto py-16 px-4">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-center mb-2">Plan du site</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-4">
            {sitemapLinks.map((link) => (
              <li key={link.href}>
                <Link href={link.href} className="text-primary text-lg hover:underline">
                  {link.title}
                </Link>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
