"use client";

import React, { useState, useEffect } from "react";
import { X, FileText, Image, File, Loader2 } from "lucide-react";
import { UploadedFile } from "./upload-context";
import { useLanguage } from "@/app/translations/language-context";
import { Progress } from "@/components/ui/progress";

interface FilePreviewProps {
  file: UploadedFile;
  onRemove?: () => void;
  showProgress?: boolean;
}

export function FilePreview({ file, onRemove, showProgress = true }: FilePreviewProps) {
  const { t } = useLanguage();
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [pdfLoading, setPdfLoading] = useState(true);

  // Create and clean up object URL for PDF preview
  useEffect(() => {
    if (file.file.type === "application/pdf") {
      setPdfLoading(true);
      const url = URL.createObjectURL(file.file);
      setPdfUrl(url);

      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [file.file]);

  // Determine file type icon
  const getFileIcon = () => {
    const fileType = file.file.type;

    if (fileType.startsWith("image/")) {
      return <Image className="h-5 w-5" />;
    } else if (fileType === "application/pdf") {
      return <FileText className="h-5 w-5" />;
    } else {
      return <File className="h-5 w-5" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    }
  };

  // Get file extension
  const getFileExtension = (filename: string): string => {
    return filename.split('.').pop()?.toUpperCase() || '';
  };

  // Get file name without extension
  const getFileNameWithoutExtension = (filename: string): string => {
    return filename.replace(/\.[^/.]+$/, "");
  };

  return (
    <div className="border rounded-md p-3 mb-3 bg-background">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0 h-8 w-8 bg-gray-100 rounded flex items-center justify-center text-xs font-medium uppercase">
            {getFileExtension(file.file.name)}
          </div>

          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">
              {file.name || getFileNameWithoutExtension(file.file.name)}
              <span className="text-gray-500">.{getFileExtension(file.file.name).toLowerCase()}</span>
            </p>
            <p className="text-xs text-gray-500">{formatFileSize(file.file.size)}</p>
          </div>
        </div>

        {file.status === "uploading" ? (
          <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />
        ) : (
          onRemove && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <X className="h-5 w-5" />
            </button>
          )
        )}
      </div>

      {/* Image preview */}
      {file.preview && file.file.type.startsWith("image/") && (
        <div className="mt-2">
          <img
            src={file.preview}
            alt={file.file.name}
            className="max-h-32 max-w-full object-contain rounded border"
          />
        </div>
      )}

      {/* PDF preview */}
      {file.file.type === "application/pdf" && pdfUrl && (
        <div className="mt-2">
          <div className="relative overflow-hidden rounded border">
            {pdfLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50 z-10">
                <Loader2 className="h-8 w-8 text-primary animate-spin" />
              </div>
            )}
            <iframe
              src={pdfUrl + "#toolbar=0&navpanes=0&scrollbar=0&view=FitH"}
              title={file.file.name}
              className="w-full h-[300px] bg-white"
              style={{ border: 'none' }}
              onLoad={() => setPdfLoading(false)}
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">{t('uploadPage.pdfPreview')}</p>
        </div>
      )}

      {showProgress && file.status === "uploading" && (
        <div className="mt-2">
          <Progress value={file.progress || 0} className="h-1" />
          <p className="text-xs text-gray-500 mt-1">
            {t('common.uploading')}: {file.progress || 0}%
          </p>
        </div>
      )}

      {file.status === "error" && file.error && (
        <p className="text-xs text-red-500 mt-1">{file.error}</p>
      )}

      {file.status === "success" && (
        <p className="text-xs text-green-500 mt-1">{t('common.uploaded')}</p>
      )}
    </div>
  );
}
