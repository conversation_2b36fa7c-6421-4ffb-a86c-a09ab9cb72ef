"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { ArrowRight, Building2, Home, Hammer } from "lucide-react";

export function DomainsSection({ content, section }: { content: any[]; section: any }) {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const domains = (content || []).map((domain, idx) => ({
    id: idx + 1,
    title: domain.name,
    description: domain.description,
    icon: domain.name === "Foncier" ? Building2 : domain.name === "Immobilier" ? Home : Hammer,
    href: domain.url,
    featured: idx === 0,
    services: domain.services,
    bgColor: idx === 0 ? "bg-primary/10" : "bg-gray-100",
    borderColor: idx === 0 ? "border-primary" : "border-gray-200",
    iconColor: "text-primary" 
  }));

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
              {section?.badge}
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {section?.title}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {section?.subtitle}
            </p>
          </div>

          {/* Domains Grid */}
          <div className="grid lg:grid-cols-3 gap-8">
            {domains.map((domain, index) => {
              const Icon = domain.icon;
              const isFeatured = domain.featured;
              const isHovered = hoveredCard === domain.id;
              
              return (
                <div
                  key={domain.id}
                  className={`transition-all duration-700 ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                  }`}
                  style={{ transitionDelay: `${index * 200}ms` }}
                >
                  <Card
                    className={`h-full transition-all duration-300 hover:shadow-xl cursor-pointer group ${
                      isFeatured ? 'lg:scale-105 shadow-lg' : 'hover:scale-105'
                    } ${domain.borderColor} border-2 ${isHovered ? 'shadow-2xl' : ''}`}
                    onMouseEnter={() => setHoveredCard(domain.id)}
                    onMouseLeave={() => setHoveredCard(null)}
                  >
                    <CardHeader className={`${domain.bgColor} transition-colors duration-300`}>
                      <div className="flex items-center justify-between">
                        <div className={`w-12 h-12 ${domain.bgColor} rounded-xl flex items-center justify-center transition-transform duration-300 group-hover:scale-110`}>
                          <Icon className={`w-6 h-6 ${domain.iconColor}`} />
                        </div>
                        {isFeatured && (
                          <div className="bg-primary text-white text-xs px-2 py-1 rounded-full font-medium">
                            Expertise principale
                          </div>
                        )}
                      </div>
                      
                      <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                        {domain.title}
                      </CardTitle>
                      
                      <CardDescription className="text-gray-600">
                        {domain.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-6">
                      {/* Services List */}
                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-900 text-sm">Services clés :</h4>
                        <ul className="space-y-2">
                          {domain.services.map((service, serviceIndex) => (
                            <li key={serviceIndex} className="flex items-start space-x-2 text-sm text-gray-600">
                              <div className={`w-1.5 h-1.5 ${domain.iconColor.replace('text-', 'bg-')} rounded-full mt-2 flex-shrink-0`}></div>
                              <span>{service}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* CTA Button */}
                      <Button asChild variant={isFeatured ? "default" : "outline"} className="w-full group/btn">
                        <Link href={domain.href} className="flex items-center justify-center">
                          {isFeatured ? "Découvrir nos services" : "En savoir plus"}
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
