"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";

// Define the context type
interface OnlineStatusContextType {
  isOnline: boolean;
}

// Create the context with a default value
const OnlineStatusContext = createContext<OnlineStatusContextType>({ isOnline: true });

// Provider component
export function OnlineStatusProvider({ children }: { children: ReactNode }) {
  // Default to true (optimistic) and update once we're on the client
  const [isOnline, setIsOnline] = useState<boolean>(true);

  useEffect(() => {
    // Update the online status initially
    setIsOnline(navigator.onLine);

    // Event handlers for online and offline events
    const handleOnline = () => {
      console.log('Network status: Online');
      setIsOnline(true);
    };

    const handleOffline = () => {
      console.log('Network status: Offline');
      setIsOnline(false);
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <OnlineStatusContext.Provider value={{ isOnline }}>
      {children}
    </OnlineStatusContext.Provider>
  );
}

// Custom hook to use the online status context
export function useOnlineStatus() {
  const context = useContext(OnlineStatusContext);
  if (context === undefined) {
    throw new Error("useOnlineStatus must be used within an OnlineStatusProvider");
  }
  return context;
}
