# CMS Requirements - <PERSON> Consulting

## Overview
Custom Content Management System for managing website content including text, images, videos, files, and PDFs.

## Core Features Required

### 1. Content Types
- **Text Content**: Page content, service descriptions, blog posts
- **Images**: Hero images, service images, gallery images, team photos
- **Videos**: Promotional videos, testimonials, project showcases
- **Files**: PDF documents, brochures, forms, legal documents
- **Metadata**: SEO titles, descriptions, keywords

### 2. Content Areas to Manage
- **Homepage**: Hero section, services overview, testimonials
- **Service Pages**: Descriptions, images, documents, pricing
- **About Page**: Company info, team members, mission/vision
- **Contact Information**: Address, phone, email, social media
- **Legal Pages**: Terms, privacy policy, cookies
- **Blog/News**: Articles, announcements, updates

### 3. User Roles & Permissions
- **Super Admin**: Full access to all content and settings
- **Content Manager**: Can edit content but not system settings
- **Editor**: Can edit specific sections assigned to them
- **Viewer**: Read-only access for review purposes

### 4. Technical Requirements
- **Framework**: Next.js 14 with App Router
- **Database**: PostgreSQL or MongoDB for content storage
- **File Storage**: Google Drive integration (already implemented)
- **Authentication**: NextAuth.js or custom auth system
- **UI Framework**: Tailwind CSS + shadcn/ui components
- **Rich Text Editor**: TipTap or similar WYSIWYG editor

### 5. Content Management Features
- **WYSIWYG Editor**: Rich text editing with formatting
- **Media Library**: Upload, organize, and manage media files
- **SEO Management**: Meta tags, Open Graph, structured data
- **Version Control**: Track changes and revert if needed
- **Preview Mode**: Preview changes before publishing
- **Bulk Operations**: Mass edit, delete, or update content
- **Search & Filter**: Find content quickly
- **Content Scheduling**: Schedule content publication

### 6. File Management
- **Upload Interface**: Drag & drop file uploads
- **File Organization**: Folders, tags, categories
- **File Types**: Support for images, videos, PDFs, documents
- **File Optimization**: Automatic image compression and resizing
- **CDN Integration**: Fast content delivery
- **Access Control**: Public/private file permissions

### 7. API & Integration
- **REST API**: For external integrations
- **Webhooks**: Notify external systems of changes
- **Import/Export**: Backup and restore content
- **Multi-language**: Support for French/English content

### 8. Security Features
- **Authentication**: Secure login system
- **Authorization**: Role-based access control
- **Audit Logs**: Track all content changes
- **Data Validation**: Prevent malicious content
- **Backup System**: Regular automated backups

### 9. Performance Requirements
- **Fast Loading**: Optimized content delivery
- **Caching**: Redis or similar for performance
- **Image Optimization**: WebP conversion, lazy loading
- **Database Optimization**: Efficient queries and indexing

### 10. User Experience
- **Intuitive Interface**: Easy-to-use admin dashboard
- **Mobile Responsive**: Works on all devices
- **Real-time Updates**: Live preview of changes
- **Keyboard Shortcuts**: Power user features
- **Help Documentation**: Built-in help and tutorials

## Implementation Plan (Tomorrow's Work)

### Phase 1: Foundation (Day 1)
1. Set up database schema
2. Create authentication system
3. Build basic admin layout
4. Implement user management

### Phase 2: Content Management (Day 1-2)
1. Create content models
2. Build WYSIWYG editor
3. Implement media library
4. Add file upload system

### Phase 3: Advanced Features (Day 2-3)
1. SEO management tools
2. Version control system
3. Preview functionality
4. API endpoints

### Phase 4: Polish & Testing (Day 3)
1. UI/UX improvements
2. Performance optimization
3. Security testing
4. Documentation

## File Structure (Proposed)
```
website/
├── app/
│   ├── admin/
│   │   ├── dashboard/
│   │   ├── content/
│   │   ├── media/
│   │   ├── users/
│   │   └── settings/
│   └── api/
│       ├── admin/
│       ├── content/
│       └── media/
├── components/
│   ├── admin/
│   │   ├── layout/
│   │   ├── forms/
│   │   ├── editors/
│   │   └── media/
│   └── cms/
├── lib/
│   ├── cms/
│   │   ├── auth.ts
│   │   ├── content.ts
│   │   ├── media.ts
│   │   └── database.ts
│   └── validations/
└── types/
    └── cms.ts
```

## Database Schema (Preliminary)
- **users**: Admin users and permissions
- **content**: Page content and metadata
- **media**: File information and metadata
- **revisions**: Content version history
- **settings**: System configuration

## Next Steps
1. Review and approve requirements
2. Set up development environment
3. Choose database solution
4. Begin implementation

---
*This document will be updated as requirements evolve during development.*
