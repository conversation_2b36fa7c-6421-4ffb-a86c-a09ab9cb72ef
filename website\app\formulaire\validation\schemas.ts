import { z } from "zod";

/**
 * Minimum length for text fields
 */
const MIN_TEXT_LENGTH = 3;

/**
 * Regular expression for validating phone numbers
 * Accepts formats like:
 * - +123456789
 * - +123 456 789
 * - ************
 * - (*************
 * - +237 6 82 34 67 89
 */
const PHONE_REGEX = /^\+\d{1,3}[-.\s]?([\d\s\-().]{8,})$/;

/**
 * Regular expression for validating email addresses
 */
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

// Note: We're using direct translation keys in the schema
// The actual translation happens in the components

/**
 * Schema for Step 1: Personal Information
 */
export const personalInfoSchema = z.object({
  // Required fields
  consent: z.boolean().refine(val => val === true, {
    message: "validation.consent",
  }),
  fullName: z.string().min(MIN_TEXT_LENGTH+2, {
    message: "validation.minLength",
  }),
  gender: z.enum(["Masculin", "Féminin"], {
    required_error: "validation.selection",
  }),
  birthDate: z.string().min(1, {
    message: "validation.date",
  }),
  birthPlace: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }),
  nationality: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }),
  address: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }),
  primaryPhone: z.string().regex(PHONE_REGEX, {
    message: "validation.phone",
  }),

  // Optional fields
  profession: z.string().optional(),
  secondaryPhone: z.string().regex(PHONE_REGEX, {
    message: "validation.phone",
  }).optional().or(z.literal("")),
  email: z.string().regex(EMAIL_REGEX, {
    message: "validation.email",
  }).optional().or(z.literal("")),

  // File upload fields
  idDocument: z.any().optional(), // We'll validate this separately
});

/**
 * Schema for Step 2: Emergency Contacts and Procedure
 */
export const emergencyProcedureSchema = z.object({
  // Required fields
  emergencyContact1Name: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }),
  emergencyContact1Phone: z.string().regex(PHONE_REGEX, {
    message: "validation.phone",
  }),
  emergencyContact1Relation: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }),
  landStatus: z.string().min(1, {
    message: "validation.selection",
  }),
  procedureTypes: z.array(z.string()).min(1, {
    message: "validation.selection",
  }),

  // Conditional fields
  otherLandStatus: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }).optional().or(z.literal("")).refine(
    (_val) => {
      // This will be validated in the component based on landStatus value
      return true;
    },
    {
      message: "validation.required",
    }
  ),
  otherProcedureType: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }).optional().or(z.literal("")).refine(
    (_val) => {
      // This will be validated in the component based on procedureTypes value
      return true;
    },
    {
      message: "validation.required",
    }
  ),

  // Optional fields
  emergencyContact2Name: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }).optional().or(z.literal("")),
  emergencyContact2Phone: z.string().regex(PHONE_REGEX, {
    message: "validation.phone",
  }).optional().or(z.literal("")),
  emergencyContact2Relation: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }).optional().or(z.literal("")),
  additionalInfo: z.string().optional(),
});

/**
 * Schema for Step 3: Documents and Location
 */
export const documentsLocationSchema = z.object({
  // Required fields
  zoneType: z.enum(["Urbaine", "Rurale"], {
    required_error: "validation.selection",
  }),
  region: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }),
  department: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }),

  // Required fields (continued)
  subdivision: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }),
  neighborhood: z.string().min(MIN_TEXT_LENGTH, {
    message: "validation.minLength",
  }),
  area: z.string().min(1, {
    message: "validation.required",
  }),

  // Optional fields
  availableDocuments: z.array(z.string()).optional(),
  uploadedDocuments: z.array(z.any()).optional(), // We'll validate this separately
  documentsDetails: z.string().optional(),
  locationDetails: z.string().optional(),
});

/**
 * Schema for Step 4: Summary
 */
export const summarySchema = z.object({
  // Required fields
  finalConsent: z.boolean().refine(val => val === true, {
    message: "validation.consent",
  }),

  // Optional fields
  additionalComments: z.string().optional(),
});

/**
 * Combined schema for the entire form
 */
export const formSchema = z.object({
  ...personalInfoSchema.shape,
  ...emergencyProcedureSchema.shape,
  ...documentsLocationSchema.shape,
  ...summarySchema.shape,
});

/**
 * Type for validation errors
 */
export type ValidationErrors = {
  [key: string]: string;
};
