"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Shield, CreditCard, TrendingUp, ArrowRight, CheckCircle } from "lucide-react";
import { getFoncierContent } from "@/app/cms/utils/foncier";

export function WhySecureSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeCard, setActiveCard] = useState<number | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const whySecure = getFoncierContent().whySecure;
  const iconMap = { Shield, CreditCard, TrendingUp };
  const benefits = whySecure?.benefits?.map((benefit: any) => ({
    ...benefit,
    icon: iconMap[benefit.icon] || Shield
  })) || [];

  return (
    <section ref={sectionRef} id="pourquoi" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
              Pourquoi ?
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {whySecure?.title || "Pourquoi sécuriser vos droits fonciers ?"}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {whySecure?.description || "La sécurisation foncière est essentielle pour protéger votre patrimoine, éviter les litiges et garantir la transmission de vos biens. Nos experts vous accompagnent à chaque étape."}
            </p>
          </div>

          {/* Benefits Cards */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              const isActive = activeCard === benefit.id;
              
              return (
                <div
                  key={benefit.id}
                  className={`transition-all duration-700 ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                  }`}
                  style={{ transitionDelay: `${index * 200}ms` }}
                >
                  <Card
                    className={`h-full transition-all duration-300 cursor-pointer group border-2 ${
                      isActive 
                        ? `${benefit.borderColor} shadow-xl scale-105` 
                        : 'border-gray-100 hover:border-primary/30 hover:shadow-lg hover:scale-102'
                    }`}
                    onMouseEnter={() => setActiveCard(benefit.id)}
                    onMouseLeave={() => setActiveCard(null)}
                  >
                    <CardHeader className={`${benefit.bgColor} transition-colors duration-300`}>
                      <div className="flex items-center justify-between mb-4">
                        <div className={`w-14 h-14 ${benefit.bgColor} rounded-xl flex items-center justify-center transition-transform duration-300 group-hover:scale-110`}>
                          <Icon className={`w-7 h-7 ${benefit.color}`} />
                        </div>
                        <div className={`text-2xl font-bold ${benefit.color}`}>
                          0{benefit.id}
                        </div>
                      </div>
                      
                      <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                        {benefit.title}
                      </CardTitle>
                      
                      <CardDescription className="text-gray-600 leading-relaxed">
                        {benefit.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      {/* Details List */}
                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-900 text-sm">Avantages concrets :</h4>
                        <ul className="space-y-2">
                          {benefit.details.map((detail, detailIndex) => (
                            <li key={detailIndex} className="flex items-start space-x-3 text-sm text-gray-600">
                              <CheckCircle className={`w-4 h-4 ${benefit.color} flex-shrink-0 mt-0.5`} />
                              <span>{detail}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Statistics Section */}
          <div className={`bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="text-center space-y-6">
              <h3 className="text-2xl font-bold text-gray-900">
                Les chiffres parlent d'eux-mêmes
              </h3>
              
              <div className="grid md:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">85%</div>
                  <div className="text-sm text-gray-600">des litiges fonciers sont évités grâce au titre foncier</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">+40%</div>
                  <div className="text-sm text-gray-600">de valeur ajoutée à votre terrain s’il est immatriculé</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">3x</div>
                  <div className="text-sm text-gray-600">plus de chances d’obtenir un crédit bancaire</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">100%</div>
                  <div className="text-sm text-gray-600">de protection juridique pour un terrain immatriculé</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
