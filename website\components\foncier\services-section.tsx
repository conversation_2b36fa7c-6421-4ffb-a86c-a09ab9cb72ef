"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Map,
  FileText,
  Key,
  Home,
  Handshake,
  Landmark,
  FileCheck2,
  FilePlus2,
  BadgeCheck,
  Layers3,
  RefreshCcw,
  Users,
  LayoutGrid,
  Ruler,
  ArrowRight,
  ArrowLeft,
  CheckCircle
} from "lucide-react";
import { getFoncierContent } from "@/app/cms/utils/foncier";

const serviceIcons = [
  Map, // Dérogation Spéciale
  FileText, // Dossier technique
  Key, // Achat de Terrain Non Titré
  Home, // Achat de Gré à Gré
  Handshake, // Recours gracieux
  Landmark, // Concession Domaniale
  FileCheck2, // Réhabilitation des titres fonciers
  FilePlus2, // Immatriculation Directe
  BadgeCheck, // Indemnisation
  Layers3, // Morcellement & Mutation par Achat
  RefreshCcw, // Rétrocession
  Users, // Mutation par Décès
  LayoutGrid, // Lotissement
  Ruler // Bornage et reconstitution des bornes
];

export function FoncierServicesSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredService, setHoveredService] = useState<number | null>(null);
  const [scrollIndex, setScrollIndex] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const CARD_WIDTH = 250; // px
  const CARD_GAP = 24; // px (gap-6)
  const VISIBLE_CARDS = 3; // default desktop

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    return () => observer.disconnect();
  }, []);

  const services = getFoncierContent().services;

  // Responsive: update visible cards on resize
  const [visibleCards, setVisibleCards] = useState(VISIBLE_CARDS);
  useEffect(() => {
    function handleResize() {
      if (window.innerWidth < 640) setVisibleCards(1);
      else if (window.innerWidth < 1024) setVisibleCards(2);
      else setVisibleCards(VISIBLE_CARDS);
    }
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Scroll to card index
  const scrollToIndex = (index: number) => {
    if (!carouselRef.current) return;
    const scrollX = index * (CARD_WIDTH + CARD_GAP);
    carouselRef.current.scrollTo({ left: scrollX, behavior: "smooth" });
    setScrollIndex(index);
  };

  // Arrow handlers
  const handlePrev = () => scrollToIndex(Math.max(scrollIndex - 1, 0));
  const handleNext = () =>
    scrollToIndex(
      Math.min(scrollIndex + 1, services.length - visibleCards)
    );

  // Hide arrows if not needed
  const showLeft = scrollIndex > 0;
  const showRight = scrollIndex < services.length - visibleCards;

  return (
    <section ref={sectionRef} id="services" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
              Nos prestations
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Nos services fonciers
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Acquisition, régularisation et mutation de terrains en toute sécurité.
            </p>
          </div>

          {/* Carousel with arrows */}
          <div className="relative">
            {/* Left Arrow */}
            {showLeft && (
              <button
                aria-label="Précédent"
                onClick={handlePrev}
                className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-primary/90 hover:text-white text-primary rounded-full shadow-md p-2 transition flex"
                style={{ boxShadow: '0 2px 8px 0 rgba(0,0,0,0.08)' }}
              >
                <ArrowLeft className="w-6 h-6" />
              </button>
            )}
            {/* Right Arrow */}
            {showRight && (
              <button
                aria-label="Suivant"
                onClick={handleNext}
                className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-primary/90 hover:text-white text-primary rounded-full shadow-md p-2 transition flex"
                style={{ boxShadow: '0 2px 8px 0 rgba(0,0,0,0.08)' }}
              >
                <ArrowRight className="w-6 h-6" />
              </button>
            )}
            {/* Carousel */}
            <div
              ref={carouselRef}
              className="overflow-x-auto scrollbar-thin scrollbar-thumb-primary/10 scrollbar-track-transparent snap-x snap-mandatory px-1 sm:px-8 hide-scrollbar"
              style={{ WebkitOverflowScrolling: "touch", scrollbarWidth: 'thin', scrollbarColor: '#d1d5db #fff' }}
            >
              <div className="flex gap-6 min-w-[900px] pb-4">
                {services.map((service, index) => {
                  const Icon = serviceIcons[index] || Map;
                  return (
                    <div
                      key={service.id}
                      className="w-[280px] min-w-[280px] max-w-[280px] flex-shrink-0 transition-all duration-700 bg-white rounded-2xl border border-gray-200 shadow-md hover:shadow-xl hover:scale-105 cursor-pointer group snap-start"
                      style={{ transitionDelay: `${index * 80}ms` }}
                    >
                      <CardHeader className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                            <Icon className="w-6 h-6 text-primary" />
                          </div>
                        </div>
                        <CardTitle className="text-lg font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                          {service.title}
                        </CardTitle>
                        <CardDescription className="text-gray-600 leading-relaxed mt-1">
                          {service.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4 p-4 pt-0">
                        <div className="space-y-2">
                          <h4 className="font-semibold text-gray-900 text-xs uppercase tracking-wide">Prestations incluses :</h4>
                          <ul className="space-y-1">
                            {service.features.map((feature: string, featureIndex: number) => (
                              <li key={featureIndex} className="flex items-center text-xs text-gray-600">
                                <CheckCircle className="w-4 h-4 text-primary mr-2 flex-shrink-0" />
                                <span>{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        <Button asChild size="sm" className="w-full group/btn">
                          <Link href={"/foncier/services/" + service.slug} className="flex items-center justify-center">
                            En savoir plus
                            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                          </Link>
                        </Button>
                      </CardContent>
                    </div>
                  );
                })}
              </div>
            </div>
            {/* Mobile arrows overlay, always centered */}
            <div className="sm:hidden pointer-events-none absolute inset-0 flex items-center justify-between px-2 z-10">
              <button
                aria-label="Précédent"
                onClick={handlePrev}
                className="pointer-events-auto bg-white/80 hover:bg-primary/90 hover:text-white text-primary rounded-full shadow-md p-2 transition"
                disabled={!showLeft}
                style={{ boxShadow: '0 2px 8px 0 rgba(0,0,0,0.08)' }}
              >
                <ArrowLeft className="w-6 h-6" />
              </button>
              <button
                aria-label="Suivant"
                onClick={handleNext}
                className="pointer-events-auto bg-white/80 hover:bg-primary/90 hover:text-white text-primary rounded-full shadow-md p-2 transition"
                disabled={!showRight}
                style={{ boxShadow: '0 2px 8px 0 rgba(0,0,0,0.08)' }}
              >
                <ArrowRight className="w-6 h-6" />
              </button>
            </div>
            {/* Hide default mobile arrows below carousel */}
          </div>

        </div>
      </div>
    </section>
  );
}

// Add this to the bottom of the file or in your global CSS:
// .hide-scrollbar::-webkit-scrollbar { display: none; }
// .hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
