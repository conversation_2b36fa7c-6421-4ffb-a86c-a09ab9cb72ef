import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowR<PERSON>,
  Hammer,
  Building2,
  Ruler,
  Cog,
  Users,
  Award,
  CheckCircle,
  Target,
  Shield,
  Clock,
  Star,
  Zap,
  Eye,
  ThumbsUp
} from "lucide-react";
import { getConstructionContent } from "@/app/cms/utils/construction";

const constructionData = getConstructionContent();

export const metadata: Metadata = {
  title: constructionData.metadata.title,
  description: constructionData.metadata.description,
  keywords: constructionData.metadata.keywords,
  openGraph: constructionData.metadata.openGraph,
  alternates: {
    canonical: constructionData.metadata.canonical
  }
};

export default function ConstructionPage() {
  const heroData = constructionData.hero;
  const backgroundImageStyle = {
    backgroundImage: `url('${heroData.backgroundImage}')`
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70 z-10"></div>
          <div className="w-full h-full bg-gradient-to-br from-orange-900 via-gray-800 to-blue-900"></div>
          {/* Construction site image - easily modifiable */}
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={backgroundImageStyle}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative z-20">
          <div className="max-w-4xl mx-auto text-center text-white space-y-8">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-orange-600/90 backdrop-blur-sm rounded-full text-sm font-medium border border-orange-500">
              <Hammer className="w-4 h-4 mr-2" />
              {heroData.badge.text}
            </div>

            {/* Main Title */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
              {heroData.title.main}
              <span className="block text-orange-400">{heroData.title.highlight}</span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed">
              {heroData.subtitle}
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center pt-8">
              {heroData.buttons.map((button, index) => (
                <Button
                  key={index}
                  asChild
                  size="lg"
                  className={button.variant === 'primary'
                    ? "bg-orange-600 hover:bg-orange-700 text-white px-8 py-4 text-lg group"
                    : "border-white text-primary hover:bg-white hover:text-orange-600 px-8 py-4 text-lg backdrop-blur-sm"
                  }
                  variant={button.variant === 'primary' ? 'default' : 'outline'}
                >
                  <Link href={button.href} className="flex items-center">
                    {button.text}
                    {button.icon && <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />}
                  </Link>
                </Button>
              ))}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-16 max-w-2xl mx-auto">
              {heroData.stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-orange-400 mb-2">{stat.value}</div>
                  <div className="text-sm text-gray-300">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce"></div>
          </div>
        </div>
      </section>

      {/* Notre Approche Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                <div className="space-y-6">
                  <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium border border-orange-200">
                    <Building2 className="w-4 h-4 mr-2" />
                    Notre Engagement
                  </div>

                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                    Notre Engagement : Votre Vision Réalisée
                  </h2>

                  <p className="text-xl text-gray-600 leading-relaxed">
                    Avec plus de 10 ans d'expérience dans le secteur de la construction au Cameroun,
                    Charlie Oscar Consulting transforme vos idées en réalités concrètes. Notre approche
                    collaborative et notre expertise technique garantissent des projets réussis,
                    dans les délais et selon vos attentes.
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Target className="w-6 h-6 text-orange-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">Expertise Technique</h3>
                    <p className="text-gray-600 text-sm">Maîtrise des dernières technologies et normes de construction</p>
                  </div>

                  <div className="space-y-3">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">Équipe Qualifiée</h3>
                    <p className="text-gray-600 text-sm">Ingénieurs et architectes certifiés avec 10+ ans d'expérience</p>
                  </div>

                  <div className="space-y-3">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <Award className="w-6 h-6 text-green-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">Qualité Garantie</h3>
                    <p className="text-gray-600 text-sm">Respect strict des normes internationales de construction</p>
                  </div>

                  <div className="space-y-3">
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Cog className="w-6 h-6 text-purple-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">Innovation</h3>
                    <p className="text-gray-600 text-sm">Solutions modernes, durables et respectueuses de l'environnement</p>
                  </div>
                </div>
              </div>

              <div className="relative">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-4">
                    <div className="h-48 bg-gradient-to-br from-orange-200 to-orange-300 rounded-lg overflow-hidden">
                      {/* Construction team image - easily modifiable */}
                      <div className="w-full h-full bg-[url('https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80')] bg-cover bg-center"></div>
                    </div>
                    <div className="h-32 bg-gradient-to-br from-blue-200 to-blue-300 rounded-lg overflow-hidden">
                      {/* Construction planning image - easily modifiable */}
                      <div className="w-full h-full bg-[url('https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80')] bg-cover bg-center"></div>
                    </div>
                  </div>
                  <div className="space-y-4 pt-8">
                    <div className="h-32 bg-gradient-to-br from-green-200 to-green-300 rounded-lg overflow-hidden">
                      {/* Construction tools image - easily modifiable */}
                      <div className="w-full h-full bg-[url('https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80')] bg-cover bg-center"></div>
                    </div>
                    <div className="h-48 bg-gradient-to-br from-purple-200 to-purple-300 rounded-lg overflow-hidden">
                      {/* Modern building image - easily modifiable */}
                      <div className="w-full h-full bg-[url('https://images.unsplash.com/photo-1590736969955-71cc94901144?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80')] bg-cover bg-center"></div>
                    </div>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-orange-200 rounded-full"></div>
                <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-blue-200 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services en Bref Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Section Header */}
            <div className="text-center space-y-6 mb-16">
              <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
                <Building2 className="w-4 h-4 mr-2" />
                Nos Services
              </div>

              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                Une Expertise Complète à Chaque Étape de Votre Projet
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Découvrez nos 5 domaines d'expertise qui couvrent l'intégralité de vos besoins en construction,
                de la conception initiale à la livraison finale.
              </p>
            </div>

            {/* Services Grid - 5 Categories */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {/* Études Préliminaires */}
              <Card className="group hover:shadow-xl transition-all duration-300 border-l-4 border-l-orange-500">
                <CardHeader className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge className="bg-orange-100 text-orange-800 border-orange-200">
                      Études
                    </Badge>
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Eye className="w-6 h-6 text-orange-600" />
                    </div>
                  </div>

                  <CardTitle className="text-xl group-hover:text-orange-600 transition-colors">
                    Études Préliminaires
                  </CardTitle>

                  <CardDescription className="text-gray-600 leading-relaxed">
                    Analyse complète de faisabilité, étude de sol et évaluation des contraintes techniques pour optimiser votre projet.
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 text-sm">Inclut :</h4>
                    <ul className="space-y-1">
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-orange-500 flex-shrink-0 mt-0.5" />
                        <span>Étude de faisabilité technique</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-orange-500 flex-shrink-0 mt-0.5" />
                        <span>Analyse géotechnique</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-orange-500 flex-shrink-0 mt-0.5" />
                        <span>Évaluation environnementale</span>
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Conception Architecturale */}
              <Card className="group hover:shadow-xl transition-all duration-300 border-l-4 border-l-blue-500">
                <CardHeader className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                      Conception
                    </Badge>
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Ruler className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>

                  <CardTitle className="text-xl group-hover:text-blue-600 transition-colors">
                    Conception Architecturale
                  </CardTitle>

                  <CardDescription className="text-gray-600 leading-relaxed">
                    Plans détaillés, modélisation 3D et design architectural adapté à vos besoins et contraintes.
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 text-sm">Inclut :</h4>
                    <ul className="space-y-1">
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-blue-500 flex-shrink-0 mt-0.5" />
                        <span>Plans architecturaux détaillés</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-blue-500 flex-shrink-0 mt-0.5" />
                        <span>Modélisation 3D</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-blue-500 flex-shrink-0 mt-0.5" />
                        <span>Optimisation des espaces</span>
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Ingénierie */}
              <Card className="group hover:shadow-xl transition-all duration-300 border-l-4 border-l-green-500">
                <CardHeader className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      Ingénierie
                    </Badge>
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <Cog className="w-6 h-6 text-green-600" />
                    </div>
                  </div>

                  <CardTitle className="text-xl group-hover:text-green-600 transition-colors">
                    Ingénierie Structure
                  </CardTitle>

                  <CardDescription className="text-gray-600 leading-relaxed">
                    Calculs de structure, dimensionnement et solutions techniques pour garantir la solidité de votre construction.
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 text-sm">Inclut :</h4>
                    <ul className="space-y-1">
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Calculs de résistance</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Plans de structure</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Optimisation des matériaux</span>
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Gestion de Projet */}
              <Card className="group hover:shadow-xl transition-all duration-300 border-l-4 border-l-purple-500">
                <CardHeader className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge className="bg-purple-100 text-purple-800 border-purple-200">
                      Gestion
                    </Badge>
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Users className="w-6 h-6 text-purple-600" />
                    </div>
                  </div>

                  <CardTitle className="text-xl group-hover:text-purple-600 transition-colors">
                    Gestion de Projet
                  </CardTitle>

                  <CardDescription className="text-gray-600 leading-relaxed">
                    Coordination complète des travaux, suivi des délais et contrôle qualité pour une livraison réussie.
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 text-sm">Inclut :</h4>
                    <ul className="space-y-1">
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-purple-500 flex-shrink-0 mt-0.5" />
                        <span>Coordination des équipes</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-purple-500 flex-shrink-0 mt-0.5" />
                        <span>Suivi des délais</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-purple-500 flex-shrink-0 mt-0.5" />
                        <span>Contrôle qualité</span>
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Smart Building */}
              <Card className="group hover:shadow-xl transition-all duration-300 border-l-4 border-l-yellow-500">
                <CardHeader className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                      Innovation
                    </Badge>
                    <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                      <Zap className="w-6 h-6 text-yellow-600" />
                    </div>
                  </div>

                  <CardTitle className="text-xl group-hover:text-yellow-600 transition-colors">
                    Smart Building
                  </CardTitle>

                  <CardDescription className="text-gray-600 leading-relaxed">
                    Intégration de technologies intelligentes pour des bâtiments connectés, économes et durables.
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 text-sm">Inclut :</h4>
                    <ul className="space-y-1">
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-yellow-500 flex-shrink-0 mt-0.5" />
                        <span>Domotique avancée</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-yellow-500 flex-shrink-0 mt-0.5" />
                        <span>Efficacité énergétique</span>
                      </li>
                      <li className="flex items-start space-x-2 text-sm text-gray-600">
                        <CheckCircle className="w-3 h-3 text-yellow-500 flex-shrink-0 mt-0.5" />
                        <span>Systèmes connectés</span>
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* All Services Link */}
            <div className="text-center">
              <Button asChild size="lg" variant="outline" className="border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white">
                <Link href="/construction/services" className="flex items-center">
                  Découvrir tous nos services construction
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Pourquoi Choisir Charlie Oscar Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Section Header */}
            <div className="text-center space-y-6 mb-16">
              <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium border border-orange-200">
                <Star className="w-4 h-4 mr-2" />
                Nos Avantages
              </div>

              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                Notre Engagement : Votre Garantie de Réussite
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Découvrez les 6 raisons qui font de Charlie Oscar Consulting votre partenaire idéal
                pour tous vos projets de construction au Cameroun.
              </p>
            </div>

            {/* Advantages Grid - 6 Points */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* 1. Expertise Locale */}
              <div className="flex items-start space-x-4 p-6 bg-orange-50 rounded-lg border border-orange-100">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Target className="w-6 h-6 text-orange-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Expertise Locale Approfondie</h3>
                  <p className="text-gray-600 text-sm">
                    Plus de 15 ans d'expérience au Cameroun avec une parfaite connaissance
                    des réglementations locales et des défis spécifiques du terrain.
                  </p>
                </div>
              </div>

              {/* 2. Équipe Qualifiée */}
              <div className="flex items-start space-x-4 p-6 bg-blue-50 rounded-lg border border-blue-100">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Équipe d'Experts Certifiés</h3>
                  <p className="text-gray-600 text-sm">
                    Ingénieurs et architectes diplômés avec certifications internationales,
                    garantissant un niveau d'expertise technique de premier plan.
                  </p>
                </div>
              </div>

              {/* 3. Respect des Délais */}
              <div className="flex items-start space-x-4 p-6 bg-green-50 rounded-lg border border-green-100">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Clock className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Rigueur Méthodologique</h3>
                  <p className="text-gray-600 text-sm">
                    Nous appliquons des processus de gestion de projet éprouvés, garantissant une
                    planification précise, un suivi rigoureux et une exécution conforme aux standards
                    de qualité internationaux.
                  </p>
                </div>
              </div>

              {/* 4. Qualité Garantie */}
              <div className="flex items-start space-x-4 p-6 bg-purple-50 rounded-lg border border-purple-100">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Award className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Qualité Certifiée ISO</h3>
                  <p className="text-gray-600 text-sm">
                    Processus qualité certifiés avec contrôles à chaque étape,
                    garantissant des constructions durables et conformes aux normes.
                  </p>
                </div>
              </div>

              {/* 5. Innovation Technologique */}
              <div className="flex items-start space-x-4 p-6 bg-yellow-50 rounded-lg border border-yellow-100">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Zap className="w-6 h-6 text-yellow-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Technologies de Pointe</h3>
                  <p className="text-gray-600 text-sm">
                    Utilisation des dernières innovations : BIM, modélisation 3D,
                    matériaux écologiques et solutions smart building.
                  </p>
                </div>
              </div>

              {/* 6. Accompagnement Complet */}
              <div className="flex items-start space-x-4 p-6 bg-red-50 rounded-lg border border-red-100">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <ThumbsUp className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Accompagnement 360°</h3>
                  <p className="text-gray-600 text-sm">
                    Support complet de A à Z : études, autorisations, construction,
                    livraison et maintenance post-construction.
                  </p>
                </div>
              </div>
            </div>

            {/* Stats Section */}
            <div className="mt-16 bg-gradient-to-br from-orange-100 via-white to-blue-100 rounded-2xl p-8 border border-gray-200">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold text-orange-600">10+</div>
                  <div className="text-sm text-gray-600">Années d'expérience</div>
                </div>
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold text-orange-600">100%</div>
                  <div className="text-sm text-gray-600">Engagement qualité</div>
                </div>
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold text-orange-600">24/7</div>
                  <div className="text-sm text-gray-600">Support technique</div>
                </div>
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold text-orange-600">5</div>
                  <div className="text-sm text-gray-600">Domaines d'expertise</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-600 to-orange-700 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Prêts à Démarrer Votre Projet ?
            </h2>

            <p className="text-xl text-orange-100">
              Transformez vos idées en réalité avec Charlie Oscar Consulting.
              Nos experts vous accompagnent à chaque étape pour garantir le succès de votre projet de construction.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" variant="secondary" className="group">
                <Link href="/construction/services" className="flex items-center">
                  Découvrir nos services
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>

              <Button asChild size="lg" variant="outline" className="border-white text-primary hover:bg-white hover:text-orange-600">
                <Link href="/contact">
                  Nous contacter
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
