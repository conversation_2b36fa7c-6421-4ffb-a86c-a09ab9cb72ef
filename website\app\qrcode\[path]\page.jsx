import {redirect} from 'next/navigation';
import path from 'path';

export default async function Layout({ children, params }) {


 const path_redirection_object = {
    "formulaire":{
        redirect : "/formulaire",
    }
 }
 const par = await params.path
 console.log("params.path", par);
  
 // Redirect to the QR code page
 if (path_redirection_object[par]) {
    return redirect(path_redirection_object[par].redirect);
    }
  redirect('/not-found'); // Redirect to a not found page if no match is found
  
  // This return is never reached, but required for the component to be valid
  return (
    <div>
      {children}
    </div>
  )
}