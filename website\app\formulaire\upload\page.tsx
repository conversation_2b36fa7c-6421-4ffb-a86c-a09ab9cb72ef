"use client";

import { FileUploadForm } from "./components/file-upload-form";
import { useLanguage } from "@/app/translations/language-context";

export default function UploadPage() {
  const { t } = useLanguage();

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-bold mb-2">{t('uploadPage.title')}</h1>
        <p className="text-gray-600 mb-8">{t('uploadPage.description')}</p>

        <div className="bg-card rounded-xl shadow-lg p-6">
          <FileUploadForm />
        </div>
      </div>
    </div>
  );
}