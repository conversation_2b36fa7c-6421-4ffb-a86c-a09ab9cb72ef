{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/cms/utils/construction-services.ts"], "sourcesContent": ["import constructionServices from \"@/app/cms/data/construction-services.json\";\n\nexport function getConstructionServicesContent() {\n  return constructionServices;\n}\n\nexport function getConstructionServicesMetadata() {\n  return constructionServices.metadata;\n}\n\nexport function getConstructionServicesHero() {\n  return constructionServices.hero;\n}\n\nexport function getConstructionServicesSection() {\n  return constructionServices.servicesSection;\n}\n\nexport function getConstructionServicesWhyChoose() {\n  return constructionServices.whyChoose;\n}\n\nexport function getConstructionServicesFinalCta() {\n  return constructionServices.finalCta;\n}\n\nexport function getConstructionServiceCategories() {\n  return constructionServices.servicesSection.categories;\n}\n\nexport function getConstructionServiceCategoryById(id: string) {\n  return constructionServices.servicesSection.categories.find(category => category.id === id);\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB;AAC7B;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,QAAQ;AACtC;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,IAAI;AAClC;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,eAAe;AAC7C;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,SAAS;AACvC;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,QAAQ;AACtC;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,eAAe,CAAC,UAAU;AACxD;AAEO,SAAS,mCAAmC,EAAU;IAC3D,OAAO,sHAAA,CAAA,UAAoB,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC1F", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/construction/services/components/hero-section.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight, ChevronRight, Home, Building2 } from \"lucide-react\";\n\ninterface HeroSectionProps {\n  heroData: {\n    breadcrumb: Array<{\n      text: string;\n      href?: string;\n      icon?: string;\n      current?: boolean;\n    }>;\n    badge: {\n      icon: string;\n      text: string;\n    };\n    title: {\n      main: string;\n      highlight: string;\n    };\n    description: string;\n    cta: {\n      text: string;\n      href: string;\n      icon: string;\n    };\n  };\n}\n\nexport default function HeroSection({ heroData }: HeroSectionProps) {\n  return (\n    <section className=\"bg-gradient-to-br from-orange-50 to-white py-20\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Breadcrumb */}\n          <nav className=\"flex items-center space-x-2 text-sm text-gray-500 mb-8\">\n            {heroData.breadcrumb.map((item, index) => (\n              <div key={index} className=\"flex items-center\">\n                {index > 0 && <ChevronRight className=\"w-4 h-4 mx-2\" />}\n                {item.current ? (\n                  <span className=\"text-orange-600\">{item.text}</span>\n                ) : (\n                  <Link href={item.href || \"#\"} className=\"hover:text-orange-600 transition-colors flex items-center\">\n                    {item.icon === \"Home\" && <Home className=\"w-4 h-4 mr-1\" />}\n                    {item.text}\n                  </Link>\n                )}\n              </div>\n            ))}\n          </nav>\n\n          <div className=\"text-center space-y-6\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium border border-orange-200\">\n              <Building2 className=\"w-4 h-4 mr-2\" />\n              {heroData.badge.text}\n            </div>\n\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight\">\n              {heroData.title.main}{\" \"}\n              <span className=\"text-orange-600 relative\">\n                {heroData.title.highlight}\n                <div className=\"absolute -bottom-2 left-0 w-full h-1 bg-orange-600/40 rounded-full\"></div>\n              </span>\n            </h1>\n\n            <p className=\"text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\n              {heroData.description}\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button asChild size=\"lg\" className=\"bg-orange-600 hover:bg-orange-700 group\">\n                <Link href={heroData.cta.href} className=\"flex items-center\">\n                  {heroData.cta.text}\n                  <ArrowRight className=\"ml-2 h-5 w-5 transition-transform group-hover:translate-x-1\" />\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;;;;;AA2Be,SAAS,YAAY,EAAE,QAAQ,EAAoB;IAChE,qBACE,4TAAC;QAAQ,WAAU;kBACjB,cAAA,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;kCACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,4TAAC;gCAAgB,WAAU;;oCACxB,QAAQ,mBAAK,4TAAC,6SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCACrC,KAAK,OAAO,iBACX,4TAAC;wCAAK,WAAU;kDAAmB,KAAK,IAAI;;;;;6DAE5C,4TAAC,8RAAA,CAAA,UAAI;wCAAC,MAAM,KAAK,IAAI,IAAI;wCAAK,WAAU;;4CACrC,KAAK,IAAI,KAAK,wBAAU,4TAAC,0RAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACxC,KAAK,IAAI;;;;;;;;+BAPN;;;;;;;;;;kCAcd,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,uSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB,SAAS,KAAK,CAAC,IAAI;;;;;;;0CAGtB,4TAAC;gCAAG,WAAU;;oCACX,SAAS,KAAK,CAAC,IAAI;oCAAE;kDACtB,4TAAC;wCAAK,WAAU;;4CACb,SAAS,KAAK,CAAC,SAAS;0DACzB,4TAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAInB,4TAAC;gCAAE,WAAU;0CACV,SAAS,WAAW;;;;;;0CAGvB,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC,8HAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,MAAK;oCAAK,WAAU;8CAClC,cAAA,4TAAC,8RAAA,CAAA,UAAI;wCAAC,MAAM,SAAS,GAAG,CAAC,IAAI;wCAAE,WAAU;;4CACtC,SAAS,GAAG,CAAC,IAAI;0DAClB,4TAAC,ySAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;KArDwB", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\r\nimport { ChevronDownIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Accordion({\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\r\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\r\n}\r\n\r\nfunction AccordionItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\r\n  return (\r\n    <AccordionPrimitive.Item\r\n      data-slot=\"accordion-item\"\r\n      className={cn(\"border-b last:border-b-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AccordionTrigger({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\r\n  return (\r\n    <AccordionPrimitive.Header className=\"flex\">\r\n      <AccordionPrimitive.Trigger\r\n        data-slot=\"accordion-trigger\"\r\n        className={cn(\r\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\r\n      </AccordionPrimitive.Trigger>\r\n    </AccordionPrimitive.Header>\r\n  )\r\n}\r\n\r\nfunction AccordionContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\r\n  return (\r\n    <AccordionPrimitive.Content\r\n      data-slot=\"accordion-content\"\r\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\r\n      {...props}\r\n    >\r\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\r\n    </AccordionPrimitive.Content>\r\n  )\r\n}\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,4TAAC,+XAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;KAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,4TAAC,+XAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,4TAAC,+XAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,4TAAC,+XAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,4TAAC,+SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,4TAAC,+XAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,4TAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD;MAdS", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/construction/services/components/services-accordion.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\nimport { Star, Eye, Ruler, Cog, Users, Zap, Building2, CheckCircle, Hammer } from \"lucide-react\";\n\ninterface ServicesAccordionProps {\n  servicesData: {\n    badge: {\n      icon: string;\n      text: string;\n    };\n    title: string;\n    description: string;\n    categories: Array<{\n      id: string;\n      title: string;\n      description: string;\n      icon: string;\n      color: string;\n      content: {\n        introduction: string;\n        services: Array<{\n          title: string;\n          description: string;\n          icon: string;\n          features?: Array<{\n            title: string;\n            subtitle: string;\n          }>;\n        }>;\n      };\n    }>;\n  };\n  openAccordion: string;\n  onValueChange: (value: string) => void;\n}\n\nconst iconMap = {\n  Eye,\n  Ruler,\n  Cog,\n  Users,\n  Zap,\n  Building2,\n  CheckCircle,\n  Hammer,\n  Star\n};\n\nexport default function ServicesAccordion({ servicesData, openAccordion, onValueChange }: ServicesAccordionProps) {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center space-y-4 mb-16\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium border border-orange-200\">\n              <Star className=\"w-4 h-4 mr-2\" />\n              {servicesData.badge.text}\n            </div>\n\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\n              {servicesData.title}\n            </h2>\n\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              {servicesData.description}\n            </p>\n          </div>\n\n          {/* Accordéons des Services */}\n          <Accordion \n            type=\"single\" \n            collapsible \n            className=\"space-y-4\"\n            value={openAccordion}\n            onValueChange={onValueChange}\n          >\n            {servicesData.categories.map((category) => {\n              const IconComponent = iconMap[category.icon as keyof typeof iconMap];\n              const colorClasses: Record<string, string> = {\n                orange: \"border-orange-200 bg-orange-50\",\n                blue: \"border-blue-200 bg-blue-50\",\n                green: \"border-green-200 bg-green-50\",\n                purple: \"border-purple-200 bg-purple-50\",\n                yellow: \"border-yellow-200 bg-yellow-50\"\n              };\n\n              return (\n                <AccordionItem\n                  key={category.id}\n                  id={category.id}\n                  value={category.id}\n                  className={`border-2 rounded-lg ${colorClasses[category.color]}`}\n                >\n                  <AccordionTrigger className=\"px-6 py-4 hover:no-underline\">\n                    <div className=\"flex items-center space-x-4 text-left\">\n                      <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${\n                        category.color === 'orange' ? 'bg-orange-100' :\n                        category.color === 'blue' ? 'bg-blue-100' :\n                        category.color === 'green' ? 'bg-green-100' :\n                        category.color === 'purple' ? 'bg-purple-100' :\n                        'bg-yellow-100'\n                      }`}>\n                        <IconComponent className={`w-6 h-6 ${\n                          category.color === 'orange' ? 'text-orange-600' :\n                          category.color === 'blue' ? 'text-blue-600' :\n                          category.color === 'green' ? 'text-green-600' :\n                          category.color === 'purple' ? 'text-purple-600' :\n                          'text-yellow-600'\n                        }`} />\n                      </div>\n                      <div className=\"flex-1\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-1\">\n                          {category.title}\n                        </h3>\n                        <p className=\"text-gray-600 text-sm\">\n                          {category.description}\n                        </p>\n                      </div>\n                    </div>\n                  </AccordionTrigger>\n\n                  <AccordionContent className=\"px-6 pb-6\">\n                    <div className=\"space-y-6 mt-4\">\n                      <p className=\"text-gray-700 leading-relaxed\">\n                        {category.content.introduction}\n                      </p>\n                      \n                      <div className={`grid ${category.content.services.length > 1 ? 'md:grid-cols-2' : ''} gap-6`}>\n                        {category.content.services.map((service, serviceIndex) => {\n                          const ServiceIcon = iconMap[service.icon as keyof typeof iconMap];\n                          \n                          return (\n                            <div key={serviceIndex} className={`bg-white rounded-lg p-6 border border-${category.color}-200`}>\n                              <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center\">\n                                <div className={`w-8 h-8 bg-${category.color}-100 rounded-lg flex items-center justify-center mr-3`}>\n                                  <ServiceIcon className={`w-4 h-4 text-${category.color}-600`} />\n                                </div>\n                                {service.title}\n                              </h4>\n                              <p className=\"text-gray-600 text-sm leading-relaxed mb-4\">\n                                {service.description}\n                              </p>\n                              \n                              {service.features && (\n                                <div className=\"grid md:grid-cols-3 gap-4 mt-4\">\n                                  {service.features.map((feature, featureIndex) => (\n                                    <div key={featureIndex} className={`text-center p-3 bg-${category.color}-50 rounded-lg`}>\n                                      <div className=\"text-sm font-medium text-gray-900\">{feature.title}</div>\n                                      <div className=\"text-xs text-gray-600 mt-1\">{feature.subtitle}</div>\n                                    </div>\n                                  ))}\n                                </div>\n                              )}\n                            </div>\n                          );\n                        })}\n                      </div>\n                    </div>\n                  </AccordionContent>\n                </AccordionItem>\n              );\n            })}\n          </Accordion>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAqCA,MAAM,UAAU;IACd,KAAA,uRAAA,CAAA,MAAG;IACH,OAAA,2RAAA,CAAA,QAAK;IACL,KAAA,uRAAA,CAAA,MAAG;IACH,OAAA,2RAAA,CAAA,QAAK;IACL,KAAA,uRAAA,CAAA,MAAG;IACH,WAAA,uSAAA,CAAA,YAAS;IACT,aAAA,kTAAA,CAAA,cAAW;IACX,QAAA,6RAAA,CAAA,SAAM;IACN,MAAA,yRAAA,CAAA,OAAI;AACN;AAEe,SAAS,kBAAkB,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAA0B;IAC9G,qBACE,4TAAC;QAAQ,WAAU;kBACjB,cAAA,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,yRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,aAAa,KAAK,CAAC,IAAI;;;;;;;0CAG1B,4TAAC;gCAAG,WAAU;0CACX,aAAa,KAAK;;;;;;0CAGrB,4TAAC;gCAAE,WAAU;0CACV,aAAa,WAAW;;;;;;;;;;;;kCAK7B,4TAAC,iIAAA,CAAA,YAAS;wBACR,MAAK;wBACL,WAAW;wBACX,WAAU;wBACV,OAAO;wBACP,eAAe;kCAEd,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC;4BAC5B,MAAM,gBAAgB,OAAO,CAAC,SAAS,IAAI,CAAyB;4BACpE,MAAM,eAAuC;gCAC3C,QAAQ;gCACR,MAAM;gCACN,OAAO;gCACP,QAAQ;gCACR,QAAQ;4BACV;4BAEA,qBACE,4TAAC,iIAAA,CAAA,gBAAa;gCAEZ,IAAI,SAAS,EAAE;gCACf,OAAO,SAAS,EAAE;gCAClB,WAAW,CAAC,oBAAoB,EAAE,YAAY,CAAC,SAAS,KAAK,CAAC,EAAE;;kDAEhE,4TAAC,iIAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAC1B,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAW,CAAC,sDAAsD,EACrE,SAAS,KAAK,KAAK,WAAW,kBAC9B,SAAS,KAAK,KAAK,SAAS,gBAC5B,SAAS,KAAK,KAAK,UAAU,iBAC7B,SAAS,KAAK,KAAK,WAAW,kBAC9B,iBACA;8DACA,cAAA,4TAAC;wDAAc,WAAW,CAAC,QAAQ,EACjC,SAAS,KAAK,KAAK,WAAW,oBAC9B,SAAS,KAAK,KAAK,SAAS,kBAC5B,SAAS,KAAK,KAAK,UAAU,mBAC7B,SAAS,KAAK,KAAK,WAAW,oBAC9B,mBACA;;;;;;;;;;;8DAEJ,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAG,WAAU;sEACX,SAAS,KAAK;;;;;;sEAEjB,4TAAC;4DAAE,WAAU;sEACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDAM7B,4TAAC,iIAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAC1B,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAE,WAAU;8DACV,SAAS,OAAO,CAAC,YAAY;;;;;;8DAGhC,4TAAC;oDAAI,WAAW,CAAC,KAAK,EAAE,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,mBAAmB,GAAG,MAAM,CAAC;8DACzF,SAAS,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS;wDACvC,MAAM,cAAc,OAAO,CAAC,QAAQ,IAAI,CAAyB;wDAEjE,qBACE,4TAAC;4DAAuB,WAAW,CAAC,sCAAsC,EAAE,SAAS,KAAK,CAAC,IAAI,CAAC;;8EAC9F,4TAAC;oEAAG,WAAU;;sFACZ,4TAAC;4EAAI,WAAW,CAAC,WAAW,EAAE,SAAS,KAAK,CAAC,qDAAqD,CAAC;sFACjG,cAAA,4TAAC;gFAAY,WAAW,CAAC,aAAa,EAAE,SAAS,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;wEAE7D,QAAQ,KAAK;;;;;;;8EAEhB,4TAAC;oEAAE,WAAU;8EACV,QAAQ,WAAW;;;;;;gEAGrB,QAAQ,QAAQ,kBACf,4TAAC;oEAAI,WAAU;8EACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,4TAAC;4EAAuB,WAAW,CAAC,mBAAmB,EAAE,SAAS,KAAK,CAAC,cAAc,CAAC;;8FACrF,4TAAC;oFAAI,WAAU;8FAAqC,QAAQ,KAAK;;;;;;8FACjE,4TAAC;oFAAI,WAAU;8FAA8B,QAAQ,QAAQ;;;;;;;2EAFrD;;;;;;;;;;;2DAdR;;;;;oDAuBd;;;;;;;;;;;;;;;;;;+BAnED,SAAS,EAAE;;;;;wBAyEtB;;;;;;;;;;;;;;;;;;;;;;AAMZ;KAvHwB", "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/construction/services/components/why-choose-section.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Co<PERSON>, <PERSON>, <PERSON><PERSON>, Star } from \"lucide-react\";\n\ninterface WhyChooseSectionProps {\n  whyChooseData: {\n    badge: {\n      icon: string;\n      text: string;\n    };\n    title: string;\n    description: string;\n    advantages: Array<{\n      id: number;\n      icon: string;\n      title: string;\n      description: string;\n      color: string;\n    }>;\n  };\n}\n\nconst iconMap = {\n  CheckCircle,\n  Users,\n  Cog,\n  Eye,\n  Zap,\n  Star\n};\n\nexport default function WhyChooseSection({ whyChooseData }: WhyChooseSectionProps) {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center space-y-4 mb-16\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium border border-orange-200\">\n              <CheckCircle className=\"w-4 h-4 mr-2\" />\n              {whyChooseData.badge.text}\n            </div>\n\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\n              {whyChooseData.title}\n            </h2>\n\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              {whyChooseData.description}\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {whyChooseData.advantages.map((advantage) => {\n              const IconComponent = iconMap[advantage.icon as keyof typeof iconMap];\n              \n              return (\n                <div key={advantage.id} className=\"bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow border border-gray-200\">\n                  <div className=\"flex items-start space-x-4\">\n                    <div className={`w-12 h-12 bg-${advantage.color}-100 rounded-lg flex items-center justify-center flex-shrink-0`}>\n                      <IconComponent className={`w-6 h-6 text-${advantage.color}-600`} />\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-gray-900 mb-2\">{advantage.title}</h3>\n                      <p className=\"text-gray-600 text-sm leading-relaxed\">\n                        {advantage.description}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAoBA,MAAM,UAAU;IACd,aAAA,kTAAA,CAAA,cAAW;IACX,OAAA,2RAAA,CAAA,QAAK;IACL,KAAA,uRAAA,CAAA,MAAG;IACH,KAAA,uRAAA,CAAA,MAAG;IACH,KAAA,uRAAA,CAAA,MAAG;IACH,MAAA,yRAAA,CAAA,OAAI;AACN;AAEe,SAAS,iBAAiB,EAAE,aAAa,EAAyB;IAC/E,qBACE,4TAAC;QAAQ,WAAU;kBACjB,cAAA,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,kTAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,cAAc,KAAK,CAAC,IAAI;;;;;;;0CAG3B,4TAAC;gCAAG,WAAU;0CACX,cAAc,KAAK;;;;;;0CAGtB,4TAAC;gCAAE,WAAU;0CACV,cAAc,WAAW;;;;;;;;;;;;kCAI9B,4TAAC;wBAAI,WAAU;kCACZ,cAAc,UAAU,CAAC,GAAG,CAAC,CAAC;4BAC7B,MAAM,gBAAgB,OAAO,CAAC,UAAU,IAAI,CAAyB;4BAErE,qBACE,4TAAC;gCAAuB,WAAU;0CAChC,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAW,CAAC,aAAa,EAAE,UAAU,KAAK,CAAC,8DAA8D,CAAC;sDAC7G,cAAA,4TAAC;gDAAc,WAAW,CAAC,aAAa,EAAE,UAAU,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;sDAEjE,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAAoC,UAAU,KAAK;;;;;;8DACjE,4TAAC;oDAAE,WAAU;8DACV,UAAU,WAAW;;;;;;;;;;;;;;;;;;+BARpB,UAAU,EAAE;;;;;wBAc1B;;;;;;;;;;;;;;;;;;;;;;AAMZ;KA7CwB", "debugId": null}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/construction/services/components/final-cta-section.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { Button } from \"@/components/ui/button\";\nimport { ArrowRight } from \"lucide-react\";\n\ninterface FinalCtaSectionProps {\n  ctaData: {\n    title: string;\n    description: string;\n    buttons: Array<{\n      text: string;\n      href: string;\n      variant: string;\n      icon?: string;\n    }>;\n  };\n}\n\nexport default function FinalCtaSection({ ctaData }: FinalCtaSectionProps) {\n  return (\n    <section className=\"py-20 bg-gradient-to-r from-orange-600 to-orange-700 text-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-4xl mx-auto text-center space-y-8\">\n          <h2 className=\"text-3xl md:text-4xl font-bold\">\n            {ctaData.title}\n          </h2>\n\n          <p className=\"text-xl text-orange-100\">\n            {ctaData.description}\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            {ctaData.buttons.map((button, index) => (\n              <Button \n                key={index}\n                asChild \n                size=\"lg\" \n                variant={button.variant === 'secondary' ? 'secondary' : 'outline'}\n                className={button.variant === 'outline' \n                  ? \"border-white text-primary hover:bg-white hover:text-orange-600\"\n                  : button.variant === 'secondary' \n                    ? \"group\" \n                    : \"\"\n                }\n              >\n                <Link href={button.href} className=\"flex items-center\">\n                  {button.text}\n                  {button.icon && <ArrowRight className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />}\n                </Link>\n              </Button>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAee,SAAS,gBAAgB,EAAE,OAAO,EAAwB;IACvE,qBACE,4TAAC;QAAQ,WAAU;kBACjB,cAAA,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAGhB,4TAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;kCAGtB,4TAAC;wBAAI,WAAU;kCACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5B,4TAAC,8HAAA,CAAA,SAAM;gCAEL,OAAO;gCACP,MAAK;gCACL,SAAS,OAAO,OAAO,KAAK,cAAc,cAAc;gCACxD,WAAW,OAAO,OAAO,KAAK,YAC1B,mEACA,OAAO,OAAO,KAAK,cACjB,UACA;0CAGN,cAAA,4TAAC,8RAAA,CAAA,UAAI;oCAAC,MAAM,OAAO,IAAI;oCAAE,WAAU;;wCAChC,OAAO,IAAI;wCACX,OAAO,IAAI,kBAAI,4TAAC,ySAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;+BAbnC;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBrB;KAtCwB", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/construction/services/construction-services-client.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { getConstructionServicesContent } from \"@/app/cms/utils/construction-services\";\nimport HeroSection from \"./components/hero-section\";\nimport ServicesAccordion from \"./components/services-accordion\";\nimport WhyChooseSection from \"./components/why-choose-section\";\nimport FinalCtaSection from \"./components/final-cta-section\";\n\nexport default function ConstructionServicesClient() {\n  const constructionServicesData = getConstructionServicesContent();\n  \n  // État pour contrôler quel accordéon est ouvert\n  const [openAccordion, setOpenAccordion] = useState<string>(\"etudes-preliminaires\");\n\n  // IDs valides des accordéons\n  const validAccordionIds = constructionServicesData.servicesSection.categories.map(cat => cat.id);\n\n  // Fonction pour faire défiler vers un élément avec offset pour le header\n  const scrollToElement = (elementId: string) => {\n    const element = document.getElementById(elementId);\n    if (element) {\n      const headerOffset = 100; // Offset pour le header fixe\n      const elementPosition = element.offsetTop - headerOffset;\n      \n      window.scrollTo({\n        top: elementPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  // Gérer les ancres URL au chargement et lors des changements\n  useEffect(() => {\n    const handleHashChange = () => {\n      const hash = window.location.hash.replace('#', '');\n      \n      if (hash && validAccordionIds.includes(hash)) {\n        setOpenAccordion(hash);\n        // Délai pour permettre à l'accordéon de s'ouvrir avant le scroll\n        setTimeout(() => scrollToElement(hash), 100);\n      } else if (!hash) {\n        // Si pas d'ancre, ouvrir le premier accordéon\n        setOpenAccordion(\"etudes-preliminaires\");\n      }\n    };\n\n    // Gérer au chargement initial\n    handleHashChange();\n\n    // Écouter les changements d'ancre\n    window.addEventListener('hashchange', handleHashChange);\n\n    return () => {\n      window.removeEventListener('hashchange', handleHashChange);\n    };\n  }, [validAccordionIds]);\n\n  return (\n    <div className=\"min-h-screen\">\n      <HeroSection heroData={constructionServicesData.hero} />\n      \n      <ServicesAccordion \n        servicesData={constructionServicesData.servicesSection}\n        openAccordion={openAccordion}\n        onValueChange={setOpenAccordion}\n      />\n      \n      <WhyChooseSection whyChooseData={constructionServicesData.whyChoose} />\n      \n      <FinalCtaSection ctaData={constructionServicesData.finalCta} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,2BAA2B,CAAA,GAAA,kJAAA,CAAA,iCAA8B,AAAD;IAE9D,gDAAgD;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,6BAA6B;IAC7B,MAAM,oBAAoB,yBAAyB,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;IAE/F,yEAAyE;IACzE,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe,KAAK,6BAA6B;YACvD,MAAM,kBAAkB,QAAQ,SAAS,GAAG;YAE5C,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,6DAA6D;IAC7D,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;gDAAE;YACR,MAAM;yEAAmB;oBACvB,MAAM,OAAO,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;oBAE/C,IAAI,QAAQ,kBAAkB,QAAQ,CAAC,OAAO;wBAC5C,iBAAiB;wBACjB,iEAAiE;wBACjE;qFAAW,IAAM,gBAAgB;oFAAO;oBAC1C,OAAO,IAAI,CAAC,MAAM;wBAChB,8CAA8C;wBAC9C,iBAAiB;oBACnB;gBACF;;YAEA,8BAA8B;YAC9B;YAEA,kCAAkC;YAClC,OAAO,gBAAgB,CAAC,cAAc;YAEtC;wDAAO;oBACL,OAAO,mBAAmB,CAAC,cAAc;gBAC3C;;QACF;+CAAG;QAAC;KAAkB;IAEtB,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,oKAAA,CAAA,UAAW;gBAAC,UAAU,yBAAyB,IAAI;;;;;;0BAEpD,4TAAC,0KAAA,CAAA,UAAiB;gBAChB,cAAc,yBAAyB,eAAe;gBACtD,eAAe;gBACf,eAAe;;;;;;0BAGjB,4TAAC,6KAAA,CAAA,UAAgB;gBAAC,eAAe,yBAAyB,SAAS;;;;;;0BAEnE,4TAAC,4KAAA,CAAA,UAAe;gBAAC,SAAS,yBAAyB,QAAQ;;;;;;;;;;;;AAGjE;GAhEwB;KAAA", "debugId": null}}]}