"use client";

import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Link
} from '@react-pdf/renderer';
import { FormData } from './components/form-context';

// Register fonts (optional - you can add custom fonts here)
// Font.register({
//   family: 'Roboto',
//   src: '/fonts/Roboto-Regular.ttf',
// });

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
  },
  header: {
    marginBottom: 20,
    borderBottom: '1px solid #333333',
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 5,
    textAlign: 'center',
    color: '#555555',
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    backgroundColor: '#F0F0F0',
    padding: 5,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  label: {
    width: '40%',
    fontSize: 12,
    fontWeight: 'bold',
  },
  value: {
    width: '60%',
    fontSize: 12,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 10,
    color: '#555555',
    borderTop: '1px solid #CCCCCC',
    paddingTop: 10,
  },
  reference: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 20,
    fontWeight: 'bold',
  },
  link: {
    width: '60%',
    fontSize: 10,
    color: '#0000FF',
    textDecoration: 'underline',
  },
});

// PDF Document component
const FormPDF = ({ formData, t, language }: { formData: FormData, t: (key: string) => string, language: string }) => {
  // Debug: Log the form data to see what's available
  console.log('PDF Generator - formData:', JSON.stringify(formData, null, 2));
  console.log('PDF Generator - uploadedFileUrls:', formData.uploadedFileUrls);

  // Create a deep copy of the form data to ensure we're not losing any properties
  const formDataCopy = JSON.parse(JSON.stringify(formData));

  // Use a stable reference number if provided in formData, from local storage, or generate one
  const referenceNumber = formData.referenceNumber || (() => {
    // Try to get reference number from local storage first
    if (typeof window !== 'undefined') {
      try {
        const savedData = localStorage.getItem('formData');
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData.referenceNumber) {
            console.log('PDF: Using reference number from local storage:', parsedData.referenceNumber);
            return parsedData.referenceNumber;
          }
        }
      } catch (error) {
        console.error('PDF: Error reading reference number from local storage:', error);
      }
    }

    // Generate a reference number in the format CAPITALIZED_USER_NAME-ddMMYY:HHmm
    const now = new Date();
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = String(now.getFullYear()).slice(-2);
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    // Process the name part
    let namePart = 'USER';
    if (formData.fullName && formData.fullName.trim()) {
      const firstName = formData.fullName.split(' ')[0];
      namePart = firstName.toUpperCase().replace(/[^A-Z]/g, '');
    }

    // Use underscore instead of colon to avoid file path issues
    const newReference = `${namePart}-${day}${month}${year}_${hours}${minutes}`;
    console.log('PDF: Generated new reference number:', newReference);
    return newReference;
  })();

  // Format date for display in French
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);

    // Format date in French style: DD/MM/YYYY
    const options: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    };

    return date.toLocaleDateString('fr-FR', options);
  };

  // Format procedure types for display
  const formatProcedureTypes = (procedureTypes?: string[]) => {
    if (!procedureTypes || procedureTypes.length === 0) return '';
    return procedureTypes.join(', ');
  };

  return (
    <Document title={t('stepSummary.title')} author="Charlie Oscar Consulting">
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Charlie Oscar Consulting</Text>
          <Text style={styles.subtitle}>{t('stepSummary.title')}</Text>
        </View>

        {/* Reference Number */}
        <View style={styles.reference}>
          <Text>{t('stepSummary.confirmation.referenceLabel')}: {referenceNumber}</Text>
        </View>

        {/* Personal Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('stepSummary.personalInfo.title')}</Text>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.personalInfo.name')}:</Text>
            <Text style={styles.value}>{formData.fullName || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.personalInfo.gender')}:</Text>
            <Text style={styles.value}>{formData.gender || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.personalInfo.birthDate')}:</Text>
            <Text style={styles.value}>{formatDate(formData.birthDate)}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.personalInfo.birthPlace')}:</Text>
            <Text style={styles.value}>{formData.birthPlace || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.personalInfo.nationality')}:</Text>
            <Text style={styles.value}>{formData.nationality || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.personalInfo.profession')}:</Text>
            <Text style={styles.value}>{formData.profession || ''}</Text>
          </View>
        </View>

        {/* Contact Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('stepSummary.contactInfo.title')}</Text>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.contactInfo.phone')}:</Text>
            <Text style={styles.value}>{formData.primaryPhone || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.contactInfo.phone')} 2:</Text>
            <Text style={styles.value}>{formData.secondaryPhone || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.contactInfo.email')}:</Text>
            <Text style={styles.value}>{formData.email || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.contactInfo.address')}:</Text>
            <Text style={styles.value}>{formData.address || ''}</Text>
          </View>
        </View>

        {/* Emergency Contacts Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contacts d'urgence 1</Text>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepEmergencyProcedure.emergencyContacts.contact1.name.label')}:</Text>
            <Text style={styles.value}>{formData.emergencyContact1Name || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepEmergencyProcedure.emergencyContacts.contact1.phone.label')}:</Text>
            <Text style={styles.value}>{formData.emergencyContact1Phone || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepEmergencyProcedure.emergencyContacts.contact1.relation.label')}:</Text>
            <Text style={styles.value}>{formData.emergencyContact1Relation || ''}</Text>
          </View>
        </View>
        {/* second emergency contact, if it exists */}
        {formData.emergencyContact2Name && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contacts d'urgence 2</Text>

            <View style={styles.row}>
              <Text style={styles.label}>{t('stepEmergencyProcedure.emergencyContacts.contact2.name.label')}:</Text>
              <Text style={styles.value}>{formData.emergencyContact2Name || ''}</Text>
            </View>

            <View style={styles.row}>
              <Text style={styles.label}>{t('stepEmergencyProcedure.emergencyContacts.contact2.phone.label')}:</Text>
              <Text style={styles.value}>{formData.emergencyContact2Phone || ''}</Text>
            </View>

            <View style={styles.row}>
              <Text style={styles.label}>{t('stepEmergencyProcedure.emergencyContacts.contact2.relation.label')}:</Text>
              <Text style={styles.value}>{formData.emergencyContact2Relation || ''}</Text>
            </View>
          </View>
        )}

        {/* Procedure Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('stepSummary.procedureInfo.title')}</Text>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.procedureInfo.status')}:</Text>
            <Text style={styles.value}>
              {formData.landStatus ?
                (formData.landStatus === 'owner' ? "Propriétaire" :
                 formData.landStatus === 'heir' ? "Héritier" :
                 formData.landStatus === 'buyer' ? "Acheteur" :
                 formData.landStatus === 'applicant' ? "Demandeur" :
                 formData.landStatus === 'other' ? "Autre" :
                 formData.landStatus) : ''}
            </Text>
          </View>

          {/* Other Land Status (if selected 'other') */}
          {formData.landStatus === 'other' && formData.otherLandStatus && (
            <View style={styles.row}>
              <Text style={styles.label}>{t('stepEmergencyProcedure.landStatus.other.label')}:</Text>
              <Text style={styles.value}>{formData.otherLandStatus}</Text>
            </View>
          )}

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.procedureInfo.type')}:</Text>
            <Text style={styles.value}>{formatProcedureTypes(formData.procedureTypes)}
            {formData.procedureTypes?.includes('other') && formData.otherProcedureType && (
            `: ${formData.otherProcedureType}`
            )}
          </Text>
          </View>


        </View>

        {/* Land Location Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('stepSummary.locationInfo.title')}</Text>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.locationInfo.region')}:</Text>
            <Text style={styles.value}>{formData.region || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.locationInfo.department')}:</Text>
            <Text style={styles.value}>{formData.department || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.locationInfo.subdivision')}:</Text>
            <Text style={styles.value}>{formData.subdivision || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.locationInfo.neighborhood')}:</Text>
            <Text style={styles.value}>{formData.neighborhood || ''}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{t('stepSummary.locationInfo.area')}:</Text>
            <Text style={styles.value}>{formData.area ? `${formData.area} m²` : ''}</Text>
          </View>

          {/* Documents Details */}
          {formData.documentsDetails && (
            <View style={styles.row}>
              <Text style={styles.label}>{t('stepDocumentsLocation.documents.details.label')}:</Text>
              <Text style={styles.value}>{formData.documentsDetails}</Text>
            </View>
          )}

          {/* Uploaded Files */}
          {formDataCopy.uploadedFileUrls && Object.keys(formDataCopy.uploadedFileUrls).length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Fichiers téléchargés</Text>
              {Object.entries(formDataCopy.uploadedFileUrls).map(([fileName, fileUrl], index) => {
                // Skip entries where the key contains a path separator (we only want to show the simple file names)
                if (fileName.includes('/')) {
                  return null;
                }

                console.log(`Rendering file ${index + 1}:`, fileName, fileUrl);
                return (
                  <View key={index} style={styles.row}>
                    <Text style={styles.label}>{fileName.split('/').pop() || fileName}:</Text>
                    <Link src={fileUrl} style={styles.link}>
                      {fileUrl}
                    </Link>
                  </View>
                );
              }).filter(Boolean)}
            </View>
          )}

          {/* Debug: Log if no files are found */}
          {(!formDataCopy.uploadedFileUrls || Object.keys(formDataCopy.uploadedFileUrls).length === 0) && (
            console.log('No uploaded file URLs found in form data')
          )}
        </View>

        {/* Additional Information Section */}
        {formData.additionalComments && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('stepSummary.additionalComments.label')}</Text>
            <View style={styles.row}>
              <Text style={styles.value}>{formData.additionalComments}</Text>
            </View>
          </View>
        )}

        {/* Language Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations du document</Text>

          <View style={styles.row}>
            <Text style={styles.label}>Langue du client:</Text>
            <Text style={styles.value}>{language === 'fr' ? 'Français' : 'English'}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Date de génération:</Text>
            <Text style={styles.value}>{formatDate(new Date().toISOString())}</Text>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text>Charlie Oscar Consulting SARL - {formatDate(new Date().toISOString())}</Text>
          <Text>{t('stepSummary.confirmation.referenceHelp')}</Text>
        </View>
      </Page>
    </Document>
  );
};

// PDF Download functionality has been removed as it's no longer necessary

// Function to get PDF blob for API submission
export const getPdfBlob = async (formData: FormData): Promise<Blob> => {
  try {
    // Debug: Log the form data being used to generate the PDF
    console.log('getPdfBlob - formData:', JSON.stringify(formData, null, 2));
    console.log('getPdfBlob - uploadedFileUrls:', formData.uploadedFileUrls);

    // Import necessary modules
    const { pdf } = await import('@react-pdf/renderer');

    // Get translations from a non-hook function
    const translations = require('@/app/translations/fr.ts').default;
    const t = (key: string) => {
      const keys = key.split('.');
      let value = translations;
      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          return key; // Fallback to the key if translation not found
        }
      }
      return value || key;
    };

    // Create a deep copy of the form data to ensure we're not losing any properties
    const formDataCopy = JSON.parse(JSON.stringify(formData));

    // Create the PDF document
    const pdfDocument = <FormPDF formData={formDataCopy} t={t} language="fr" />;

    // Generate the PDF as a blob
    console.log('Generating PDF blob...');
    const blob = await pdf(pdfDocument).toBlob();
    console.log('PDF blob generated successfully, size:', blob.size);

    return blob;
  } catch (error) {
    console.error('Error in getPdfBlob:', error);
    throw error;
  }
};

export default FormPDF;
