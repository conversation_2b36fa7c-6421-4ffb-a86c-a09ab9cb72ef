"use client"

import type React from "react"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Upload, X } from "lucide-react"
import Image from "next/image"
import { useMedia } from "@/app/m/lib/contexts/media-context"

interface MediaUploadModalProps {
  isOpen: boolean
  onClose: () => void
}

export function MediaUploadModal({ isOpen, onClose }: MediaUploadModalProps) {
  const { uploadFiles, isLoading } = useMedia()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [filePreview, setFilePreview] = useState<string>("")
  const [fileName, setFileName] = useState("")
  const [description, setDescription] = useState("")

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      setFileName(file.name.replace(/\.[^/.]+$/, "")) // Remove extension for default name

      if (file.type.startsWith("image/")) {
        const reader = new FileReader()
        reader.onload = (e) => {
          setFilePreview(e.target?.result as string)
        }
        reader.readAsDataURL(file)
      } else {
        setFilePreview("")
      }
    }
  }

  const removeFile = () => {
    setSelectedFile(null)
    setFilePreview("")
    setFileName("")
    setDescription("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    try {
      // Create a new file with the custom name if changed
      let fileToUpload = selectedFile
      if (fileName !== selectedFile.name.replace(/\.[^/.]+$/, "")) {
        const extension = selectedFile.name.split(".").pop()
        const newFileName = `${fileName}.${extension}`
        fileToUpload = new File([selectedFile], newFileName, { type: selectedFile.type })
      }

      await uploadFiles([fileToUpload])

      // Reset form
      removeFile()
      onClose()
    } catch (error) {
      console.error("Error uploading file:", error)
    }
  }

  const getFileIcon = () => {
    if (!selectedFile) return "📄"
    if (selectedFile.type.startsWith("image/")) return "🖼️"
    if (selectedFile.type.startsWith("video/")) return "🎥"
    return "📄"
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Media</DialogTitle>
          <DialogDescription>Upload and configure your media file</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {!selectedFile ? (
            <div
              className="border-2 border-dashed border-muted-foreground/25 rounded-md p-8 text-center cursor-pointer hover:border-primary/50 transition-colors"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-lg font-medium">Click to select file</p>
              <p className="text-sm text-muted-foreground">Images, videos, and documents</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* File Preview */}
              <div className="relative border rounded-md p-4">
                <Button variant="ghost" size="sm" className="absolute top-2 right-2" onClick={removeFile}>
                  <X className="h-4 w-4" />
                </Button>

                {filePreview ? (
                  <Image
                    src={filePreview || "/placeholder.svg"}
                    alt="Preview"
                    width={200}
                    height={150}
                    className="w-full h-32 object-cover rounded-md"
                  />
                ) : (
                  <div className="flex items-center justify-center h-32 bg-muted rounded-md">
                    <div className="text-center">
                      <div className="text-4xl mb-2">{getFileIcon()}</div>
                      <p className="text-sm text-muted-foreground">{selectedFile.type}</p>
                    </div>
                  </div>
                )}

                <div className="mt-2 text-sm text-muted-foreground">
                  Size: {Math.round(selectedFile.size / 1024)} KB
                </div>
              </div>

              {/* File Details */}
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="fileName">File Name</Label>
                  <Input
                    id="fileName"
                    value={fileName}
                    onChange={(e) => setFileName(e.target.value)}
                    placeholder="Enter file name..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter file description..."
                    rows={3}
                  />
                </div>
              </div>
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*,video/*,.pdf,.doc,.docx"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleUpload} disabled={!selectedFile || isLoading}>
            {isLoading ? "Uploading..." : "Upload"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
