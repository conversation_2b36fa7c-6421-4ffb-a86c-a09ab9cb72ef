"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, Building2, ImageIcon, Users, Plus } from "lucide-react"
import Link from "next/link"
import { useAuth } from "@/app/m/lib/auth-context"

export default function DashboardPage() {
  const { user } = useAuth()

  const quickActions = [
    {
      title: "New Blog Post",
      description: "Create a new blog article",
      href: "/m/dashboard/cms/blog/add",
      icon: FileText,
      color: "bg-blue-500",
    },
    {
      title: "New Foncier Service",
      description: "Add a new foncier service",
      href: "/m/dashboard/cms/services/foncier/add",
      icon: Building2,
      color: "bg-green-500",
    },
    {
      title: "Upload Media",
      description: "Add images, videos, or documents",
      href: "/m/dashboard/cms/media/add-media",
      icon: ImageIcon,
      color: "bg-purple-500",
    },
  ]

  const stats = [
    {
      title: "Total Blog Posts",
      value: "24",
      description: "+2 from last month",
      icon: FileText,
    },
    {
      title: "Foncier Services",
      value: "12",
      description: "+1 from last month",
      icon: Building2,
    },
    {
      title: "Media Files",
      value: "156",
      description: "+12 from last month",
      icon: ImageIcon,
    },
    {
      title: "Active Users",
      value: user?.role === "admin" ? "8" : "—",
      description: user?.role === "admin" ? "All roles" : "Admin only",
      icon: Users,
    },
  ]

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">Welcome back, {user?.name}. Here's what's happening with your content.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">{stat.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Quick Actions</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {quickActions.map((action) => (
            <Card key={action.title} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <div className={`p-2 rounded-md ${action.color} text-white`}>
                    <action.icon className="h-4 w-4" />
                  </div>
                  <CardTitle className="text-lg">{action.title}</CardTitle>
                </div>
                <CardDescription>{action.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full">
                  <Link href={action.href}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create New
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest updates across your content management system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-100 rounded-md">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">New blog post published</p>
                <p className="text-xs text-muted-foreground">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-100 rounded-md">
                <Building2 className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Foncier service updated</p>
                <p className="text-xs text-muted-foreground">1 day ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-purple-100 rounded-md">
                <ImageIcon className="h-4 w-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">5 new images uploaded</p>
                <p className="text-xs text-muted-foreground">2 days ago</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
