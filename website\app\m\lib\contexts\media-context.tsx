"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"

export interface MediaFile {
  id?: string
  name: string
  url: string
  type: "image" | "video" | "document"
  size: number
  file?: File
  uploadedAt?: string
  uploadedBy?: string
  alt?: string
  caption?: string
}

interface MediaContextType {
  files: MediaFile[]
  uploadFiles: (files: File[]) => Promise<MediaFile[]>
  updateFile: (id: string, fileData: Partial<MediaFile>) => Promise<MediaFile>
  deleteFile: (id: string) => Promise<void>
  getFile: (id: string) => MediaFile | undefined
  isLoading: boolean
}

const MediaContext = createContext<MediaContextType | undefined>(undefined)

export function MediaProvider({ children }: { children: React.ReactNode }) {
  const [files, setFiles] = useState<MediaFile[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const uploadFiles = async (uploadFiles: File[]): Promise<MediaFile[]> => {
    setIsLoading(true)

    try {
      const uploadedFiles: MediaFile[] = []

      for (const file of uploadFiles) {
        // Create FormData for multipart/form-data upload
        const formData = new FormData()
        formData.append("file", file)
        formData.append("name", file.name)
        formData.append("description", "") // Can be updated later

        console.log("Media POST /api/media/upload")
        console.log("Content-Type: multipart/form-data")
        console.log("Form Data - file:", file)
        console.log("Form Data - name:", file.name)
        console.log("Form Data - description:", "")

        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 500))

        const mediaFile: MediaFile = {
          id: Math.random().toString(36).substr(2, 9),
          name: file.name,
          url: URL.createObjectURL(file),
          type: file.type.startsWith("image/") ? "image" : file.type.startsWith("video/") ? "video" : "document",
          size: file.size,
          file,
          uploadedAt: new Date().toISOString(),
          uploadedBy: "Current User",
          alt: "",
          caption: "",
        }

        uploadedFiles.push(mediaFile)
      }

      setFiles((prev) => [...prev, ...uploadedFiles])
      return uploadedFiles
    } finally {
      setIsLoading(false)
    }
  }

  const updateFile = async (id: string, fileData: Partial<MediaFile>): Promise<MediaFile> => {
    setIsLoading(true)

    try {
      // Create the exact JSON structure for media update
      const requestBody = {
        name: fileData.name,
        alt: fileData.alt,
        caption: fileData.caption,
      }

      console.log("Media PUT /api/media/" + id)
      console.log("Content-Type: application/json")
      console.log("Request Body:", JSON.stringify(requestBody, null, 2))

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500))

      const existingFile = files.find((f) => f.id === id)
      if (!existingFile) throw new Error("File not found")

      const updatedFile = {
        ...existingFile,
        ...fileData,
      }

      setFiles((prev) => prev.map((f) => (f.id === id ? updatedFile : f)))
      return updatedFile
    } finally {
      setIsLoading(false)
    }
  }

  const deleteFile = async (id: string): Promise<void> => {
    setIsLoading(true)

    try {
      console.log("Media DELETE /api/media/" + id)

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500))
      setFiles((prev) => prev.filter((f) => f.id !== id))
    } finally {
      setIsLoading(false)
    }
  }

  const getFile = (id: string): MediaFile | undefined => {
    return files.find((f) => f.id === id)
  }

  return (
    <MediaContext.Provider
      value={{
        files,
        uploadFiles,
        updateFile,
        deleteFile,
        getFile,
        isLoading,
      }}
    >
      {children}
    </MediaContext.Provider>
  )
}

export function useMedia() {
  const context = useContext(MediaContext)
  if (context === undefined) {
    throw new Error("useMedia must be used within a MediaProvider")
  }
  return context
}
