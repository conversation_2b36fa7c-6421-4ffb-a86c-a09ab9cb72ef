// English translations
const en = {
  fill:"Thank you for carefully filling out this form. An expert from <PERSON> will contact you as soon as possible to assist you with your project.",
  common: {
    next: "Continue",
    previous: "Previous",
    submit: "Submit my request",
    processing: "Processing...",
    required: "*",
    optional: "Optional",
    fileSize: "Maximum size is 2 MB",
    filePreview: "Preview:",
    fileSelected: "File selected:",
    fileFormats: ".pdf, .jpg, .png, max 2 MB per file",
    notProvided: "Not provided",
    notSpecified: "Not specified",
    uploading: "Uploading...",
    uploaded: "Uploaded",
    uploadFailed: "Upload failed",
    retry: "Retry",
    success: "Your request has been submitted successfully. A <PERSON> Consulting advisor will contact you as soon as possible.",
    online: "Online",
    offline: "Offline",
    connectionLost: "Internet connection lost. Some features may be unavailable.",
    connectionRestored: "Internet connection restored.",
    fileUploadOffline:"You cannot upload files when you are offline.",
    waitForUploads: "Please wait for uploads to complete...",
    form: "Form",
    apiDocs: "API Docs",
    testTools: "Test Tools",
    dropFilesHere: "Drop your files here or click to browse",
    maxFileSize: "Max file size: 2 MB",
    fileTooLarge: "File is too large. Maximum size is 2 MB",
    invalidFileType: "File type not supported",
    fileNameTooShort: "File name must be at least 3 characters",
    cancel: "Cancel",
  },

  validation: {
    required: "This field is required",
    minLength: "Must be at least {min} characters",
    email: "Please enter a valid email address",
    phone: "Please enter a valid phone number",
    consent: "You must consent to proceed",
    date: "Please enter a valid date",
    selection: "Please make a selection",
    fileRequired: "Please upload a file",
    fileSize: "File size must be less than {size}",
    fileType: "File type not supported",
  },

  sidebar: {
    title: "Charlie Oscar",
    subtitle: "Consulting",
    step: "Step",
    of: "/",
    currentStep: "Current step",
  },

  steps: {
    personal: {
      name: "Personal Information",
      description: "Enter your personal information to help us get to know you better.",
    },
    emergency: {
      name: "Emergency Contacts and Procedure",
      description: "Provide your emergency contacts and desired procedure.",
    },
    documents: {
      name: "Documents and Land Location",
      description: "Provide documents and information about the land location.",
    },
    summary: {
      name: "Summary and Confirmation",
      description: "Verify all information before submitting your request.",
    },
  },

  stepPersonal: {
    title: "Your Personal Information",
    intro: "Please fill in all required fields marked with an asterisk (*) accurately.",
    dataProtection: {
      title: "Data Protection",
      description: "The information collected is strictly confidential and used only for processing your land request by Charlie Oscar Consulting SARL.",
      consent: "I agree that my information will be processed by Charlie Oscar Consulting SARL",
    },
    personalInfo: {
      title: "Personal Information",
      fullName: {
        label: "Full Name",
        placeholder: "Ex: MBARGA Paul Serge",
      },
      gender: {
        label: "Gender",
        male: "Male",
        female: "Female",
      },
      birthDate: {
        label: "Date of Birth",
      },
      birthPlace: {
        label: "Place of Birth",
        placeholder: "Ex: Yaoundé, Cameroon",
      },
      nationality: {
        label: "Nationality",
        placeholder: "Select your nationality",
        options: {
          cameroon: "Cameroonian",
          france: "French",
          senegal: "Senegalese",
          ivoryCoast: "Ivorian",
          gabon: "Gabonese",
          congo: "Congolese",
          other: "Other",
        },
      },
      profession: {
        label: "Profession",
        placeholder: "Ex: Teacher, Merchant",
      },
    },
    contactInfo: {
      title: "Contact Information",
      primaryPhone: {
        label: "Primary Phone",
        placeholder: "Ex: +237 6XXXXXXXX",
      },
      secondaryPhone: {
        label: "Secondary Phone",
        placeholder: "Ex: +237 6XXXXXXXX",
      },
      email: {
        label: "Email",
        placeholder: "<EMAIL>",
      },
      address: {
        label: "Current Address",
        placeholder: "Ex: Bastos District, Street 1.890, Yaoundé",
      },
      idDocument: {
        label: "ID Document",
        description: "Upload a clear and legible copy of your ID card or passport",
        notUploaded: "No document uploaded",
        nameRequired: "Please enter your full name first",
      },
    },
  },

  stepDocumentsLocation: {
    title: "Documents and Land Location",
    documents: {
      title: "Documents",
      intro: "Please indicate and upload the documents you already have regarding this procedure. The location plan is highly recommended if available.",
      availableDocs: {
        label: "Available Documents",
        help: "Check all documents you can provide",
        options: {
          customaryProperty: "Customary Property Certificate",
          saleAct: "Sale/Donation Deed",
          occupationPermit: "Occupation Permit",
          locationPlan: "Location Plan / Land Sketch",
          landTitle: "Land Title",
          otherDocs: "Other Documents",
        },
      },
      upload: {
        label: "Document Upload",
        documentLabel: "Document",
        description: "Upload your most relevant documents for your request once at the time. You can provide additional documents later.",
      },
      details: {
        label: "Document Details",
        placeholder: "Ex: The land title dates from 2015, the notarized deed of sale is attached...",
        help: "Optional: specify the nature or status of the documents provided",
      },
    },
    location: {
      title: "Location",
      intro: "Please provide precise information regarding the location of the land concerned by the procedure.",
      zoneType: {
        label: "Zone Type",
        urban: "Urban",
        rural: "Rural",
        help: "Select the type of environment where the land is located",
      },
      region: {
        label: "Region",
        placeholder: "Select a region",
        options: [
          "Centre", "Littoral", "South", "East", "West",
          "North-West", "South-West", "North", "Far North", "Adamawa"
        ],
      },
      department: {
        label: "Department",
        placeholder: "Select or specify a department",
        options: {
          centre: ["Mfoundi", "Nyong-et-Kellé", "Nyong-et-So'o", "Lekié", "Mbam-et-Inoubou"],
          littoral: ["Wouri", "Sanaga-Maritime", "Nkam", "Moungo"],
          south: ["Océan", "Vallée-du-Ntem", "Mvila", "Dja-et-Lobo"],
          east: ["Haut-Nyong", "Kadey", "Lom-et-Djérem", "Boumba-et-Ngoko"],
          west: ["Mifi", "Menoua", "Bamboutos", "Haut-Nkam", "Ndé", "Koung-Khi"],
        },
      },
      subdivision: {
        label: "Subdivision",
        placeholder: "Ex: Yaoundé II, Douala III, Bafoussam I",
        help: "Specify the subdivision where the land is located",
      },
      neighborhood: {
        label: "Neighborhood or Village",
        placeholder: "Ex: Mokolo, Bonapriso, Banengo...",
      },
      locationDetails: {
        label: "Locality",
        placeholder: "Ex: Near Saint-Jean Church, behind the central market...",
        help: "Specific geographical landmark facilitating the location of the land",
      },
      area: {
        label: "Estimated Area",
        placeholder: "Ex: 500",
        unit: "m²",
        help: "Approximate land area in square meters",
      },
    },
  },

  stepEmergencyProcedure: {
    title: "Procedure and Urgency",
    emergencyContacts: {
      title: "Emergency Contacts",
      intro: "Please indicate at least one person to contact in case of unavailability.",
      contact1: {
        title: "Person 1 (required)",
        name: {
          label: "Full Name",
          placeholder: "Ex: ATANGANA Marie Claire",
        },
        phone: {
          label: "Phone",
          placeholder: "Ex: +237 6XXXXXXXX",
        },
        relation: {
          label: "Relationship to applicant",
          placeholder: "Ex: Brother, Spouse, Colleague",
          help: "Specify your relationship with this person",
        },
      },
      contact2: {
        title: "Person 2 (optional)",
        name: {
          label: "Full Name",
          placeholder: "Ex: ESSOMBA Jean",
        },
        phone: {
          label: "Phone",
          placeholder: "Ex: +237 6XXXXXXXX",
        },
        relation: {
          label: "Relationship to applicant",
          placeholder: "Ex: Sister, Parent, Friend",
        },
      },
    },
    landStatus: {
      title: "Land Status",
      intro: "Please specify your status in relation to the land concerned by the procedure.",
      label: "Your Status",
      options: {
        owner: "Owner",
        heir: "Heir",
        buyer: "Buyer",
        applicant: "Allocation Applicant",
        other: "Other",
      },
      otherLabel: "Status Details",
      otherPlaceholder: "Please specify your status",
    },
    procedureType: {
      title: "Procedure Type",
      intro: "Please indicate the type of land procedure you wish to undertake.",
      label: "Desired Procedure Type",
      help: "This selection will determine the documents and procedures needed for your file",
      options: [
        "Special Ministerial Derogation",
        "Direct Registration",
        "Registration by Concession",
        "Amicable Decision",
        "Goodwill Protocol Agreement",
        "Land Division",
        "Total Transfer",
        "Transfer by Death",
        "Protocol for Purchasing Unregistered Land",
        "Technical File",
        "Compensation",
        "Retrocession",
        "Administrative Appeal",
        "Land Title Rehabilitation",
        "Other"
      ],
      otherLabel: "Procedure Details",
      otherPlaceholder: "Please specify the exact nature of your request",
      otherHelp: "Provide as much detail as possible to help us prepare your file",
    },
    additionalInfo: {
      label: "Additional Information",
      placeholder: "Enter any additional information regarding your request here",
      help: "Optional: any context or details that can help us better understand your situation",
    },
  },

  stepSummary: {
    title: "Summary and Submission",
    intro: "Please verify the information entered before submitting your request.",
    sections: {
      personal: {
        title: "Personal Information",
      },
      emergency: {
        title: "Emergency Contacts",
      },
      procedure: {
        title: "Procedure",
      },
      documents: {
        title: "Documents Provided",
      },
      location: {
        title: "Land Location",
      },
      editButton: "Edit this section",
    },
    additionalComments: {
      label: "Additional Comments",
      placeholder: "Add any additional information regarding your request here",
      help: "Optional: provide any information that might help us better process your request",
    },
    finalConsent: {
      dataProtection: "By submitting this form, you agree that the information provided will be processed by Charlie Oscar Consulting SARL strictly for your land request, in accordance with Cameroonian legislation on personal data protection.",
      label: "I confirm the accuracy of the information provided and agree to submit my request",
    },
    submit: {
      button: "Submit my request",
      processing: "Processing...",
    },
    confirmation: {
      title: "Request Successfully Submitted",
      message: "Your request has been registered. A Charlie Oscar Consulting advisor will contact you as soon as possible.",
      referenceLabel: "Your request reference",
      referenceHelp: "Please keep this reference for any future communication regarding your file.",
      downloadButton: "Download summary",
      newFormButton: "Start a new form",
    },
    personalInfo: {
      title: "Personal Information",
      name: "Full Name",
      gender: "Gender",
      birthDate: "Date of Birth",
      birthPlace: "Place of Birth",
      nationality: "Nationality",
      profession: "Profession",
    },
    contactInfo: {
      title: "Contact Information",
      phone: "Phone",
      email: "Email",
      address: "Address",
    },
    locationInfo: {
      title: "Land Location",
      region: "Region",
      department: "Department",
      subdivision: "Subdivision",
      neighborhood: "Neighborhood/Village",
      area: "Area",
    },
    procedureInfo: {
      title: "Procedure",
      status: "Status",
      type: "Procedure Type",
    },
    documents: {
      title: "Documents Provided",
      none: "No documents provided",
    },
  },

  uploadPage: {
    title: "Upload Documents",
    description: "Upload your completed form and supporting documents for your land request.",
    mainFile: "Main Document",
    mainFileDescription: "Upload your completed form or main document (PDF only).",
    mainFileRequired: "Please upload your main document before submitting.",
    relatedFiles: "Supporting Documents",
    relatedFilesDescription: "Upload any supporting documents related to your request.",
    addFiles: "Add Files",
    dragOrClick: "Drag and drop files here or click to browse",
    uploadedFiles: "Uploaded Files",
    uploadSuccess: "Your documents have been successfully uploaded. A Charlie Oscar Consulting advisor will contact you very soon.",
    uploadSuccessWithRef: "Your documents have been successfully uploaded with reference number: {ref}. A Charlie Oscar Consulting advisor will contact you very soon.",
    dropMainFile: "Drop your main document here or click to browse",
    selectFile: "Select a file to upload",
    fileName: "File Name/Description",
    fileNamePlaceholder: "Enter a name or description for this file",
    pdfPreview: "PDF Preview",
    confirmWithErrors: "Some files have validation errors. Do you want to continue without them?",
    validationErrors: "Some files have validation errors and will be excluded from submission.",
    mainFileValidationError: "The main document has validation errors. Please fix them before submitting.",
  },
};

export default en;
