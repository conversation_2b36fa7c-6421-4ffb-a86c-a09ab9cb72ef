"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowRight, Sparkle, Circle, Star, Hexagon, Pentagon, Earth } from "lucide-react";

export function CompanyIntro({ content }: { content: any }) {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section
      ref={sectionRef}
      className="py-20 w-full relative bg-cover bg-center"
      style={{
        backgroundImage: "url('/images/company-intro-bg.jpeg')"
      }}
    >
      {/* Overlay for better contrast */}
      <div className="absolute inset-0 bg-black/50 pointer-events-none" />
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className={`space-y-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="flex flex-col gap-10 sm:flex-row  w-full">
                {/* Section Header */}
                <div className="space-y-4 w-full sm:w-[40%]">
                  <div className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
                    {content?.title}
                  </div>
                  <h2 className="text-2xl md:text-3xl font-bold text-white">
                    {content?.content}
                  </h2>
                </div>

                {/* Main Content */}
                <div className="space-y-6  w-full sm:w-[60%]">
                  {content?.paragraphs && content.paragraphs.map((p: string, idx: number) => (
                    <p key={idx} className="text-md text-white leading-relaxed" dangerouslySetInnerHTML={{ __html: p }} />
                  ))}
                </div>
              </div>
            {/* Key Points */}
            {content?.keyPoints && (
              <div className="grid md:grid-cols-3 gap-8 pt-8">
                {content.keyPoints.map((point: any, idx: number) => {
                  // Choose an abstract icon based on index
                  const icons = [Pentagon, Hexagon, Earth];
                  const Icon = icons[idx % icons.length];
                  return (
                    <div
                      key={idx}
                      className="flex flex-col items-center bg-white border border-gray-200 rounded-xl shadow-md transition-transform duration-300 hover:shadow-lg hover:-translate-y-1 p-6 min-h-[220px] group"
                    >
                      <Icon className="w-10 h-10 text-primary mb-4 group-hover:text-primary-700 transition-colors duration-200" />
                      <div className="font-bold text-lg text-primary mb-2 group-hover:text-primary-700 transition-colors duration-200 text-center">
                        {point.title}
                      </div>
                      <div className="text-gray-500 text-sm text-center">
                        {point.description}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
