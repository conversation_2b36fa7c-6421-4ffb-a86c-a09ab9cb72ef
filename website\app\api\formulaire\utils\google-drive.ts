/**
 * Google Drive API utilities
 *
 * This module provides functions for interacting with Google Drive API,
 * including uploading files and creating folders using service account authentication.
 *
 * @module google-drive
 * @requires googleapis
 * @requires stream
 *
 * @example
 * // Upload a file to Google Drive
 * const result = await uploadToGoogleDrive(
 *   'example.pdf',
 *   'application/pdf',
 *   fileBuffer
 * );
 *
 * @example
 * // Create a folder in Google Drive
 * const folder = await createDriveFolder('My Folder');
 */

import { google } from 'googleapis';
import type { drive_v3 } from 'googleapis';
import { Readable } from 'stream';

/**
 * Load Google Drive credentials from environment variables
 *
 * In development, these are loaded from .env.local
 * In production, these should be set in the hosting environment
 */
const CREDENTIALS = {
  type: process.env.GOOGLE_DRIVE_TYPE || "service_account",
  project_id: process.env.GOOGLE_DRIVE_PROJECT_ID || "",
  private_key_id: process.env.GOOGLE_DRIVE_PRIVATE_KEY_ID || "",
  private_key: process.env.GOOGLE_DRIVE_PRIVATE_KEY || "",
  client_email: process.env.GOOGLE_DRIVE_CLIENT_EMAIL || "",
  client_id: process.env.GOOGLE_DRIVE_CLIENT_ID || "",
  auth_uri: process.env.GOOGLE_DRIVE_AUTH_URI || "https://accounts.google.com/o/oauth2/auth",
  token_uri: process.env.GOOGLE_DRIVE_TOKEN_URI || "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: process.env.GOOGLE_DRIVE_AUTH_PROVIDER_CERT_URL || "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: process.env.GOOGLE_DRIVE_CLIENT_X509_CERT_URL || "",
  universe_domain: process.env.GOOGLE_DRIVE_UNIVERSE_DOMAIN || "googleapis.com"
};

// Google Drive folder ID where files will be uploaded by default
const DEFAULT_FOLDER_ID = process.env.GOOGLE_DRIVE_FOLDER_ID || '';

// Google Drive API scopes
const SCOPES = (process.env.GOOGLE_DRIVE_SCOPES || 'https://www.googleapis.com/auth/drive.file').split(',');

/**
 * Interface for file upload options
 *
 * This interface defines the options that can be passed to the uploadToGoogleDrive function
 * to customize the upload behavior.
 *
 * @interface FileUploadOptions
 */
export interface FileUploadOptions {
  /**
   * The ID of the folder to upload the file to
   *
   * If not provided, the default folder ID from environment variables will be used.
   * This allows uploading files to specific folders in Google Drive.
   *
   * @type {string}
   * @memberof FileUploadOptions
   * @example
   * // Upload to a specific folder
   * const options = { folderId: '1a2b3c4d5e6f7g8h9i0j' };
   */
  folderId?: string;

  /**
   * Whether to make the file publicly accessible via a link
   *
   * If true, the file will be shared with anyone who has the link.
   * Default is false, which means the file is only accessible to the service account
   * and users with whom it's explicitly shared.
   *
   * @type {boolean}
   * @memberof FileUploadOptions
   * @default false
   * @example
   * // Make the file publicly accessible
   * const options = { makePublic: true };
   */
  makePublic?: boolean;

  /**
   * Fields to include in the response from the Google Drive API
   *
   * This allows customizing which metadata fields are returned for the uploaded file.
   * Default is 'id,name,webViewLink,webContentLink'.
   *
   * @type {string}
   * @memberof FileUploadOptions
   * @default 'id,name,webViewLink,webContentLink'
   * @example
   * // Request additional fields in the response
   * const options = { fields: 'id,name,webViewLink,webContentLink,size,createdTime' };
   */
  fields?: string;

  /**
   * Create a new folder for this file
   *
   * If provided, a new folder with this name will be created in the parent folder
   * and the file will be uploaded to this new folder. This is useful for organizing
   * files by creating a dedicated folder for them.
   *
   * @type {string}
   * @memberof FileUploadOptions
   * @example
   * // Create a new folder for the file
   * const options = { createFolder: 'Customer Documents' };
   */
  createFolder?: string;
}

/**
 * Interface for folder information
 *
 * This interface represents metadata about a Google Drive folder.
 * It's used to return information about folders created or used during file uploads.
 *
 * @interface FolderInfo
 */
export interface FolderInfo {
  /**
   * The unique ID of the folder in Google Drive
   *
   * This ID can be used to reference the folder in subsequent API calls,
   * such as uploading files to this specific folder.
   *
   * @type {string}
   * @memberof FolderInfo
   */
  id: string;

  /**
   * The display name of the folder
   *
   * This is the name that appears in the Google Drive UI.
   *
   * @type {string}
   * @memberof FolderInfo
   */
  name: string;

  /**
   * The URL to view the folder in Google Drive web interface
   *
   * This link can be shared with users to give them access to the folder
   * (subject to the folder's sharing permissions).
   *
   * @type {string}
   * @memberof FolderInfo
   */
  webViewLink?: string;
}

/**
 * Interface for file upload result
 *
 * This interface represents the result of a file upload operation to Google Drive.
 * It includes information about the uploaded file and any errors that occurred.
 *
 * @interface FileUploadResult
 */
export interface FileUploadResult {
  /**
   * Whether the upload was successful
   *
   * True if the file was successfully uploaded to Google Drive,
   * false if an error occurred during the upload process.
   *
   * @type {boolean}
   * @memberof FileUploadResult
   */
  success: boolean;

  /**
   * The unique ID of the uploaded file in Google Drive
   *
   * This ID can be used to reference the file in subsequent API calls.
   * Only present if the upload was successful.
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  fileId?: string;

  /**
   * The name of the uploaded file in Google Drive
   *
   * This is the name that appears in the Google Drive UI.
   * Only present if the upload was successful.
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  fileName?: string;

  /**
   * The URL to view the file in Google Drive web interface
   *
   * This link can be shared with users to give them access to the file
   * (subject to the file's sharing permissions).
   * Only present if the upload was successful.
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  webViewLink?: string;

  /**
   * The URL to download the file content directly
   *
   * This link can be used to download the file without opening the Google Drive UI.
   * Only present if the upload was successful and the file is downloadable.
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  webContentLink?: string;

  /**
   * Information about the folder the file was uploaded to
   *
   * This property is only present if a new folder was created during the upload process.
   * It contains metadata about the created folder.
   *
   * @type {FolderInfo}
   * @memberof FileUploadResult
   */
  folder?: FolderInfo;

  /**
   * Error message if the upload failed
   *
   * This property contains a human-readable error message explaining why the upload failed.
   * Only present if the upload was not successful (success = false).
   *
   * @type {string}
   * @memberof FileUploadResult
   */
  error?: string;
}

/**
 * Create a folder in Google Drive
 *
 * This function creates a new folder in Google Drive using the service account credentials.
 * If a parent folder ID is provided, the new folder will be created inside that folder.
 * Otherwise, it will be created in the default folder specified by the GOOGLE_DRIVE_FOLDER_ID
 * environment variable.
 *
 * @param {string} folderName - The name of the folder to create
 * @param {string} [parentFolderId] - The ID of the parent folder. If not provided,
 *                                    the default folder ID from environment variables will be used.
 * @returns {Promise<drive_v3.Schema$File>} A promise that resolves to the created folder's metadata
 *
 * @throws {Error} If folder creation fails or if credentials are invalid
 *
 * @example
 * // Create a folder in the default parent folder
 * const folder = await createDriveFolder('Customer Documents');
 * console.log(`Folder created with ID: ${folder.id}`);
 *
 * @example
 * // Create a subfolder in a specific parent folder
 * const subfolder = await createDriveFolder('Invoices', parentFolderId);
 * console.log(`Subfolder created with ID: ${subfolder.id}`);
 */
export async function createDriveFolder(
  folderName: string,
  parentFolderId?: string
): Promise<drive_v3.Schema$File> {
  console.log('Google Drive: Creating folder:', folderName);
  console.log('Google Drive: Parent folder ID:', parentFolderId || DEFAULT_FOLDER_ID);

  // Retry configuration
  const maxRetries = 3;
  const initialBackoffMs = 1000; // Start with 1 second delay

  // Track retry attempts
  let retryCount = 0;
  let lastError: any = null;

  while (retryCount <= maxRetries) {
    try {
      // If this is a retry, log the attempt
      if (retryCount > 0) {
        console.log(`Google Drive: Retry attempt ${retryCount}/${maxRetries} for folder creation`);
      }

      // Initialize the Google Drive API client
      console.log('Google Drive: Initializing auth client for folder creation');
      const auth = new google.auth.GoogleAuth({
        credentials: CREDENTIALS,
        scopes: SCOPES
      });

      console.log('Google Drive: Creating drive client for folder creation');
      const drive = google.drive({
        version: 'v3',
        auth
      });

      // Folder metadata
      const folderMetadata: drive_v3.Schema$File = {
        name: folderName,
        mimeType: 'application/vnd.google-apps.folder',
        parents: [parentFolderId || DEFAULT_FOLDER_ID]
      };
      console.log('Google Drive: Folder metadata:', folderMetadata);

      // Create the folder
      console.log('Google Drive: Sending folder creation request');

      // Set a timeout for the API request
      const timeoutMs = 10000; // 10 seconds
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Folder creation timed out after ${timeoutMs}ms`)), timeoutMs);
      });

      // Race the API request against the timeout
      const response = await Promise.race([
        drive.files.create({
          requestBody: folderMetadata,
          fields: 'id,name,webViewLink'
        }),
        timeoutPromise
      ]) as any;

      console.log('Google Drive: Folder creation response:', response.data);
      console.log(`Google Drive: Folder created successfully: ${folderName}`);

      return response.data;
    } catch (error) {
      lastError = error;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      // Check if this is a network-related error
      const isNetworkError = errorMessage.includes('ENOTFOUND') ||
                             errorMessage.includes('ETIMEDOUT') ||
                             errorMessage.includes('ECONNREFUSED') ||
                             errorMessage.includes('ECONNRESET') ||
                             errorMessage.includes('network') ||
                             errorMessage.includes('timeout');

      console.error(`Google Drive: Error creating folder (attempt ${retryCount + 1}/${maxRetries + 1}):`, errorMessage);

      // If we've reached max retries or it's not a network error, don't retry
      if (retryCount >= maxRetries || !isNetworkError) {
        console.error('Google Drive: Max retries reached or non-network error, giving up');
        break;
      }

      // Calculate backoff time with exponential increase and jitter
      const backoffMs = initialBackoffMs * Math.pow(2, retryCount) * (0.5 + Math.random() * 0.5);
      console.log(`Google Drive: Waiting ${backoffMs}ms before retry ${retryCount + 1}`);

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, backoffMs));

      // Increment retry counter
      retryCount++;
    }
  }

  // If we get here, all retries failed
  console.error('Google Drive: All folder creation attempts failed');

  // Create a minimal folder object with just the name for fallback purposes
  const fallbackFolder: drive_v3.Schema$File = {
    name: folderName,
    id: `fallback-${Date.now()}`, // Generate a temporary ID
    mimeType: 'application/vnd.google-apps.folder'
  };

  // Log the last error with additional context
  const errorMessage = lastError instanceof Error ? lastError.message : 'Unknown error occurred';
  console.error('Google Drive: Folder creation error message:', errorMessage);

  // Instead of throwing, return a fallback folder object
  console.log('Google Drive: Returning fallback folder object for offline mode');
  return fallbackFolder;
}

/**
 * Upload a file to Google Drive
 *
 * This function uploads a file to Google Drive using the service account credentials.
 * It supports various options like specifying a parent folder, making the file public,
 * and creating a new folder for the file.
 *
 * @param {string} fileName - The name of the file to be created in Google Drive
 * @param {string} mimeType - The MIME type of the file (e.g., 'application/pdf', 'image/jpeg')
 * @param {Buffer} fileContent - The file content as a Buffer
 * @param {FileUploadOptions} [options={}] - Additional options for the upload
 * @param {string} [options.folderId] - The ID of the folder to upload the file to
 * @param {boolean} [options.makePublic=false] - Whether to make the file publicly accessible
 * @param {string} [options.fields] - Fields to include in the response (default: 'id,name,webViewLink,webContentLink')
 * @param {string} [options.createFolder] - Name of a new folder to create for this file
 *
 * @returns {Promise<FileUploadResult>} A promise that resolves to the result of the upload operation
 *
 * @throws {Error} The function handles errors internally and returns a FileUploadResult with success=false
 *
 * @example
 * // Basic file upload
 * const result = await uploadToGoogleDrive(
 *   'document.pdf',
 *   'application/pdf',
 *   fileBuffer
 * );
 *
 * @example
 * // Upload with options
 * const result = await uploadToGoogleDrive(
 *   'image.jpg',
 *   'image/jpeg',
 *   imageBuffer,
 *   {
 *     folderId: 'specific-folder-id',
 *     makePublic: true,
 *     fields: 'id,name,webViewLink,webContentLink,size'
 *   }
 * );
 *
 * @example
 * // Upload and create a new folder
 * const result = await uploadToGoogleDrive(
 *   'report.pdf',
 *   'application/pdf',
 *   reportBuffer,
 *   {
 *     createFolder: 'Customer Reports'
 *   }
 * );
 */
export async function uploadToGoogleDrive(
  fileName: string,
  mimeType: string,
  fileContent: Buffer,
  options: FileUploadOptions = {}
): Promise<FileUploadResult> {
  console.log('Google Drive: Starting upload for file:', fileName);
  console.log('Google Drive: Using credentials:', {
    type: CREDENTIALS.type,
    project_id: CREDENTIALS.project_id,
    client_email: CREDENTIALS.client_email,
    // Don't log sensitive information like private keys
  });
  console.log('Google Drive: Default folder ID:', DEFAULT_FOLDER_ID);
  console.log('Google Drive: Options:', options);

  try {
    // Initialize the Google Drive API client
    console.log('Google Drive: Initializing auth client');
    const auth = new google.auth.GoogleAuth({
      credentials: CREDENTIALS,
      scopes: SCOPES
    });

    console.log('Google Drive: Creating drive client');
    const drive = google.drive({
      version: 'v3',
      auth
    });

    // Determine the parent folder ID
    let parentFolderId = options.folderId || DEFAULT_FOLDER_ID;
    console.log('Google Drive: Using parent folder ID:', parentFolderId);
    let folderInfo: { id: string; name: string; webViewLink?: string } | null = null;

    // Create a new folder if requested
    if (options.createFolder) {
      console.log('Google Drive: Creating new folder:', options.createFolder);
      try {
        // Try up to 3 times to create the folder
        let retryCount = 0;
        const maxRetries = 3;
        let folder = null;

        while (retryCount < maxRetries) {
          try {
            folder = await createDriveFolder(options.createFolder, parentFolderId);
            console.log('Google Drive: Folder created successfully:', folder);
            break; // Success, exit the retry loop
          } catch (retryError) {
            retryCount++;
            console.error(`Google Drive: Folder creation attempt ${retryCount} failed:`, retryError);

            if (retryCount < maxRetries) {
              // Wait a bit before retrying (exponential backoff)
              const waitTime = 1000 * Math.pow(2, retryCount);
              console.log(`Google Drive: Waiting ${waitTime}ms before retry ${retryCount + 1}`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
            }
          }
        }

        if (folder && folder.id) {
          parentFolderId = folder.id;
          folderInfo = {
            id: folder.id,
            name: folder.name || options.createFolder,
            webViewLink: folder.webViewLink || undefined
          };
          console.log('Google Drive: Will use new folder ID:', parentFolderId);
        } else {
          throw new Error('Failed to create folder after multiple attempts');
        }
      } catch (folderError) {
        console.error('Google Drive: Error creating folder, will upload to parent folder instead:', folderError);
        console.log('Google Drive: Using parent folder ID as fallback:', parentFolderId);
      }
    }

    // Create a readable stream from the file buffer
    console.log('Google Drive: Creating readable stream from buffer');
    const fileStream = new Readable();
    fileStream.push(fileContent);
    fileStream.push(null); // Mark the end of the stream

    // File metadata
    const fileMetadata: drive_v3.Schema$File = {
      name: fileName,
      parents: [parentFolderId]
    };
    console.log('Google Drive: File metadata:', fileMetadata);

    // Upload the file
    console.log('Google Drive: Uploading file to Drive');
    const response = await drive.files.create({
      requestBody: fileMetadata,
      media: {
        mimeType,
        body: fileStream
      },
      fields: options.fields || 'id,name,webViewLink,webContentLink'
    });
    console.log('Google Drive: Upload response:', response.data);

    // Always make the file accessible via link
    if (response.data.id) {
      console.log('Google Drive: Making file accessible via link');
      try {
        const permissionResponse = await drive.permissions.create({
          fileId: response.data.id,
          requestBody: {
            role: 'reader',
            type: 'anyone',
            allowFileDiscovery: false // This makes it "anyone with the link" instead of fully public
          }
        });
        console.log('Google Drive: Permission created:', permissionResponse.data);
      } catch (permissionError) {
        console.error('Google Drive: Error making file accessible via link:', permissionError);
      }
    }

    console.log(`Google Drive: File uploaded successfully: ${fileName}`);

    // Create a proper sharing link that works for public access
    const fileId = response.data.id;
    const directLink = fileId ? `https://drive.google.com/file/d/${fileId}/view?usp=sharing` : undefined;

    console.log('Google Drive: Created direct link:', directLink);

    const result = {
      success: true,
      fileId: fileId || undefined,
      fileName: response.data.name || undefined,
      webViewLink: response.data.webViewLink || undefined,
      webContentLink: response.data.webContentLink || undefined,
      directLink: directLink,
      folder: folderInfo || undefined
    };

    console.log('Google Drive: Returning result:', result);
    return result;
  } catch (error) {
    console.error('Google Drive: Error uploading file to Google Drive:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('Google Drive: Error message:', errorMessage);

    const result = {
      success: false,
      error: errorMessage
    };

    console.log('Google Drive: Returning error result:', result);
    return result;
  }
}

/**
 * Get MIME type for a file based on its extension
 *
 * This function determines the appropriate MIME type for a file based on its file extension.
 * It supports common file types including documents, images, spreadsheets, presentations, and more.
 * If the file extension is not recognized, it returns 'application/octet-stream' as a fallback.
 *
 * @param {string} fileName - The name of the file including its extension
 * @returns {string} The MIME type corresponding to the file extension
 *
 * @example
 * // Get MIME type for a PDF file
 * const mimeType = getMimeType('document.pdf');
 * // Returns: 'application/pdf'
 *
 * @example
 * // Get MIME type for an image
 * const mimeType = getMimeType('photo.jpg');
 * // Returns: 'image/jpeg'
 *
 * @example
 * // Get MIME type for an unknown extension
 * const mimeType = getMimeType('data.xyz');
 * // Returns: 'application/octet-stream'
 */
export function getMimeType(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'pdf':
      return 'application/pdf';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'doc':
      return 'application/msword';
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'xls':
      return 'application/vnd.ms-excel';
    case 'xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'ppt':
      return 'application/vnd.ms-powerpoint';
    case 'pptx':
      return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
    case 'txt':
      return 'text/plain';
    case 'csv':
      return 'text/csv';
    case 'json':
      return 'application/json';
    case 'xml':
      return 'application/xml';
    case 'zip':
      return 'application/zip';
    default:
      return 'application/octet-stream';
  }
}
