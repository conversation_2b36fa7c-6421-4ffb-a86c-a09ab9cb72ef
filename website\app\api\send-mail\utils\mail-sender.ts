import nodemailer from 'nodemailer';

/**
 * Interface for email data
 */
export interface EmailData {
  to: string | string[]; // Single email or array of emails
  subject: string;
  html?: string; // HTML content
  text?: string; // Plain text alternative
}

/**
 * Interface for email sending response
 */
export interface EmailResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

/**
 * Creates a nodemailer transporter using SMTP credentials from environment variables
 * @returns Nodemailer transporter
 */
export const createTransporter = () => {
  // Get SMTP credentials from environment variables
  const host = process.env.SMTP_HOST;
  const port = parseInt(process.env.SMTP_PORT || '587', 10);
  const secure = process.env.SMTP_SECURE === 'true';
  const user = process.env.SMTP_USER;
  const pass = process.env.SMTP_PASS;

  // Validate required environment variables
  if (!host || !user || !pass) {
    throw new Error('Missing required SMTP configuration. Please check your environment variables.');
  }

  // Create and return the transporter
  return nodemailer.createTransport({
    host,
    port,
    secure,
    auth: {
      user,
      pass,
    },
  });
};

/**
 * Sends an email using nodemailer
 * @param emailData - The email data including recipients, subject, and content
 * @returns Promise resolving to an EmailResponse object
 */
export const sendEmail = async (emailData: EmailData): Promise<EmailResponse> => {
  try {
    // Validate email data
    if (!emailData.to || (!emailData.html && !emailData.text)) {
      return {
        success: false,
        message: 'Missing required email fields (recipients or content)',
      };
    }

    // Create transporter
    const transporter = createTransporter();

    // Validate email address format
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const recipients = Array.isArray(emailData.to) ? emailData.to : [emailData.to];

    for (const recipient of recipients) {
      if (!emailRegex.test(recipient)) {
        console.error(`Invalid email format: ${recipient}`);
        return {
          success: false,
          message: 'Invalid email address format',
          error: `The email address "${recipient}" is not valid`
        };
      }
    }

    // Prepare email options
    const mailOptions = {
      from: `"Charlie Oscar Consulting" <${process.env.EMAIL_FROM || process.env.SMTP_USER}>`,
      to: Array.isArray(emailData.to) ? emailData.to.join(',') : emailData.to,
      subject: emailData.subject || 'No Subject',
      html: emailData.html,
      text: emailData.text || (emailData.html ? undefined : 'No content provided'),
    };

    // Log email sending attempt (without sensitive content)
    console.log('Attempting to send email:', {
      to: mailOptions.to,
      from: mailOptions.from,
      subject: mailOptions.subject,
      hasHtmlContent: !!mailOptions.html,
      hasTextContent: !!mailOptions.text
    });

    // Send email
    const info = await transporter.sendMail(mailOptions);
    // Log success
    console.log('Email sent successfully:', info.messageId);

    return {
      success: true,
      message: 'Email sent successfully',
      data: {
        messageId: info.messageId,
        recipients: mailOptions.to,
      },
    };
  } catch (error) {
    // Log error
    console.error('Error sending email:', error);

    return {
      success: false,
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Sends emails to multiple recipients individually
 * @param emails - Array of email addresses
 * @param subject - Email subject
 * @param content - Email content (HTML or plain text)
 * @param isHtml - Whether the content is HTML (default: true)
 * @returns Promise resolving to an array of EmailResponse objects
 */
export const sendBulkEmails = async (
  emails: string[],
  subject: string,
  content: string,
  isHtml = true
): Promise<EmailResponse[]> => {
  const results: EmailResponse[] = [];

  // Validate inputs
  if (!emails || emails.length === 0) {
    return [{
      success: false,
      message: 'No recipients provided',
    }];
  }

  if (!content) {
    return [{
      success: false,
      message: 'No content provided',
    }];
  }

  // Send emails to each recipient individually
  for (const email of emails) {
    try {
      const emailData: EmailData = {
        to: email,
        subject,
        ...(isHtml ? { html: content } : { text: content }),
      };

      const result = await sendEmail(emailData);
      results.push(result);
    } catch (error) {
      console.error(`Error sending email to ${email}:`, error);

      results.push({
        success: false,
        message: `Failed to send email to ${email}`,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  return results;
};
