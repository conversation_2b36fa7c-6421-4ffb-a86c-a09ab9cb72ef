"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Download, CheckSquare, MapPin, Calculator, Mail, CheckCircle, FileText } from "lucide-react";

export function ResourcesSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [emailInputs, setEmailInputs] = useState({
    checklist: "",
    procedure: "",
    comparator: ""
  });
  const [downloadStates, setDownloadStates] = useState({
    checklist: false,
    procedure: false,
    comparator: false
  });
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleEmailChange = (resource: string, value: string) => {
    setEmailInputs(prev => ({ ...prev, [resource]: value }));
  };

  const handleDownload = async (resource: string) => {
    const email = emailInputs[resource as keyof typeof emailInputs];
    if (!email) return;

    // Simulate download process
    setDownloadStates(prev => ({ ...prev, [resource]: true }));
    
    // Reset after 3 seconds
    setTimeout(() => {
      setDownloadStates(prev => ({ ...prev, [resource]: false }));
      setEmailInputs(prev => ({ ...prev, [resource]: "" }));
    }, 3000);
  };

  const resources = [
    {
      id: "checklist",
      icon: CheckSquare,
      title: "Checklist Documents",
      subtitle: "Documents pour un titre foncier",
      description: "Liste complète des documents nécessaires pour constituer votre dossier de titre foncier",
      features: [
        "Liste exhaustive des pièces",
        "Modèles de documents",
        "Conseils de préparation",
        "Checklist de vérification"
      ],
      fileSize: "PDF - 8 pages",
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    {
      id: "procedure",
      icon: MapPin,
      title: "Fiche Bornage",
      subtitle: "Procédure de bornage officiel",
      description: "Guide détaillé pour comprendre et réaliser un bornage officiel conforme à la réglementation",
      features: [
        "Étapes du bornage",
        "Réglementation applicable",
        "Coûts et délais",
        "Conseils pratiques"
      ],
      fileSize: "PDF - 12 pages",
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    },
    {
      id: "comparator",
      icon: Calculator,
      title: "Comparateur Coûts",
      subtitle: "Coût moyen des frais fonciers",
      description: "Outil de calcul pour estimer les coûts de vos démarches foncières selon votre situation",
      features: [
        "Calculateur interactif",
        "Barème officiel",
        "Estimation personnalisée",
        "Conseils d'optimisation"
      ],
      fileSize: "Excel - Outil interactif",
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200"
    }
  ];

  return (
    <section ref={sectionRef} id="ressources" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
              <FileText className="w-4 h-4 mr-2" />
              Ressources gratuites
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Ressources gratuites
            </h2>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Téléchargez nos outils et guides pratiques pour vous accompagner 
              dans vos démarches foncières.
            </p>
          </div>

          {/* Resources Grid */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {resources.map((resource, index) => {
              const Icon = resource.icon;
              const isDownloaded = downloadStates[resource.id as keyof typeof downloadStates];
              const email = emailInputs[resource.id as keyof typeof emailInputs];
              
              return (
                <div
                  key={resource.id}
                  className={`transition-all duration-700 ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                  }`}
                  style={{ transitionDelay: `${index * 200}ms` }}
                >
                  <Card className={`h-full transition-all duration-300 hover:shadow-lg border-2 ${resource.borderColor} ${resource.bgColor}`}>
                    <CardHeader className="text-center space-y-4">
                      <div className={`w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto transition-transform duration-300 hover:scale-110`}>
                        <Icon className={`w-8 h-8 ${resource.color}`} />
                      </div>
                      
                      <div className="space-y-2">
                        <CardTitle className="text-xl font-bold text-gray-900">
                          {resource.title}
                        </CardTitle>
                        <CardDescription className="text-sm font-medium text-gray-700">
                          {resource.subtitle}
                        </CardDescription>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          {resource.description}
                        </p>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-6">
                      {/* Features */}
                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-900 text-sm">Contenu inclus :</h4>
                        <ul className="space-y-2">
                          {resource.features.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-start space-x-3 text-sm text-gray-600">
                              <CheckCircle className={`w-4 h-4 ${resource.color} flex-shrink-0 mt-0.5`} />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* File Info */}
                      <div className="text-center py-3 bg-white rounded-lg border border-gray-200">
                        <p className="text-sm text-gray-600">{resource.fileSize}</p>
                      </div>

                      {/* Download Form */}
                      {isDownloaded ? (
                        <div className="text-center space-y-4">
                          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                            <CheckCircle className="w-6 h-6 text-green-600" />
                          </div>
                          <div className="space-y-2">
                            <p className="text-sm font-medium text-gray-900">Envoyé !</p>
                            <p className="text-xs text-gray-600">Vérifiez votre email</p>
                          </div>
                          <Button asChild size="sm" className="w-full">
                            <a href={`/foncier/resources/${resource.id}.pdf`} download>
                              <Download className="w-4 h-4 mr-2" />
                              Télécharger
                            </a>
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-700 flex items-center">
                              <Mail className="w-4 h-4 mr-2" />
                              Email pour recevoir le document
                            </label>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              value={email}
                              onChange={(e) => handleEmailChange(resource.id, e.target.value)}
                              className="h-10"
                            />
                          </div>
                          
                          <Button 
                            onClick={() => handleDownload(resource.id)}
                            disabled={!email}
                            className="w-full group"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Télécharger gratuitement
                          </Button>
                          
                          <p className="text-xs text-gray-500 text-center">
                            Pas de spam • Désabonnement facile
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Additional Resources */}
          <div className={`bg-white rounded-2xl p-8 shadow-lg border border-gray-100 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="text-center space-y-6">
              <h3 className="text-2xl font-bold text-gray-900">
                Besoin de plus de ressources ?
              </h3>
              
              <p className="text-gray-600 max-w-3xl mx-auto">
                Accédez à notre bibliothèque complète de guides, modèles et outils 
                pour tous vos projets fonciers.
              </p>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <FileText className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold text-gray-900">Guides complets</h4>
                  <p className="text-sm text-gray-600">Documentation détaillée sur tous les aspects du foncier</p>
                </div>
                
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <Calculator className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold text-gray-900">Outils de calcul</h4>
                  <p className="text-sm text-gray-600">Calculateurs et simulateurs pour vos projets</p>
                </div>
                
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <CheckSquare className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold text-gray-900">Modèles et templates</h4>
                  <p className="text-sm text-gray-600">Documents types prêts à utiliser</p>
                </div>
              </div>

              <Button asChild size="lg" variant="outline" className="group">
                <a href="/foncier/ressources" className="flex items-center">
                  Voir toutes les ressources
                  <Download className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
