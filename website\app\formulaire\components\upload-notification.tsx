"use client";

import React from 'react';
import { useFormContext } from './form-context';
import { hasActiveUploads } from '../utils/file-upload';
import { useLanguage } from '@/app/translations/language-context';
import { AlertCircle } from 'lucide-react';

/**
 * A notification component that displays a message when files are being uploaded
 * 
 * This component checks the form data for active uploads and displays a notification
 * when files are being uploaded. It's used to inform users that they need to wait
 * for uploads to complete before proceeding to the next step.
 */
export function UploadNotification() {
  const { formData } = useFormContext();
  const { t } = useLanguage();
  const isUploading = hasActiveUploads(formData);

  if (!isUploading) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 rounded shadow-md z-50 max-w-md animate-fade-in">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <AlertCircle className="h-5 w-5 text-blue-500" />
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium">
            {t('common.waitForUploads')}
          </p>
          <p className="mt-1 text-xs text-blue-600">
            {t('common.uploading')}
          </p>
        </div>
      </div>
    </div>
  );
}
