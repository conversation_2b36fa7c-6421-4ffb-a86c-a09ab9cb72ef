'use client';

import { useState } from 'react';
import openApiSchema from './openapi';
import Link from 'next/link';

/**
 * API Documentation Page
 *
 * This page displays a custom API documentation UI based on the OpenAPI schema.
 * It uses the OpenAPI schema defined in openapi.ts.
 */
export default function ApiDocsPage() {
  const [activeTag, setActiveTag] = useState<string | null>(null);
  const [activeEndpoint, setActiveEndpoint] = useState<string | null>(null);

  // Extract tags from the schema
  const tags = openApiSchema.tags || [];

  // Group endpoints by tag
  const endpointsByTag: Record<string, { path: string; method: string; summary: string; description: string }[]> = {};

  // Process paths and organize by tag
  Object.entries(openApiSchema.paths).forEach(([path, methods]) => {
    Object.entries(methods as Record<string, any>).forEach(([method, details]) => {
      const endpointTags = details.tags || ['default'];

      endpointTags.forEach(tag => {
        if (!endpointsByTag[tag]) {
          endpointsByTag[tag] = [];
        }

        endpointsByTag[tag].push({
          path,
          method: method.toUpperCase(),
          summary: details.summary || '',
          description: details.description || '',
        });
      });
    });
  });

  return (
    <div className="container mx-auto py-4 px-4">
      <h1 className="text-2xl font-bold mb-4">Charlie Oscar Consulting API Documentation</h1>

      <div className="bg-yellow-100 border-l-4 border-yellow-500 p-4 mb-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              <strong>Note:</strong> Test endpoints are only available in non-production environments.
            </p>
          </div>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        {/* Sidebar */}
        <div className="w-full md:w-1/4">
          <div className="bg-white rounded-lg shadow-md p-4">
            <h2 className="text-lg font-semibold mb-4">API Endpoints</h2>

            <div className="space-y-4">
              {tags.map(tag => (
                <div key={tag.name} className="space-y-2">
                  <button
                    onClick={() => {
                      setActiveTag(activeTag === tag.name ? null : tag.name);
                      setActiveEndpoint(null);
                    }}
                    className="flex items-center justify-between w-full text-left font-medium text-gray-700 hover:text-blue-600"
                  >
                    <span>{tag.name}</span>
                    <svg
                      className={`h-5 w-5 transform ${activeTag === tag.name ? 'rotate-180' : ''}`}
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>

                  {activeTag === tag.name && endpointsByTag[tag.name] && (
                    <div className="pl-4 space-y-2">
                      {endpointsByTag[tag.name].map((endpoint, index) => (
                        <button
                          key={`${endpoint.path}-${endpoint.method}`}
                          onClick={() => setActiveEndpoint(JSON.stringify({tag: tag.name, path: endpoint.path, method: endpoint.method}))}
                          className={`block w-full text-left text-sm ${
                            activeEndpoint === JSON.stringify({tag: tag.name, path: endpoint.path, method: endpoint.method})
                              ? 'text-blue-600 font-medium'
                              : 'text-gray-600 hover:text-blue-600'
                          }`}
                        >
                          <span className={`inline-block px-2 py-1 text-xs font-bold rounded mr-2 ${
                            endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                            endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                            endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                            endpoint.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {endpoint.method}
                          </span>
                          {endpoint.path}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="w-full md:w-3/4">
          <div className="bg-white rounded-lg shadow-md p-6">
            {activeEndpoint ? (
              // Display endpoint details
              (() => {
                const { tag: tagName, path, method } = JSON.parse(activeEndpoint);
                const endpoint = endpointsByTag[tagName].find(
                  e => e.path === path && e.method === method
                );

                if (!endpoint) return <div>Endpoint not found</div>;

                const endpointDetails = (openApiSchema.paths[path] as any)[method.toLowerCase()];

                return (
                  <div>
                    <div className="flex items-center gap-2 mb-4">
                      <span className={`inline-block px-2 py-1 text-xs font-bold rounded ${
                        endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                        endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                        endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                        endpoint.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {endpoint.method}
                      </span>
                      <h2 className="text-xl font-semibold">{path}</h2>
                    </div>

                    <div className="mb-6">
                      <h3 className="text-lg font-medium mb-2">{endpoint.summary}</h3>
                      <p className="text-gray-600">{endpoint.description}</p>
                    </div>

                    {/* Request */}
                    {endpointDetails.requestBody && (
                      <div className="mb-6">
                        <h3 className="text-md font-medium mb-2">Request Body</h3>

                        {/* Required Parameters */}
                        <div className="mb-4">
                          <h4 className="text-sm font-medium mb-2">Required Parameters:</h4>
                          <ul className="list-disc pl-5 space-y-2">
                            {endpointDetails.requestBody.content['application/json'].schema.required.map((param: string) => {
                              const property = endpointDetails.requestBody.content['application/json'].schema.properties[param];
                              return (
                                <li key={param} className="text-sm">
                                  <code className="bg-gray-100 px-2 py-1 rounded font-bold">{param}</code>
                                  <span className="text-gray-600 ml-2">({property.type}): {property.description}</span>
                                </li>
                              );
                            })}
                          </ul>
                        </div>

                        {/* Optional Parameters */}
                        <div className="mb-4">
                          <h4 className="text-sm font-medium mb-2">Optional Parameters:</h4>
                          <ul className="list-disc pl-5 space-y-2">
                            {Object.entries(endpointDetails.requestBody.content['application/json'].schema.properties)
                              .filter(([key]) => !endpointDetails.requestBody.content['application/json'].schema.required.includes(key))
                              .map(([key, value]: [string, any]) => (
                                <li key={key} className="text-sm">
                                  <code className="bg-gray-100 px-2 py-1 rounded">{key}</code>
                                  <span className="text-gray-600 ml-2">({value.type}): {value.description}</span>
                                  {value.default !== undefined && (
                                    <span className="text-gray-500 ml-1">(default: {String(value.default)})</span>
                                  )}
                                </li>
                              ))
                            }
                          </ul>
                        </div>

                        {/* Examples */}
                        {endpointDetails.requestBody.content['application/json'].examples && (
                          <div className="mt-4">
                            <h4 className="text-sm font-medium mb-2">Examples:</h4>
                            <div className="space-y-4">
                              {Object.entries(endpointDetails.requestBody.content['application/json'].examples).map(([name, example]: [string, any]) => (
                                <div key={name} className="bg-gray-50 p-4 rounded-md">
                                  <h5 className="text-sm font-medium mb-2">{name}:</h5>
                                  <pre className="text-sm overflow-x-auto">
                                    {JSON.stringify(example.value, null, 2)}
                                  </pre>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Responses */}
                    <div className="mb-6">
                      <h3 className="text-md font-medium mb-2">Responses</h3>
                      <div className="space-y-4">
                        {Object.entries(endpointDetails.responses || {}).map(([code, response]) => (
                          <div key={code} className="bg-gray-50 p-4 rounded-md">
                            <div className="flex items-center gap-2 mb-2">
                              <span className={`inline-block px-2 py-1 text-xs font-bold rounded ${
                                code.startsWith('2') ? 'bg-green-100 text-green-800' :
                                code.startsWith('4') ? 'bg-yellow-100 text-yellow-800' :
                                code.startsWith('5') ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {code}
                              </span>
                              <span className="font-medium">{(response as any).description}</span>
                            </div>

                            {(response as any).content && (response as any).content['application/json'] && (
                              <div>
                                <h4 className="text-sm font-medium mb-2">Response Schema:</h4>
                                {(response as any).content['application/json'].schema.$ref ? (
                                  <div className="text-sm">
                                    <p>Uses schema: <code className="bg-gray-200 px-1 py-0.5 rounded">
                                      {(response as any).content['application/json'].schema.$ref.split('/').pop()}
                                    </code></p>

                                    {/* Display schema details if it's a reference */}
                                    {(() => {
                                      const schemaName = (response as any).content['application/json'].schema.$ref.split('/').pop();
                                      const schema = openApiSchema.components.schemas[schemaName];

                                      if (schema) {
                                        return (
                                          <div className="mt-2 pl-4 border-l-2 border-gray-200">
                                            <p className="text-xs text-gray-600 mb-1">Properties:</p>
                                            <ul className="list-disc pl-4 space-y-1">
                                              {Object.entries(schema.properties).map(([propName, propDetails]: [string, any]) => (
                                                <li key={propName} className="text-xs">
                                                  <code className="bg-gray-100 px-1 py-0.5 rounded">{propName}</code>
                                                  <span className="text-gray-600 ml-1">
                                                    ({propDetails.type}): {propDetails.description}
                                                  </span>
                                                </li>
                                              ))}
                                            </ul>
                                          </div>
                                        );
                                      }
                                      return null;
                                    })()}
                                  </div>
                                ) : (
                                  <pre className="text-xs overflow-x-auto">
                                    {JSON.stringify((response as any).content['application/json'].schema, null, 2)}
                                  </pre>
                                )}
                              </div>
                            )}

                            {/* Example Response */}
                            {(response as any).content && (response as any).content['application/json'] &&
                             (response as any).content['application/json'].examples && (
                              <div className="mt-4">
                                <h4 className="text-sm font-medium mb-2">Example Response:</h4>
                                <pre className="text-xs overflow-x-auto">
                                  {JSON.stringify(
                                    Object.values((response as any).content['application/json'].examples)[0].value,
                                    null, 2
                                  )}
                                </pre>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                );
              })()
            ) : (
              // Display API overview
              <div>
                <h2 className="text-xl font-semibold mb-4">API Overview</h2>
                <p className="mb-4">{openApiSchema.info.description}</p>

                <h3 className="text-lg font-medium mb-2">Base URLs</h3>
                <ul className="list-disc pl-5 mb-6 space-y-1">
                  {openApiSchema.servers.map((server, index) => (
                    <li key={index}>
                      <code className="bg-gray-100 px-2 py-1 rounded">{server.url}</code> - {server.description}
                    </li>
                  ))}
                </ul>

                <h3 className="text-lg font-medium mb-2">Available Endpoints</h3>
                <p className="mb-4">Select an endpoint from the sidebar to view detailed documentation.</p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {tags.map(tag => (
                    <div key={tag.name} className="border rounded-md p-4">
                      <h4 className="font-medium mb-2">{tag.name}</h4>
                      <p className="text-sm text-gray-600 mb-2">{tag.description}</p>
                      <button
                        onClick={() => {
                          setActiveTag(tag.name);
                          setActiveEndpoint(null);
                        }}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        View endpoints →
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
