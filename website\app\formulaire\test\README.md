# Form Validation Test

This directory contains a test page for the form validation system.

## Overview

The test page is a simple page that renders the multi-step form with validation. It's used to test the validation system in isolation.

## How to Use

1. Navigate to `/formulaire/test` in your browser.
2. Fill out the form and test the validation.
3. Try submitting the form with invalid data to see the validation errors.
4. Try submitting the form with valid data to see the form submission process.

## Validation Rules

- **Text Fields**: Most text fields require at least 3 characters.
- **Email**: Must be a valid email address format.
- **Phone Numbers**: Must match a specific phone number format.
- **Required Fields**: All required fields must be filled out.
- **Consent Checkboxes**: Must be checked.

## Testing Scenarios

Here are some testing scenarios to try:

1. **Empty Required Fields**: Leave required fields empty and try to proceed to the next step.
2. **Short Text**: Enter less than 3 characters in text fields and try to proceed.
3. **Invalid Email**: Enter an invalid email address and try to proceed.
4. **Invalid Phone**: Enter an invalid phone number and try to proceed.
5. **Unchecked Consent**: Leave the consent checkbox unchecked and try to proceed.
6. **Valid Data**: Fill out all fields with valid data and proceed through all steps.

## Notes

- The validation system is integrated with the form components using the `useFormValidation` hook.
- Each form field is wrapped in a `FormField` component that displays validation errors.
- The validation schemas are defined in `app/formulaire/validation/schemas.ts`.
- The validation hook is defined in `app/formulaire/validation/useFormValidation.ts`.
