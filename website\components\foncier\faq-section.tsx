"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ChevronDown, ChevronUp, HelpCircle, ArrowRight, FileText, Clock, Users, Calculator, Scale } from "lucide-react";
import { getFoncierContent } from "@/app/cms/utils/foncier";

interface FaqSectionProps {
  customFaqs?: any[];
}

export function FaqSection({ customFaqs = [] }: FaqSectionProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [openFaq, setOpenFaq] = useState<number | null>(1);
  const sectionRef = useRef<HTMLElement>(null);
  const faqContent = getFoncierContent().faq;
  const iconMap = { FileText, Clock, Users, Calculator, Scale };
  const cmsFaqs = faqContent?.faqs && Array.isArray(faqContent.faqs) && faqContent.faqs.length > 0 ? faqContent.faqs.map((faq: any) => ({
    ...faq,
    icon: iconMap[faq.icon] || FileText
  })) : [];
  const mergedFaqs = (customFaqs && customFaqs.length > 0) ? [...customFaqs.map((faq: any, idx: number) => ({
      ...faq,
      id: faq.id || `custom-${idx}`,
      icon: iconMap[faq.icon] || FileText
    }))] : [
    ...cmsFaqs,
    ...customFaqs.map((faq: any, idx: number) => ({
      ...faq,
      id: faq.id || `custom-${idx}`,
      icon: iconMap[faq.icon] || FileText
    }))
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  return (
    <section ref={sectionRef} id="faq" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
              FAQ
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {faqContent?.title || "Questions fréquentes sur le foncier"}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {faqContent?.description || "Retrouvez ici les réponses aux questions les plus courantes concernant la sécurisation foncière au Cameroun."}
            </p>
          </div>

          {/* FAQ Accordion */}
          <div className="space-y-4 mb-16">
            {mergedFaqs.map((faq, index) => {
              const Icon = faq.icon;
              const isOpen = openFaq === faq.id;
              
              return (
                <div
                  key={faq.id}
                  className={`transition-all duration-700 ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                  }`}
                  style={{ transitionDelay: `${index * 100}ms` }}
                >
                  <Card className={`transition-all duration-300 cursor-pointer border-2 ${
                    isOpen ? 'border-primary shadow-lg' : 'border-gray-100 hover:border-primary/30 hover:shadow-md'
                  }`}>
                    <CardContent className="p-0">
                      <button
                        onClick={() => toggleFaq(faq.id)}
                        className="w-full p-6 text-left flex items-center justify-between group"
                      >
                        <div className="flex items-start space-x-4 flex-1">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 transition-colors duration-300 ${
                            isOpen ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600 group-hover:bg-primary/10 group-hover:text-primary'
                          }`}>
                            <Icon className="w-5 h-5" />
                          </div>
                          <div className="flex-1">
                            <h3 className={`font-semibold text-lg transition-colors duration-300 ${
                              isOpen ? 'text-primary' : 'text-gray-900 group-hover:text-primary'
                            }`}>
                              {faq.question}
                            </h3>
                          </div>
                        </div>
                        
                        <div className={`transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}>
                          {isOpen ? (
                            <ChevronUp className="w-5 h-5 text-primary" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-gray-400 group-hover:text-primary" />
                          )}
                        </div>
                      </button>

                      {isOpen && (
                        <div className="px-6 pb-6 space-y-4 border-t border-gray-100">
                          <p className="text-gray-700 leading-relaxed pt-4">
                            {faq.answer}
                          </p>
                          
                          {faq.details && (
                            <div className="space-y-3">
                              <h4 className="font-semibold text-gray-900 text-sm">Points clés :</h4>
                              <ul className="grid md:grid-cols-2 gap-2">
                                {faq.details.map((detail, detailIndex) => (
                                  <li key={detailIndex} className="flex items-start space-x-2 text-sm text-gray-600">
                                    <div className="w-1.5 h-1.5 bg-primary rounded-full flex-shrink-0 mt-2"></div>
                                    <span>{detail}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Additional Help */}
          {/* <div className={`bg-white rounded-2xl p-8 shadow-lg border border-gray-100 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="text-center space-y-6">
              <h3 className="text-2xl font-bold text-gray-900">
                Vous ne trouvez pas votre réponse ?
              </h3>
              
              <p className="text-gray-600 max-w-2xl mx-auto">
                Notre équipe d'experts est à votre disposition pour répondre 
                à toutes vos questions spécifiques sur vos projets fonciers.
              </p>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <HelpCircle className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold text-gray-900">Consultation gratuite</h4>
                  <p className="text-sm text-gray-600">30 minutes d'échange avec un expert</p>
                </div>
                
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <FileText className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold text-gray-900">Documentation complète</h4>
                  <p className="text-sm text-gray-600">Guides détaillés et ressources</p>
                </div>
                
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <Users className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold text-gray-900">Support personnalisé</h4>
                  <p className="text-sm text-gray-600">Accompagnement sur mesure</p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="group">
                  <Link href="/contact" className="flex items-center">
                    Poser une question
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <Link href="/foncier/ressources">
                    Voir la FAQ complète
                  </Link>
                </Button>
              </div>
            </div>
          </div> */}
        </div>
      </div>
    </section>
  );
}
