"use client";

// @ts-ignore
import { MultiStepForm } from "./components/multi-step-form";
import { useLanguage } from "../translations/language-context";
import { LanguageSwitcher } from "../components/language-switcher";

export default function FormulairePage() {
  const { t } = useLanguage();

  return (
    <main className="min-h-screen px-4">
      <div className="container mx-auto">
        <div className="flex justify-end mb-4">
          <LanguageSwitcher />
        </div>
        <MultiStepForm />
      </div>
    </main>
  );
}