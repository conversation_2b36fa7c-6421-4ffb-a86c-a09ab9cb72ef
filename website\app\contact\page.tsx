"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { MapPin, Phone, Mail, MessageCircle } from "lucide-react";
import { getContactInfo } from "@/app/cms/utils/contact";

export default function ContactPage() {
  const contact = getContactInfo();
  const [formData, setFormData] = useState(
    Object.fromEntries((contact.form.fields || []).map(f => [f.name, ""]))
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implémenter l'envoi du formulaire
    console.log("Formulaire soumis:", formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    console.log(formData)
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary to-primary/80 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            {contact.heroSection?.title}
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            {contact.heroSection?.subtitle}
          </p>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 w-full">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold mb-8">Envoyez-nous un message</h2>
              <Card>
                <CardHeader>
                  <CardTitle>Formulaire de Contact</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      {contact.form.fields.slice(0,2).map(field => (
                        <div key={field.name}>
                          <label htmlFor={field.name} className="block text-sm font-medium mb-2">
                            {field.label} {field.type !== "tel" && '*'}
                          </label>
                          <Input
                            id={field.name}
                            name={field.name}
                            type={field.type}
                            required={field.type !== "tel"}
                            value={formData[field.name]}
                            onChange={handleChange}
                            placeholder={field.placeholder}
                          />
                        </div>
                      ))}
                    </div>
                    <div className="grid md:grid-cols-2 gap-4">
                      {contact.form.fields.slice(2,4).map(field => (
                        <div key={field.name}>
                          <label htmlFor={field.name} className="block text-sm font-medium mb-2">
                            {field.label} {field.type !== "tel" && '*'}
                          </label>
                          <Input
                            id={field.name}
                            name={field.name}
                            type={field.type}
                            required={field.type !== "tel"}
                            value={formData[field.name]}
                            onChange={handleChange}
                            placeholder={field.placeholder}
                          />
                        </div>
                      ))}
                    </div>
                    <div>
                      <label htmlFor={contact.form.fields[4].name} className="block text-sm font-medium mb-2">
                        {contact.form.fields[4].label} *
                      </label>
                      <Textarea
                        id={contact.form.fields[4].name}
                        name={contact.form.fields[4].name}
                        required
                        rows={6}
                        value={formData[contact.form.fields[4].name]}
                        onChange={handleChange}
                        placeholder={contact.form.fields[4].placeholder}
                      />
                    </div>
                    <Button type="submit" className="w-full">
                      {contact.form.submitLabel}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Contact Info */}
            <div>
              <h2 className="text-3xl font-bold mb-8">Nos Coordonnées</h2>
              <div className="space-y-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <MapPin className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2">{contact.addressCard.title}</h3>
                        <p className="text-gray-600">{contact.addressCard.content}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center">
                        <Phone className="w-6 h-6 text-accent" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2">{contact.phoneCard.title}</h3>
                        <p className="text-gray-600">{contact.phoneCard.content}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Mail className="w-6 h-6 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2">{contact.emailCard.title}</h3>
                        <p className="text-gray-600">{contact.emailCard.content}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <MessageCircle className="w-6 h-6 text-green-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2">{contact.whatsappCard.title}</h3>
                        <p className="text-gray-600">{contact.whatsappCard.content}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              {/* Quick Actions */}
              <div className="mt-8">
                <h3 className="text-xl font-semibold mb-4">Actions Rapides</h3>
                <div className="space-y-3">
                  {contact.quickActions.map((action, idx) => (
                    <Button asChild variant="outline" className="w-full justify-start" key={idx}>
                      <Link href={action.url}>
                        {action.label}
                      </Link>
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}