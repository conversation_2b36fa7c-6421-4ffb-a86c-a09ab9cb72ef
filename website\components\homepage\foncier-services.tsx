"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Shield, FileText, Users, Scale, CheckCircle } from "lucide-react";
import { getHomeContent } from "@/app/cms/utils/home";

export function FoncierServices() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeService, setActiveService] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const iconMap: Record<string, any> = {
    Shield,
    FileText,
    Users,
    Scale
  };

  const content = getHomeContent().foncierServices;
  const services = content.services;

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto flex-col ">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
              <Shield className="w-4 h-4 mr-2" />
              {content.badge}
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {content.title}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {content.subtitle}
            </p>
          </div>

          {/* Services Grid */}
          <div className="grid lg:grid-cols-2 gap-8 mb-16">
            {services.map((service, index) => {
              const Icon = iconMap[service.icon] || Shield;
              const isActive = activeService === index;
              return (
                <div
                  key={index}
                  className={`transition-all duration-700 ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                  }`}
                  style={{ transitionDelay: `${index * 150}ms` }}
                >
                  <Card
                    className={`h-full transition-all duration-300 hover:shadow-lg cursor-pointer group border-2 ${
                      isActive ? 'border-primary shadow-lg bg-primary/5' : 'border-gray-100 hover:border-primary/30'
                    }`}
                    onMouseEnter={() => setActiveService(index)}
                  >
                    <CardHeader className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 ${
                          isActive ? 'bg-primary text-white' : 'bg-primary/10 text-primary group-hover:bg-primary group-hover:text-white'
                        }`}>
                          <Icon className="w-6 h-6" />
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                            {service.title}
                          </CardTitle>
                        </div>
                      </div>

                      <CardDescription className="text-gray-600 leading-relaxed">
                        {service.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-900 text-sm">Prestations incluses :</h4>
                        <ul className="space-y-2">
                          {service.features.map((feature: string, featureIndex: number) => (
                            <li key={featureIndex} className="flex items-center space-x-3 text-sm text-gray-600">
                              <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
            
          </div>
          <Button className="col-span-2 mt-8 w-full max-w-[400px]" asChild size="lg">
              <Link href="/foncier#services" className="flex items-center ">
                Decouvrez tous nos services fonciers 
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>

          {/* Stats Section */}
          {/* <div className={`bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 mb-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="grid md:grid-cols-4 gap-8 text-center">
              <div className="space-y-2">
                <div className="text-3xl font-bold text-primary">300+</div>
                <div className="text-sm text-gray-600">Titres sécurisés</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-primary">95%</div>
                <div className="text-sm text-gray-600">Taux de réussite</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-primary">15j</div>
                <div className="text-sm text-gray-600">Délai moyen</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-primary">24/7</div>
                <div className="text-sm text-gray-600">Support client</div>
              </div>
            </div>
          </div> */}

          {/* CTA Section */}
          {/* <div className={`text-center space-y-6 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Besoin d'une expertise foncière ?
              </h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Nos experts en droit foncier sont à votre disposition pour analyser
                votre situation et vous proposer les meilleures solutions.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="group">
                  <Link href="/foncier" className="flex items-center">
                    Voir tous nos services fonciers
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <Link href="/formulaire">
                    Demander un accompagnement
                  </Link>
                </Button>
              </div>
            </div>
          </div> */}
        </div>
      </div>
    </section>
  );
}
