{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/construction/services/construction-services-client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/construction/services/construction-services-client.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/construction/services/construction-services-client.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8T,GAC3V,4FACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/construction/services/construction-services-client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/construction/services/construction-services-client.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/construction/services/construction-services-client.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/cms/utils/construction-services.ts"], "sourcesContent": ["import constructionServices from \"@/app/cms/data/construction-services.json\";\n\nexport function getConstructionServicesContent() {\n  return constructionServices;\n}\n\nexport function getConstructionServicesMetadata() {\n  return constructionServices.metadata;\n}\n\nexport function getConstructionServicesHero() {\n  return constructionServices.hero;\n}\n\nexport function getConstructionServicesSection() {\n  return constructionServices.servicesSection;\n}\n\nexport function getConstructionServicesWhyChoose() {\n  return constructionServices.whyChoose;\n}\n\nexport function getConstructionServicesFinalCta() {\n  return constructionServices.finalCta;\n}\n\nexport function getConstructionServiceCategories() {\n  return constructionServices.servicesSection.categories;\n}\n\nexport function getConstructionServiceCategoryById(id: string) {\n  return constructionServices.servicesSection.categories.find(category => category.id === id);\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB;AAC7B;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,QAAQ;AACtC;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,IAAI;AAClC;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,eAAe;AAC7C;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,SAAS;AACvC;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,QAAQ;AACtC;AAEO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAoB,CAAC,eAAe,CAAC,UAAU;AACxD;AAEO,SAAS,mCAAmC,EAAU;IAC3D,OAAO,sHAAA,CAAA,UAAoB,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC1F", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/construction/services/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\nimport ConstructionServicesClient from \"./construction-services-client\";\nimport { getConstructionServicesContent } from \"@/app/cms/utils/construction-services\";\n\nconst constructionServicesData = getConstructionServicesContent();\n\nexport const metadata: Metadata = {\n  title: constructionServicesData.metadata.title,\n  description: constructionServicesData.metadata.description,\n  keywords: constructionServicesData.metadata.keywords,\n  openGraph: constructionServicesData.metadata.openGraph,\n  alternates: {\n    canonical: constructionServicesData.metadata.canonical\n  }\n};\n\nexport default function ConstructionServicesPage() {\n  return <ConstructionServicesClient />;\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,2BAA2B,CAAA,GAAA,+IAAA,CAAA,iCAA8B,AAAD;AAEvD,MAAM,WAAqB;IAChC,OAAO,yBAAyB,QAAQ,CAAC,KAAK;IAC9C,aAAa,yBAAyB,QAAQ,CAAC,WAAW;IAC1D,UAAU,yBAAyB,QAAQ,CAAC,QAAQ;IACpD,WAAW,yBAAyB,QAAQ,CAAC,SAAS;IACtD,YAAY;QACV,WAAW,yBAAyB,QAAQ,CAAC,SAAS;IACxD;AACF;AAEe,SAAS;IACtB,qBAAO,6WAAC,sKAAA,CAAA,UAA0B;;;;;AACpC", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/next%4015.3.1_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/next%4015.3.1_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA,eAAwB;YAAA;gBAAsB,EAAC,UAAA;oBAAA;oBAAA;wBAEzG,YAAA;4BAAA;4BAAA,CACA,kCAD4D;4BAC5D,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kCAChDY,QAAAA,CAAAA,CAAY;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BACVC,MAAMZ,UAAUa,QAAQ;;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA;oBAAAA,CAAU;iBAAA;;iBACV,2CAA2C;sBAC3CC,IAAAA,CAAAA;YAAAA;SAAAA,CAAY;;SACZC,UAAU;cACVC,IAAAA;YAAAA,CAAU,EAAE,GAAA;gBACd,OAAA,QAAA;wBAAA;4BACAC,KAAAA,CAAAA,GAAAA,2UAAAA,CAAAA,CAAU,qBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,+RAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACRC,OAAAA,GAAAA,+RAAAA,CAAAA,EAAYnB,QAAAA,CAAAA,KAAAA,CAAAA,CAAAA,EAAAA,+RAAAA,CAAAA,UAAAA,CAAAA,MAAAA,EAAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACA;qBAAA", "ignoreList": [0], "debugId": null}}]}