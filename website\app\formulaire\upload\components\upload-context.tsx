"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

// Define the file types
export type FileStatus = "idle" | "uploading" | "success" | "error";

export interface UploadedFile {
  file: File;
  preview: string | null;
  status: FileStatus;
  progress?: number;
  error?: string;
  name?: string; // Custom name/description for the file
  validationError?: string; // Validation error message
}

// Define the context state
interface UploadContextState {
  mainFile: UploadedFile | null;
  relatedFiles: UploadedFile[];
  setMainFile: (file: UploadedFile | null) => void;
  addRelatedFile: (file: UploadedFile) => void;
  removeRelatedFile: (index: number) => void;
  updateFileStatus: (
    fileType: "main" | "related",
    index: number,
    status: FileStatus,
    error?: string,
    progress?: number
  ) => void;
  updateFileName: (
    fileType: "main" | "related",
    index: number,
    name: string
  ) => void;
  clearAllFiles: () => void;
}

// Create the context
const UploadContext = createContext<UploadContextState | undefined>(undefined);

// Create a provider component
export function UploadProvider({ children }: { children: ReactNode }) {
  const [mainFile, setMainFile] = useState<UploadedFile | null>(null);
  const [relatedFiles, setRelatedFiles] = useState<UploadedFile[]>([]);

  const addRelatedFile = (file: UploadedFile) => {
    setRelatedFiles((prev) => [...prev, file]);
  };

  const removeRelatedFile = (index: number) => {
    setRelatedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const updateFileStatus = (
    fileType: "main" | "related",
    index: number,
    status: FileStatus,
    error?: string,
    progress?: number
  ) => {
    if (fileType === "main" && mainFile) {
      setMainFile({
        ...mainFile,
        status,
        error,
        progress,
      });
    } else if (fileType === "related") {
      setRelatedFiles((prev) =>
        prev.map((file, i) =>
          i === index
            ? { ...file, status, error, progress }
            : file
        )
      );
    }
  };

  const updateFileName = (
    fileType: "main" | "related",
    index: number,
    name: string
  ) => {
    if (fileType === "main" && mainFile) {
      setMainFile({
        ...mainFile,
        name,
      });
    } else if (fileType === "related") {
      setRelatedFiles((prev) =>
        prev.map((file, i) =>
          i === index
            ? { ...file, name }
            : file
        )
      );
    }
  };

  const clearAllFiles = () => {
    setMainFile(null);
    setRelatedFiles([]);
  };

  return (
    <UploadContext.Provider
      value={{
        mainFile,
        relatedFiles,
        setMainFile,
        addRelatedFile,
        removeRelatedFile,
        updateFileStatus,
        updateFileName,
        clearAllFiles,
      }}
    >
      {children}
    </UploadContext.Provider>
  );
}

// Create a hook to use the context
export function useUploadContext() {
  const context = useContext(UploadContext);
  if (context === undefined) {
    throw new Error("useUploadContext must be used within an UploadProvider");
  }
  return context;
}
