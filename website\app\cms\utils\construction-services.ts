import constructionServices from "@/app/cms/data/construction-services.json";

export function getConstructionServicesContent() {
  return constructionServices;
}

export function getConstructionServicesMetadata() {
  return constructionServices.metadata;
}

export function getConstructionServicesHero() {
  return constructionServices.hero;
}

export function getConstructionServicesSection() {
  return constructionServices.servicesSection;
}

export function getConstructionServicesWhyChoose() {
  return constructionServices.whyChoose;
}

export function getConstructionServicesFinalCta() {
  return constructionServices.finalCta;
}

export function getConstructionServiceCategories() {
  return constructionServices.servicesSection.categories;
}

export function getConstructionServiceCategoryById(id: string) {
  return constructionServices.servicesSection.categories.find(category => category.id === id);
}
