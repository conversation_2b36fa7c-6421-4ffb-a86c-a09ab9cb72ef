import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";

export default function FoncierRessourcesPage() {
  const guides = [
    {
      title: "Guide complet du Titre Foncier",
      description: "Tout ce que vous devez savoir sur l'obtention d'un titre foncier au Cameroun",
      type: "PDF",
      pages: "24 pages",
      downloadUrl: "#"
    },
    {
      title: "Procédures d'Immatriculation",
      description: "Étapes détaillées pour l'immatriculation foncière",
      type: "PDF",
      pages: "16 pages",
      downloadUrl: "#"
    },
    {
      title: "Modèles de Documents",
      description: "Templates et modèles de documents fonciers",
      type: "ZIP",
      pages: "12 fichiers",
      downloadUrl: "#"
    },
    {
      title: "Réglementation Foncière 2024",
      description: "Dernières mises à jour de la législation foncière camerounaise",
      type: "PDF",
      pages: "32 pages",
      downloadUrl: "#"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary to-primary/80 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Ressources Foncières
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Guides, documents et informations utiles pour vos démarches foncières
          </p>
        </div>
      </section>

      {/* Guides Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Guides et Documents</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Téléchargez nos guides pratiques pour mieux comprendre les procédures foncières
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {guides.map((guide, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-xl mb-2">{guide.title}</CardTitle>
                      <p className="text-gray-600 text-sm">{guide.description}</p>
                    </div>
                    <div className="text-right">
                      <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs font-medium">
                        {guide.type}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">{guide.pages}</span>
                    <Button asChild variant="outline" size="sm">
                      <Link href={guide.downloadUrl}>
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Télécharger
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Questions Fréquentes</h2>
              <p className="text-gray-600">
                Réponses aux questions les plus courantes sur le droit foncier
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "Quelle est la différence entre un titre foncier et un acte de vente ?",
                  reponse: "L'acte de vente est un contrat entre vendeur et acheteur, tandis que le titre foncier est un document officiel délivré par l'État qui garantit définitivement votre droit de propriété."
                },
                {
                  question: "Combien coûte l'obtention d'un titre foncier ?",
                  reponse: "Les frais varient selon la superficie et la localisation du terrain. Comptez entre 300 000 et 800 000 FCFA pour les frais administratifs, plus nos honoraires."
                },
                {
                  question: "Peut-on obtenir un titre foncier sur un terrain hérité ?",
                  reponse: "Oui, mais il faut d'abord régulariser la succession et obtenir un acte de partage ou une attestation d'hérédité."
                },
                {
                  question: "Que faire si mon terrain est en zone rurale ?",
                  reponse: "Les terrains ruraux peuvent faire l'objet d'un titre foncier, mais la procédure peut être différente selon le statut du terrain (domaine national, privé de l'État, etc.)."
                }
              ].map((item, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-3 text-primary">{item.question}</h3>
                    <p className="text-gray-600">{item.reponse}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Besoin d'Aide Personnalisée ?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Nos experts sont à votre disposition pour répondre à vos questions spécifiques
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="secondary">
              <Link href="/contact">Contacter un Expert</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-primary">
              <Link href="/formulaire">Demander un Devis</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}