"use client";

import { useState, useRef, useEffect } from "react";
import { useFormContext } from "./form-context";
import type { FormData } from "./form-context";
import { uploadFileToGoogleDrive, FileUploadResult } from "../utils/file-upload";
import { AlertCircle, CheckCircle, RefreshCw, Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/app/translations/language-context";

interface FileUploadProps {
  id: string;
  accept?: string;
  maxSize?: number; // in bytes
  label?: string;
  required?: boolean;
  onChange?: (file: File | null) => void;
  "data-testid"?: string;
  showErrors?: boolean;
  errorMessage?: string;
}

export function FileUpload({
  id,
  accept = ".pdf,.jpg,.png",
  maxSize = 2 * 1024 * 1024, // 2MB default
  label,
  required = false,
  onChange,
  "data-testid": testId,
}: FileUploadProps) {
  const { t } = useLanguage();
  const { formData, updateFormData } = useFormContext();
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [localFile, setLocalFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get the upload status for this file
  const uploadStatus = localFile ? formData.uploadStatus?.[localFile.name] || 'idle' : 'idle';
  const uploadError = localFile ? formData.uploadErrors?.[localFile.name] : undefined;

  // Handle file selection
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;

    // Clear previous file data
    setFilePreview(null);
    setLocalFile(null);

    if (file) {
      console.log('FileUpload: File selected:', file.name, 'Size:', file.size, 'Type:', file.type);

      // Check file size
      if (maxSize && file.size > maxSize) {
        console.warn('FileUpload: File too large:', file.size, 'Max size:', maxSize);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
        return;
      }

      // Save the file to local state
      setLocalFile(file);

      // Create preview for image files
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (event) => {
          setFilePreview(event.target?.result as string);
        };
        reader.readAsDataURL(file);
      }

      // Call the onChange handler if provided
      if (onChange) {
        console.log('FileUpload: Calling onChange handler with file:', file.name);
        onChange(file);
      }

      // Upload the file immediately if we have a user name
      if (formData.fullName) {
        console.log('FileUpload: User name available, uploading file immediately:', file.name);

        // Update status to uploading
        const newUploadStatus = { ...(formData.uploadStatus || {}) };
        newUploadStatus[file.name] = 'uploading';
        updateFormData({ uploadStatus: newUploadStatus });

        // Use a timeout to ensure the form data is updated before uploading
        setTimeout(async () => {
          try {
            console.log('FileUpload: Starting upload to Google Drive for file:', file.name);
            const result = await uploadFileToGoogleDrive(file, formData, updateFormData);
            console.log('FileUpload: Upload completed with result:', result);

            // Update status based on result
            const updatedUploadStatus = { ...(formData.uploadStatus || {}) };
            updatedUploadStatus[file.name] = result.success ? 'success' : 'error';

            // Update errors if any
            const updatedUploadErrors = { ...(formData.uploadErrors || {}) };
            if (!result.success && result.error) {
              updatedUploadErrors[file.name] = result.error;
            } else {
              delete updatedUploadErrors[file.name];
            }

            updateFormData({
              uploadStatus: updatedUploadStatus,
              uploadErrors: updatedUploadErrors
            });
          } catch (error) {
            console.error('FileUpload: Error uploading file:', error);

            // Update status to error
            const updatedUploadStatus = { ...(formData.uploadStatus || {}) };
            updatedUploadStatus[file.name] = 'error';

            // Update errors
            const updatedUploadErrors = { ...(formData.uploadErrors || {}) };
            updatedUploadErrors[file.name] = error instanceof Error ? error.message : 'Unknown error';

            updateFormData({
              uploadStatus: updatedUploadStatus,
              uploadErrors: updatedUploadErrors
            });
          }
        }, 100); // Small delay to ensure form data is updated
      } else {
        console.log('FileUpload: No user name available, skipping immediate upload');
      }
    } else {
      console.log('FileUpload: No file selected or file selection canceled');
      if (onChange) {
        onChange(null);
      }
    }
  };

  // Handle retry
  const handleRetry = async () => {
    if (localFile) {
      console.log('FileUpload: Retrying upload for file:', localFile.name);

      // Update status to uploading
      const newUploadStatus = { ...(formData.uploadStatus || {}) };
      newUploadStatus[localFile.name] = 'uploading';
      updateFormData({ uploadStatus: newUploadStatus });

      try {
        // Clear any previous errors
        const updatedUploadErrors = { ...(formData.uploadErrors || {}) };
        delete updatedUploadErrors[localFile.name];
        updateFormData({ uploadErrors: updatedUploadErrors });

        // Trigger the upload
        const result = await uploadFileToGoogleDrive(localFile, formData, updateFormData);
        console.log('FileUpload: Retry completed with result:', result);

        // Update status based on result
        const updatedUploadStatus = { ...(formData.uploadStatus || {}) };
        updatedUploadStatus[localFile.name] = result.success ? 'success' : 'error';

        // Update errors if any
        if (!result.success && result.error) {
          const newUploadErrors = { ...(formData.uploadErrors || {}) };
          newUploadErrors[localFile.name] = result.error;
          updateFormData({
            uploadStatus: updatedUploadStatus,
            uploadErrors: newUploadErrors
          });
        } else {
          updateFormData({
            uploadStatus: updatedUploadStatus
          });
        }
      } catch (error) {
        console.error('FileUpload: Error retrying upload:', error);

        // Update status to error
        const updatedUploadStatus = { ...(formData.uploadStatus || {}) };
        updatedUploadStatus[localFile.name] = 'error';

        // Update errors
        const updatedUploadErrors = { ...(formData.uploadErrors || {}) };
        updatedUploadErrors[localFile.name] = error instanceof Error ? error.message : 'Unknown error';

        updateFormData({
          uploadStatus: updatedUploadStatus,
          uploadErrors: updatedUploadErrors
        });
      }
    } else {
      console.log('FileUpload: No file to retry');
    }
  };

  // If the user name changes and we have a file but haven't uploaded it yet, upload it
  useEffect(() => {
    // Skip if we don't have a file or a name
    if (!localFile || !formData.fullName) {
      console.log('FileUpload useEffect: Missing file or user name, skipping upload');
      return;
    }

    // Skip if the file is already being tracked and not in error state
    if (formData.uploadStatus &&
        formData.uploadStatus[localFile.name] &&
        formData.uploadStatus[localFile.name] !== 'error') {
      console.log('FileUpload useEffect: File already being tracked, skipping upload');
      return;
    }

    console.log('FileUpload useEffect: Uploading file:', localFile.name);

    // Update status to uploading
    const newUploadStatus = { ...(formData.uploadStatus || {}) };
    newUploadStatus[localFile.name] = 'uploading';
    updateFormData({ uploadStatus: newUploadStatus });

    // Upload the file
    const uploadFile = async () => {
      if (localFile) {
        try {
          console.log('FileUpload useEffect: Starting upload');
          const result = await uploadFileToGoogleDrive(localFile, formData, updateFormData);
          console.log('FileUpload useEffect: Upload completed with result:', result);

          // Update status based on result
          const updatedUploadStatus = { ...(formData.uploadStatus || {}) };
          updatedUploadStatus[localFile.name] = result.success ? 'success' : 'error';

          // Update errors if any
          const updatedUploadErrors = { ...(formData.uploadErrors || {}) };
          if (!result.success && result.error) {
            updatedUploadErrors[localFile.name] = result.error;
          } else {
            delete updatedUploadErrors[localFile.name];
          }

          updateFormData({
            uploadStatus: updatedUploadStatus,
            uploadErrors: updatedUploadErrors
          });
        } catch (error) {
          console.error('FileUpload useEffect: Error uploading file:', error);

          // Update status to error
          const updatedUploadStatus = { ...(formData.uploadStatus || {}) };
          updatedUploadStatus[localFile.name] = 'error';

          // Update errors
          const updatedUploadErrors = { ...(formData.uploadErrors || {}) };
          updatedUploadErrors[localFile.name] = error instanceof Error ? error.message : 'Unknown error';

          updateFormData({
            uploadStatus: updatedUploadStatus,
            uploadErrors: updatedUploadErrors
          });
        }
      }
    };

    uploadFile();
    // We're using a stable dependency array with just the file name and user name
    // This prevents the effect from running unnecessarily and avoids the changing array size error
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [localFile?.name, formData.fullName]);

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        {label && (
          <label htmlFor={id} className="text-sm font-medium">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        {/* Status indicator */}
        {localFile && (
          <div className="flex items-center space-x-1">
            {uploadStatus === 'uploading' && (
              <div className="flex items-center text-blue-500">
                <Upload className="h-4 w-4 animate-pulse mr-1" />
                <span className="text-xs font-semibold">{t('common.uploading')}</span>
              </div>
            )}

            {uploadStatus === 'success' && (
              <div className="flex items-center text-green-500">
                <CheckCircle className="h-4 w-4 mr-1" />
                <span className="text-xs">{t('common.uploaded')}</span>
              </div>
            )}

            {uploadStatus === 'error' && (
              <div className="flex items-center">
                <div className="flex items-center text-red-500">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  <span className="text-xs">{t('common.uploadFailed')}</span>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 ml-2"
                  onClick={handleRetry}
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  <span className="text-xs">{t('common.retry')}</span>
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      <div className={`border-2 border-dashed ${uploadStatus === 'uploading' ? 'border-blue-500 bg-blue-50' : 'border-input bg-background'} rounded-md p-4 focus-within:border-primary transition-colors duration-300`}>
        {uploadStatus === 'uploading' && (
          <div className="absolute top-0 left-0 w-full h-1 bg-blue-200 overflow-hidden">
            <div className="h-full bg-blue-500 animate-progress"></div>
          </div>
        )}
        <input
          id={id}
          type="file"
          accept={accept}
          className="w-full"
          onChange={handleFileChange}
          ref={fileInputRef}
          data-testid={testId}
          disabled={uploadStatus === 'uploading'}
        />

        {uploadError && (
          <div className="flex items-center mt-2 text-red-500 text-sm">
            <AlertCircle className="h-4 w-4 mr-1" />
            <span>{uploadError}</span>
          </div>
        )}

        {filePreview && (
          <div className="mt-2">
            <img
              src={filePreview}
              alt="Preview"
              className="max-h-32 max-w-full object-contain rounded-md"
            />
          </div>
        )}

        {localFile && !filePreview && (
          <div className="mt-2 text-sm text-gray-500">
            {localFile.name} ({(localFile.size / 1024).toFixed(1)} KB)
          </div>
        )}
      </div>
    </div>
  );
}
