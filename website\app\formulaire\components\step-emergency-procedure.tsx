"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useFormContext } from "./form-context";
import { useLanguage } from "@/app/translations/language-context";
import { FormField } from "./form-field";
import { emergencyProcedureSchema } from "../validation/schemas";
import useFormValidation from "../validation/useFormValidation";
import { hasActiveUploads } from "../utils/file-upload";

interface StepEmergencyProcedureProps {
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

export function StepEmergencyProcedure({
  onNext,
  onPrevious,
}: StepEmergencyProcedureProps) {
  const { t } = useLanguage();
  const { formData, updateFormData } = useFormContext();
  const [landStatus, setLandStatus] = useState<string>(formData.landStatus || "");

  // Get procedure options from translations
  const procedureOptions = t('stepEmergencyProcedure.procedureType.options') as unknown as string[];

  const [procedureTypes, setProcedureTypes] = useState<string[]>(
    formData.procedureTypes || []
  );

  const handleProcedureTypeChange = (type: string) => {
    const updatedTypes = procedureTypes.includes(type)
      ? procedureTypes.filter((t) => t !== type)
      : [...procedureTypes, type];

    setProcedureTypes(updatedTypes);
    updateFormData({ procedureTypes: updatedTypes });
  };

  // Initialize form validation
  const {
    validate,
    getFieldError,
    touchField,
    hasFieldError,
    validateField,
  } = useFormValidation(formData, emergencyProcedureSchema);

  // Handle field blur to validate individual fields
  const handleFieldBlur = (fieldName: string) => {
    touchField(fieldName);
    validateField(fieldName);
  };

  // Handle next button click
  const handleNext = () => {
    // Check if any files are currently uploading
    if (hasActiveUploads(formData)) {
      console.log('Cannot proceed while files are uploading');
      return;
    }

    // Validate the form
    const isValid = validate();

    if (isValid) {
      onNext();
    } else {
      // Focus the first field with an error
      const firstErrorField = document.querySelector('[aria-invalid="true"]') as HTMLElement;
      if (firstErrorField) {
        firstErrorField.focus();
      }
    }
  };

  return (
    <div className="space-y-8" data-testid="step-emergency-procedure">
      <h2 className="text-xl font-semibold">{t('stepEmergencyProcedure.title')}</h2>

      {/* Emergency Contacts */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">{t('stepEmergencyProcedure.emergencyContacts.title')}</h3>
        <p className="text-sm text-gray-600" data-testid="emergency-intro-text">
          {t('stepEmergencyProcedure.emergencyContacts.intro')}
        </p>

        {/* Emergency Contact 1 */}
        <div className="space-y-4 p-4 bg-gray-50 rounded-md">
          <h4 className="font-medium">{t('stepEmergencyProcedure.emergencyContacts.contact1.title')}</h4>

          <FormField
            id="emergencyContact1Name"
            label={t('stepEmergencyProcedure.emergencyContacts.contact1.name.label')}
            value={formData.emergencyContact1Name || ""}
            onChange={(value) => updateFormData({ emergencyContact1Name: value })}
            onBlur={() => handleFieldBlur('emergencyContact1Name')}
            placeholder={t('stepEmergencyProcedure.emergencyContacts.contact1.name.placeholder')}
            required={true}
            error={getFieldError('emergencyContact1Name')}
            data-testid="emergency-contact1-name-input"
          />

          <FormField
            id="emergencyContact1Phone"
            label={t('stepEmergencyProcedure.emergencyContacts.contact1.phone.label')}
            value={formData.emergencyContact1Phone || ""}
            onChange={(value) => updateFormData({ emergencyContact1Phone: value })}
            onBlur={() => handleFieldBlur('emergencyContact1Phone')}
            placeholder={t('stepEmergencyProcedure.emergencyContacts.contact1.phone.placeholder')}
            required={true}
            error={getFieldError('emergencyContact1Phone')}
            data-testid="emergency-contact1-phone-input"
          />

          <div className="space-y-2">
            <FormField
              id="emergencyContact1Relation"
              label={t('stepEmergencyProcedure.emergencyContacts.contact1.relation.label')}
              value={formData.emergencyContact1Relation || ""}
              onChange={(value) => updateFormData({ emergencyContact1Relation: value })}
              onBlur={() => handleFieldBlur('emergencyContact1Relation')}
              placeholder={t('stepEmergencyProcedure.emergencyContacts.contact1.relation.placeholder')}
              required={true}
              error={getFieldError('emergencyContact1Relation')}
              data-testid="emergency-contact1-relation-input"
            />
            <p className="text-xs text-gray-500">
              {t('stepEmergencyProcedure.emergencyContacts.contact1.relation.help')}
            </p>
          </div>
        </div>

        {/* Emergency Contact 2 */}
        <div className="space-y-4 p-4 bg-gray-50 rounded-md">
          <h4 className="font-medium">{t('stepEmergencyProcedure.emergencyContacts.contact2.title')}</h4>

          <FormField
            id="emergencyContact2Name"
            label={t('stepEmergencyProcedure.emergencyContacts.contact2.name.label')}
            value={formData.emergencyContact2Name || ""}
            onChange={(value) => updateFormData({ emergencyContact2Name: value })}
            onBlur={() => handleFieldBlur('emergencyContact2Name')}
            placeholder={t('stepEmergencyProcedure.emergencyContacts.contact2.name.placeholder')}
            error={getFieldError('emergencyContact2Name')}
            data-testid="emergency-contact2-name-input"
          />

          <FormField
            id="emergencyContact2Phone"
            label={t('stepEmergencyProcedure.emergencyContacts.contact2.phone.label')}
            value={formData.emergencyContact2Phone || ""}
            onChange={(value) => updateFormData({ emergencyContact2Phone: value })}
            onBlur={() => handleFieldBlur('emergencyContact2Phone')}
            placeholder={t('stepEmergencyProcedure.emergencyContacts.contact2.phone.placeholder')}
            error={getFieldError('emergencyContact2Phone')}
            data-testid="emergency-contact2-phone-input"
          />

          <FormField
            id="emergencyContact2Relation"
            label={t('stepEmergencyProcedure.emergencyContacts.contact2.relation.label')}
            value={formData.emergencyContact2Relation || ""}
            onChange={(value) => updateFormData({ emergencyContact2Relation: value })}
            onBlur={() => handleFieldBlur('emergencyContact2Relation')}
            placeholder={t('stepEmergencyProcedure.emergencyContacts.contact2.relation.placeholder')}
            error={getFieldError('emergencyContact2Relation')}
            data-testid="emergency-contact2-relation-input"
          />
        </div>
      </div>

      {/* Land Status */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">{t('stepEmergencyProcedure.landStatus.title')}</h3>
        <p className="text-sm text-gray-600" data-testid="status-intro-text">
          {t('stepEmergencyProcedure.landStatus.intro')}
        </p>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="flex items-center">
              {t('stepEmergencyProcedure.landStatus.label') || t('stepEmergencyProcedure.landStatus.title')}
              <span className="text-red-500 ml-1">{t('common.required')}</span>
            </Label>
            <RadioGroup
              value={landStatus}
              onValueChange={(value) => {
                setLandStatus(value);
                updateFormData({ landStatus: value });
                touchField('landStatus');
                validateField('landStatus');
              }}
              data-testid="land-status-radio"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="owner"
                  id="status-owner"
                  className={`h-5 w-5 border-2 ${getFieldError('landStatus') ? "border-red-500" : "border-input"}`}
                />
                <Label htmlFor="status-owner">{t('stepEmergencyProcedure.landStatus.options.owner')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="heir"
                  id="status-heir"
                  className={`h-5 w-5 border-2 ${getFieldError('landStatus') ? "border-red-500" : "border-input"}`}
                />
                <Label htmlFor="status-heir">{t('stepEmergencyProcedure.landStatus.options.heir')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="buyer"
                  id="status-buyer"
                  className={`h-5 w-5 border-2 ${getFieldError('landStatus') ? "border-red-500" : "border-input"}`}
                />
                <Label htmlFor="status-buyer">{t('stepEmergencyProcedure.landStatus.options.buyer')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="applicant"
                  id="status-applicant"
                  className={`h-5 w-5 border-2 ${getFieldError('landStatus') ? "border-red-500" : "border-input"}`}
                />
                <Label htmlFor="status-applicant">{t('stepEmergencyProcedure.landStatus.options.applicant')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="other"
                  id="status-other"
                  className={`h-5 w-5 border-2 ${getFieldError('landStatus') ? "border-red-500" : "border-input"}`}
                />
                <Label htmlFor="status-other">{t('stepEmergencyProcedure.landStatus.options.other')}</Label>
              </div>
            </RadioGroup>
            {getFieldError('landStatus') && (
              <div className="text-red-500 text-sm flex items-start mt-1" role="alert">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <span>{
                  /*@ts-ignore */
                    t(getFieldError('landStatus'))
                }</span>
              </div>
            )}
          </div>

          {landStatus === "other" && (
            <div className="space-y-2 ml-6">
              <FormField
                id="otherLandStatus"
                label={t('stepEmergencyProcedure.landStatus.otherLabel')}
                value={formData.otherLandStatus || ""}
                onChange={(value) => updateFormData({ otherLandStatus: value })}
                onBlur={() => handleFieldBlur('otherLandStatus')}
                placeholder={t('stepEmergencyProcedure.landStatus.otherPlaceholder')}
                required={true}
                error={getFieldError('otherLandStatus')}
                data-testid="land-status-other-input"
              />
            </div>
          )}
        </div>
      </div>

      {/* Procedure */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">{t('stepEmergencyProcedure.procedureType.title')}</h3>
        <p className="text-sm text-gray-600" data-testid="procedure-intro-text">
          {t('stepEmergencyProcedure.procedureType.intro')}
        </p>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="flex items-center">
              {t('stepEmergencyProcedure.procedureType.label')}
              <span className="text-red-500 ml-1">{t('common.required')}</span>
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2" data-testid="procedure-type-select">
              {procedureOptions.map((type: string) => (
                <div key={type} className="flex items-start space-x-2">
                  <Checkbox
                    id={`procedure-${type.toLowerCase().replace(/\s+/g, '-')}`}
                    checked={procedureTypes.includes(type)}
                    onCheckedChange={() => {
                      handleProcedureTypeChange(type);
                      touchField('procedureTypes');
                      validateField('procedureTypes');
                    }}
                    className={`h-5 w-5 border-2 ${
                      getFieldError('procedureTypes')
                        ? "border-red-500 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                        : "border-input data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                    }`}
                  />
                  <Label
                    htmlFor={`procedure-${type.toLowerCase().replace(/\s+/g, '-')}`}
                    className="text-sm"
                  >
                    {type}
                  </Label>
                </div>
              ))}
            </div>
            {getFieldError('procedureTypes') && (
              <div className="text-red-500 text-sm flex items-start mt-1" role="alert">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <span>{
                  /*@ts-ignore */
                    t(getFieldError('procedureTypes'))
                }</span>
              </div>
            )}
            <p className="text-xs text-gray-500">
              {t('stepEmergencyProcedure.procedureType.help')}
            </p>
          </div>

          {procedureTypes.includes(procedureOptions[14]) && (
            <div className="space-y-2">
              <FormField
                id="otherProcedureType"
                label={t('stepEmergencyProcedure.procedureType.otherLabel')}
                value={formData.otherProcedureType || ""}
                onChange={(value) => updateFormData({ otherProcedureType: value })}
                onBlur={() => handleFieldBlur('otherProcedureType')}
                placeholder={t('stepEmergencyProcedure.procedureType.otherPlaceholder')}
                required={true}
                error={getFieldError('otherProcedureType')}
                multiline={true}
                data-testid="procedure-other-textarea"
              />
              <p className="text-xs text-gray-500">
                {t('stepEmergencyProcedure.procedureType.otherHelp')}
              </p>
            </div>
          )}

          <div className="space-y-2">
            <FormField
              id="additionalInfo"
              label={t('stepEmergencyProcedure.additionalInfo.label')}
              value={formData.additionalInfo || ""}
              onChange={(value) => updateFormData({ additionalInfo: value })}
              onBlur={() => handleFieldBlur('additionalInfo')}
              placeholder={t('stepEmergencyProcedure.additionalInfo.placeholder')}
              multiline={true}
              error={getFieldError('additionalInfo')}
              data-testid="procedure-additional-info-textarea"
            />
            <p className="text-xs text-gray-500">
              {t('stepEmergencyProcedure.additionalInfo.help')}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-4">
        <Button
          variant="outline"
          onClick={onPrevious}
          data-testid="emergency-procedure-prev-button"
          className="border-primary text-primary hover:bg-primary/10"
        >
          {t('common.previous')}
        </Button>
        <Button
          onClick={handleNext}
          data-testid="emergency-procedure-next-button"
          className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
          disabled={hasActiveUploads(formData)}
        >
          {hasActiveUploads(formData) ? t('common.waitForUploads') : t('common.next')}
        </Button>
      </div>
    </div>
  );
}
