# JSDoc Documentation Standards

This document outlines the standards for JSDoc documentation in the Charlie Oscar Consulting project.

## Overview

JSDoc is a markup language used to annotate JavaScript/TypeScript source code files. It provides a way to document the code and its API in a standardized format that can be processed by various tools to generate documentation.

## Benefits

- **Improved code readability**: Well-documented code is easier to understand and maintain
- **Better IDE support**: IDEs like VS Code use JSDoc to provide better autocompletion and type checking
- **Automatic documentation generation**: JSDoc can be used to generate documentation websites
- **Standardized format**: Follows industry standards for code documentation

## Documentation Structure

### Module Documentation

Each module (file) should have a JSDoc comment at the top that describes the module's purpose and functionality:

```typescript
/**
 * Google Drive API utilities
 *
 * This module provides functions for interacting with Google Drive API,
 * including uploading files and creating folders using service account authentication.
 * 
 * @module google-drive
 * @requires googleapis
 * @requires stream
 * 
 * @example
 * // Upload a file to Google Drive
 * const result = await uploadToGoogleDrive(
 *   'example.pdf',
 *   'application/pdf',
 *   fileBuffer
 * );
 */
```

### Interface Documentation

Interfaces should be documented with a description and details about each property:

```typescript
/**
 * Interface for file upload options
 * 
 * This interface defines the options that can be passed to the uploadToGoogleDrive function
 * to customize the upload behavior.
 * 
 * @interface FileUploadOptions
 */
export interface FileUploadOptions {
  /**
   * The ID of the folder to upload the file to
   * 
   * If not provided, the default folder ID from environment variables will be used.
   * This allows uploading files to specific folders in Google Drive.
   * 
   * @type {string}
   * @memberof FileUploadOptions
   */
  folderId?: string;
  
  // Other properties...
}
```

### Function Documentation

Functions should be documented with a description, parameter details, return value, and examples:

```typescript
/**
 * Upload a file to Google Drive
 *
 * This function uploads a file to Google Drive using the service account credentials.
 * It supports various options like specifying a parent folder, making the file public,
 * and creating a new folder for the file.
 *
 * @param {string} fileName - The name of the file to be created in Google Drive
 * @param {string} mimeType - The MIME type of the file (e.g., 'application/pdf', 'image/jpeg')
 * @param {Buffer} fileContent - The file content as a Buffer
 * @param {FileUploadOptions} [options={}] - Additional options for the upload
 * @returns {Promise<FileUploadResult>} A promise that resolves to the result of the upload operation
 * 
 * @throws {Error} The function handles errors internally and returns a FileUploadResult with success=false
 * 
 * @example
 * // Basic file upload
 * const result = await uploadToGoogleDrive(
 *   'document.pdf',
 *   'application/pdf',
 *   fileBuffer
 * );
 */
```

## Required Tags

### For Modules

- `@module` - The name of the module
- `@requires` - External modules required by this module
- `@example` - Example usage of the module

### For Interfaces

- `@interface` - Marks the block as an interface documentation
- `@property` or inline property documentation - Documents each property of the interface
- `@memberof` - Indicates which interface a property belongs to

### For Functions

- `@param` - Documents a function parameter
- `@returns` - Documents the return value
- `@throws` - Documents exceptions that may be thrown
- `@example` - Provides example usage

### For Properties

- `@type` - The type of the property
- `@memberof` - The interface or class the property belongs to
- `@default` - The default value (if applicable)

## Type Annotations

Use TypeScript-style type annotations in JSDoc:

```typescript
/**
 * @param {string} fileName - The name of the file
 * @param {Buffer} fileContent - The file content
 * @returns {Promise<FileUploadResult>} The upload result
 */
```

## Examples

Always include examples to show how to use the function or interface:

```typescript
/**
 * @example
 * // Basic usage
 * const result = await uploadToGoogleDrive(
 *   'document.pdf',
 *   'application/pdf',
 *   fileBuffer
 * );
 * 
 * @example
 * // Advanced usage with options
 * const result = await uploadToGoogleDrive(
 *   'image.jpg',
 *   'image/jpeg',
 *   imageBuffer,
 *   {
 *     folderId: 'specific-folder-id',
 *     makePublic: true
 *   }
 * );
 */
```

## Best Practices

1. **Be concise but complete**: Provide enough information to understand the code without being overly verbose
2. **Use consistent formatting**: Follow the same format for all documentation
3. **Update documentation when code changes**: Keep documentation in sync with code
4. **Document edge cases and errors**: Explain what happens in error conditions
5. **Use proper English**: Use correct grammar, spelling, and punctuation
6. **Avoid redundancy**: Don't repeat information that's already clear from the code
7. **Document "why" not just "what"**: Explain the reasoning behind complex code

## Tools

- **VS Code**: Provides JSDoc autocompletion and validation
- **ESLint**: Can enforce JSDoc standards with the `eslint-plugin-jsdoc` plugin
- **TypeDoc**: Can generate documentation from JSDoc comments
- **JSDoc**: The original documentation generator for JavaScript

## References

- [JSDoc Official Documentation](https://jsdoc.app/)
- [TypeScript JSDoc Reference](https://www.typescriptlang.org/docs/handbook/jsdoc-supported-types.html)
- [Google JavaScript Style Guide - JSDoc](https://google.github.io/styleguide/jsguide.html#jsdoc)
