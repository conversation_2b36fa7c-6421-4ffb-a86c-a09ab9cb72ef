"use client";

import React, { useState, useCallback, useRef } from "react";
import { Upload } from "lucide-react";
import { useLanguage } from "@/app/translations/language-context";

interface DragDropZoneProps {
  onFileSelect: (files: File[], errors?: string[]) => void;
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in bytes
  className?: string;
  children?: React.ReactNode;
}

export function DragDropZone({
  onFileSelect,
  accept = "*/*",
  multiple = false,
  maxSize = 2 * 1024 * 1024, // 2MB default
  className = "",
  children,
}: DragDropZoneProps) {
  const { t } = useLanguage();
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const validateFiles = (files: File[]): { valid: File[]; errors: string[] } => {
    const validFiles: File[] = [];
    const errors: string[] = [];

    for (const file of files) {
      // Check file size
      if (file.size > maxSize) {
        errors.push(`${file.name}: ${t('common.fileTooLarge', { maxSize: (maxSize / (1024 * 1024)).toFixed(1) })}`);
        continue;
      }

      // Check file type if accept is specified
      if (accept !== "*/*") {
        const acceptTypes = accept.split(",").map(type => type.trim());
        const fileType = file.type;
        const fileExtension = `.${file.name.split(".").pop()?.toLowerCase()}`;

        const isValidType = acceptTypes.some(type => {
          if (type.startsWith(".")) {
            // Extension check
            return fileExtension === type.toLowerCase();
          } else if (type.endsWith("/*")) {
            // MIME type group check (e.g., "image/*")
            const typeGroup = type.split("/")[0];
            return fileType.startsWith(`${typeGroup}/`);
          } else {
            // Exact MIME type check
            return fileType === type;
          }
        });

        if (!isValidType) {
          errors.push(`${file.name}: ${t('common.invalidFileType')}`);
          continue;
        }
      }

      validFiles.push(file);
    }

    return { valid: validFiles, errors };
  };

  const processFiles = (fileList: FileList) => {
    const files = Array.from(fileList);
    const { valid, errors } = validateFiles(files);

    // Always pass both valid files and errors to the callback
    // This allows the parent component to handle errors appropriately
    onFileSelect(valid, errors.length > 0 ? errors : undefined);
  };

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        processFiles(e.dataTransfer.files);
      }
    },
    [onFileSelect]
  );

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      processFiles(e.target.files);
      // Reset the input value so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div
      className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
        isDragging
          ? "border-primary bg-primary/5"
          : "border-gray-300 hover:border-primary/50"
      } ${className}`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      onClick={handleBrowseClick}
    >
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        accept={accept}
        multiple={multiple}
        onChange={handleFileInputChange}
      />

      {children || (
        <div className="flex flex-col items-center justify-center text-center">
          <Upload className="h-10 w-10 text-gray-400 mb-2" />
          <p className="text-sm font-medium mb-1">
            {t('common.dropFilesHere')}
          </p>
          <p className="text-xs text-gray-500">
            {t('common.maxFileSize', { maxSize: (maxSize / (1024 * 1024)).toFixed(1) })}
          </p>
        </div>
      )}
    </div>
  );
}
