"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Phone, Mail, MapPin, Send, CheckCircle } from "lucide-react";

export function ContactForm({ content }: { content: any }) {
  const [isVisible, setIsVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  const [formData, setFormData] = useState(() => {
    const initial: Record<string, string> = {};
    (content?.fields || []).forEach((field: any) => {
      initial[field.name] = "";
    });
    return initial;
  });

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    console.log(formData);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsSubmitting(false);
    setIsSubmitted(true);
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData(() => {
        const initial: Record<string, string> = {};
        (content?.fields || []).forEach((field: any) => {
          initial[field.name] = "";
        });
        return initial;
      });
    }, 3000);
  };

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <Card className={`transition-all duration-1000 shadow-xl border-0 bg-white/90 backdrop-blur-md ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}> 
            <CardHeader className="pb-0">
              <div className="flex flex-col items-center text-center gap-2">
                <CardTitle className="text-2xl md:text-3xl font-bold text-primary">
                  {content?.sectionTitle}
                </CardTitle>
                <CardDescription className="text-gray-600 text-base md:text-lg max-w-xl">
                  {content?.sectionSubtitle}
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                {(content?.fields || []).map((field: any, idx: number) => {
                  if (field.type === "select") {
                    return (
                      <div key={field.name} className="md:col-span-2">
                        <label className="block text-sm font-semibold mb-1 text-primary">{field.label}</label>
                        <Select value={formData[field.name]} onValueChange={val => handleInputChange(field.name, val)}>
                          <SelectTrigger>
                            <SelectValue placeholder={field.placeholder} />
                          </SelectTrigger>
                          <SelectContent>
                            {field.options.map((option: string, idx: number) => (
                              <SelectItem key={idx} value={option}>{option}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    );
                  }
                  if (field.type === "textarea") {
                    return (
                      <div key={field.name} className="md:col-span-2">
                        <label className="block text-sm font-semibold mb-1 text-primary">{field.label}</label>
                        <Textarea
                          placeholder={field.placeholder}
                          value={formData[field.name]}
                          onChange={e => handleInputChange(field.name, e.target.value)}
                          required
                          className="resize-none min-h-[120px] border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/20"
                        />
                      </div>
                    );
                  }
                  return (
                    <div key={field.name}>
                      <label className="block text-sm font-semibold mb-1 text-primary">{field.label}</label>
                      <Input
                        type={field.type}
                        placeholder={field.placeholder}
                        value={formData[field.name]}
                        onChange={e => handleInputChange(field.name, e.target.value)}
                        required
                        className="border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/20"
                      />
                    </div>
                  );
                })}
                <div className="md:col-span-2 mt-2">
                  <Button type="submit" size="lg" className="w-full bg-primary text-white hover:bg-primary/90 transition-all duration-200" disabled={isSubmitting}>
                    {isSubmitting ? "Envoi en cours..." : content?.submitLabel}
                  </Button>
                  {isSubmitted && (
                    <div className="flex items-center justify-center gap-2 text-green-600 mt-4">
                      <CheckCircle className="w-5 h-5" />
                      <span>{content?.successMessage}</span>
                    </div>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
