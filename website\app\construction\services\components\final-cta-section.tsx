import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface FinalCtaSectionProps {
  ctaData: {
    title: string;
    description: string;
    buttons: Array<{
      text: string;
      href: string;
      variant: string;
      icon?: string;
    }>;
  };
}

export default function FinalCtaSection({ ctaData }: FinalCtaSectionProps) {
  return (
    <section className="py-20 bg-gradient-to-r from-orange-600 to-orange-700 text-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center space-y-8">
          <h2 className="text-3xl md:text-4xl font-bold">
            {ctaData.title}
          </h2>

          <p className="text-xl text-orange-100">
            {ctaData.description}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {ctaData.buttons.map((button, index) => (
              <Button 
                key={index}
                asChild 
                size="lg" 
                variant={button.variant === 'secondary' ? 'secondary' : 'outline'}
                className={button.variant === 'outline' 
                  ? "border-white text-primary hover:bg-white hover:text-orange-600"
                  : button.variant === 'secondary' 
                    ? "group" 
                    : ""
                }
              >
                <Link href={button.href} className="flex items-center">
                  {button.text}
                  {button.icon && <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />}
                </Link>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
