/**
 * OpenAPI schema for the Charlie Oscar Consulting API
 *
 * This file defines the OpenAPI schema for the API documentation.
 * It follows the OpenAPI 3.0.0 specification.
 */

const openApiSchema = {
  openapi: '3.0.0',
  info: {
    title: 'Charlie Oscar Consulting API',
    description: 'API documentation for Charlie Oscar Consulting services',
    version: '1.0.0',
    contact: {
      name: '<PERSON> Consulting',
      url: 'https://charlie-oscar-consulting.com',
    },
  },
  servers: [
    {
      url: 'http://localhost:3000/api',
      description: 'Local development server',
    },
    {
      url: 'https://charlie-oscar-consulting.com/api',
      description: 'Production server',
    },
  ],
  tags: [
    {
      name: 'formulaire',
      description: 'Form submission and file upload operations',
    },
    {
      name: 'email',
      description: 'Email sending operations',
    },
    {
      name: 'test',
      description: 'Test endpoints (only available in non-production environments)',
    },
  ],
  paths: {
    '/formulaire/upload': {
      post: {
        tags: ['formulaire'],
        summary: 'Upload a file to Google Drive',
        description: 'Uploads a file to Google Drive and returns the file information',
        operationId: 'uploadFile',
        requestBody: {
          content: {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary',
                    description: 'The file to upload',
                  },
                  folderId: {
                    type: 'string',
                    description: 'Optional Google Drive folder ID to upload the file to',
                  },
                  folderName: {
                    type: 'string',
                    description: 'Optional folder name to create and upload the file to',
                  },
                },
                required: ['file'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'File uploaded successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/FileUploadResult',
                },
              },
            },
          },
          '400': {
            description: 'Bad request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
    '/formulaire/submit': {
      post: {
        tags: ['formulaire'],
        summary: 'Submit form data and files',
        description: 'Submits form data and files to the server and Google Drive',
        operationId: 'submitForm',
        requestBody: {
          content: {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  formData: {
                    type: 'string',
                    description: 'JSON string containing the form data',
                  },
                  pdfFile: {
                    type: 'string',
                    format: 'binary',
                    description: 'PDF file generated from the form data',
                  },
                  folderName: {
                    type: 'string',
                    description: 'Optional folder name to create and upload the files to',
                  },
                  userFolderId: {
                    type: 'string',
                    description: 'Optional Google Drive folder ID to upload the files to',
                  },
                },
                required: ['formData'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Form submitted successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/FormSubmitResult',
                },
              },
            },
          },
          '400': {
            description: 'Bad request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
    '/test/google-drive/create-folder': {
      post: {
        tags: ['test'],
        summary: 'Create a folder in Google Drive',
        description: 'Creates a folder in Google Drive and returns the folder information. Only available in non-production environments.',
        operationId: 'createFolder',
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  folderName: {
                    type: 'string',
                    description: 'Name of the folder to create',
                  },
                  parentFolderId: {
                    type: 'string',
                    description: 'Optional parent folder ID',
                  },
                },
                required: ['folderName'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Folder created successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/FolderInfo',
                },
              },
            },
          },
          '400': {
            description: 'Bad request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
          '404': {
            description: 'Not found (in production environments)',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
    '/environment': {
      get: {
        tags: ['test'],
        summary: 'Get environment information',
        description: 'Returns information about the current environment',
        operationId: 'getEnvironment',
        responses: {
          '200': {
            description: 'Environment information',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    environment: {
                      type: 'string',
                      description: 'Current environment (development, test, staging, production)',
                    },
                    testRoutesEnabled: {
                      type: 'boolean',
                      description: 'Whether test routes are enabled in this environment',
                    },
                    debugFeaturesEnabled: {
                      type: 'boolean',
                      description: 'Whether debug features are enabled in this environment',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/send-mail': {
      post: {
        tags: ['email'],
        summary: 'Send email to one or more recipients',
        description: 'Sends an email with HTML or plain text content to one or more recipients',
        operationId: 'sendMail',
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  emails: {
                    type: 'string',
                    description: 'Email recipient(s) - can be a single email or comma-separated list',
                    example: '<EMAIL>'
                  },
                  subject: {
                    type: 'string',
                    description: 'Email subject line',
                    example: 'Important Information'
                  },
                  content: {
                    type: 'string',
                    description: 'Email content (HTML or plain text)',
                    example: '<p>This is an email</p>'
                  },
                  isHtml: {
                    type: 'boolean',
                    description: 'Whether content is HTML (default: true)',
                    default: true
                  }
                },
                required: ['emails', 'content']
              },
              examples: {
                'Single recipient': {
                  value: {
                    emails: '<EMAIL>',
                    subject: 'Test Email',
                    content: '<h1>Hello!</h1><p>This is a test email.</p>'
                  }
                },
                'Multiple recipients': {
                  value: {
                    emails: ['<EMAIL>', '<EMAIL>'],
                    subject: 'Group Announcement',
                    content: 'This is a plain text email to multiple recipients.'
                  }
                }
              }
            }
          },
          required: true
        },
        responses: {
          '200': {
            description: 'Email sent successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/EmailResponse'
                },
                examples: {
                  'Success': {
                    value: {
                      success: true,
                      message: 'Email sent successfully',
                      results: [
                        {
                          success: true,
                          message: 'Email sent successfully',
                          data: {
                            messageId: '<<EMAIL>>',
                            recipients: '<EMAIL>'
                          }
                        }
                      ]
                    }
                  }
                }
              }
            }
          },
          '400': {
            description: 'Bad request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse'
                },
                examples: {
                  'Invalid Email': {
                    value: {
                      success: false,
                      message: 'Invalid email address(es) provided',
                      error: 'Invalid emails: not-an-email'
                    }
                  }
                }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse'
                },
                examples: {
                  'SMTP Error': {
                    value: {
                      success: false,
                      message: 'Failed to send email',
                      error: 'SMTP connection failed'
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
  },
  components: {
    schemas: {
      FileUploadResult: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            description: 'Whether the upload was successful',
          },
          fileId: {
            type: 'string',
            description: 'The ID of the uploaded file in Google Drive',
          },
          fileName: {
            type: 'string',
            description: 'The name of the uploaded file',
          },
          webViewLink: {
            type: 'string',
            description: 'The URL to view the file in Google Drive',
          },
          webContentLink: {
            type: 'string',
            description: 'The URL to download the file content',
          },
          folder: {
            $ref: '#/components/schemas/FolderInfo',
          },
          error: {
            type: 'string',
            description: 'Error message if the upload failed',
          },
        },
        required: ['success'],
      },
      FormSubmitResult: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            description: 'Whether the submission was successful',
          },
          referenceNumber: {
            type: 'string',
            description: 'Reference number for the submission',
          },
          message: {
            type: 'string',
            description: 'Success message',
          },
          fileId: {
            type: 'string',
            description: 'The ID of the uploaded file in Google Drive',
          },
          fileName: {
            type: 'string',
            description: 'The name of the uploaded file',
          },
          webViewLink: {
            type: 'string',
            description: 'The URL to view the file in Google Drive',
          },
          webContentLink: {
            type: 'string',
            description: 'The URL to download the file content',
          },
          folder: {
            $ref: '#/components/schemas/FolderInfo',
          },
          error: {
            type: 'string',
            description: 'Error message if the submission failed',
          },
        },
        required: ['success'],
      },
      FolderInfo: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'The ID of the folder in Google Drive',
          },
          name: {
            type: 'string',
            description: 'The name of the folder',
          },
          webViewLink: {
            type: 'string',
            description: 'The URL to view the folder in Google Drive',
          },
        },
        required: ['id', 'name'],
      },
      ErrorResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            description: 'Always false for error responses',
            example: false,
          },
          error: {
            type: 'string',
            description: 'Error message',
            example: 'An error occurred while processing your request',
          },
        },
        required: ['success', 'error'],
      },
      EmailResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            description: 'Whether the email sending operation was successful',
            example: true,
          },
          message: {
            type: 'string',
            description: 'Status message',
            example: 'Email sent successfully',
          },
          results: {
            type: 'array',
            description: 'Results for each email recipient (for bulk emails)',
            items: {
              type: 'object',
              properties: {
                success: {
                  type: 'boolean',
                  description: 'Whether this specific email was sent successfully',
                },
                message: {
                  type: 'string',
                  description: 'Status message for this specific email',
                },
                data: {
                  type: 'object',
                  description: 'Additional data about the email (if successful)',
                  properties: {
                    messageId: {
                      type: 'string',
                      description: 'Message ID assigned by the mail server',
                    },
                    recipients: {
                      type: 'string',
                      description: 'Recipients of the email',
                    },
                  },
                },
                error: {
                  type: 'string',
                  description: 'Error message (if unsuccessful)',
                },
              },
            },
          },
          error: {
            type: 'string',
            description: 'Error message (if unsuccessful)',
          },
        },
        required: ['success', 'message'],
      },
    },
  },
};

export default openApiSchema;
