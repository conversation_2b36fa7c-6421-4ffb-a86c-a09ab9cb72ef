import { NextRequest, NextResponse } from 'next/server';
import { sendEmail, sendBulkEmails, EmailResponse } from './utils/mail-sender';

/**
 * Interface for the request body
 */
interface SendMailRequestBody {
  emails: string | string[]; // Single email or array of emails
  subject?: string;
  content: string;
  isHtml?: boolean;
}

/**
 * Interface for the response body
 */
interface SendMailResponseBody {
  success: boolean;
  message: string;
  results?: EmailResponse[];
  error?: string;
}

/**
 * Validates an email address
 * @param email - Email address to validate
 * @returns Boolean indicating if the email is valid
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * POST handler for the send-mail API endpoint
 * @param request - The incoming request
 * @returns Response with the result of the email sending operation
 */
export async function POST(request: NextRequest): Promise<NextResponse<SendMailResponseBody>> {
  try {
    // Parse request body
    const body: SendMailRequestBody = await request.json();

    // Validate request body
    if (!body) {
      return NextResponse.json(
        { success: false, message: 'Request body is required' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!body.emails) {
      return NextResponse.json(
        { success: false, message: 'Recipients (emails) are required' },
        { status: 400 }
      );
    }

    if (!body.content) {
      return NextResponse.json(
        { success: false, message: 'Email content is required' },
        { status: 400 }
      );
    }

    // Convert single email to array if needed
    const emailList = Array.isArray(body.emails) ? body.emails : [body.emails];

    // Validate email addresses
    const invalidEmails = emailList.filter(email => !isValidEmail(email));
    if (invalidEmails.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid email address(es) provided',
          error: `Invalid emails: ${invalidEmails.join(', ')}` 
        },
        { status: 400 }
      );
    }

    // Set default subject if not provided
    const subject = body.subject || 'No Subject';
    
    // Determine if content is HTML (default to true)
    const isHtml = body.isHtml !== undefined ? body.isHtml : true;

    // Send emails
    let results: EmailResponse[];
    
    if (emailList.length === 1) {
      // Send to a single recipient
      const result = await sendEmail({
        to: emailList[0],
        subject,
        ...(isHtml ? { html: body.content } : { text: body.content }),
      });
      
      results = [result];
      
      // Return appropriate response based on the result
      if (!result.success) {
        return NextResponse.json(
          { 
            success: false, 
            message: 'Failed to send email',
            results,
            error: result.error 
          },
          { status: 500 }
        );
      }
    } else {
      // Send to multiple recipients
      results = await sendBulkEmails(emailList, subject, body.content, isHtml);
      
      // Check if all emails failed
      if (results.every(result => !result.success)) {
        return NextResponse.json(
          { 
            success: false, 
            message: 'Failed to send all emails',
            results 
          },
          { status: 500 }
        );
      }
      
      // Check if some emails failed
      if (results.some(result => !result.success)) {
        return NextResponse.json(
          { 
            success: true, 
            message: 'Some emails were sent successfully, but others failed',
            results 
          },
          { status: 200 }
        );
      }
    }

    // All emails sent successfully
    return NextResponse.json(
      { 
        success: true, 
        message: `Email${emailList.length > 1 ? 's' : ''} sent successfully`,
        results 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in send-mail API:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'An error occurred while processing your request',
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
