import { NextResponse } from 'next/server';
import { 
  getEnvironment, 
  areTestRoutesEnabled, 
  areDebugFeaturesEnabled 
} from '@/app/utils/environment';

/**
 * GET handler for the environment API endpoint
 * 
 * This endpoint returns information about the current environment,
 * including whether test routes and debug features are enabled.
 * 
 * @returns A JSON response with environment information
 */
export async function GET() {
  return NextResponse.json({
    environment: getEnvironment(),
    testRoutesEnabled: areTestRoutesEnabled(),
    debugFeaturesEnabled: areDebugFeaturesEnabled(),
  });
}
