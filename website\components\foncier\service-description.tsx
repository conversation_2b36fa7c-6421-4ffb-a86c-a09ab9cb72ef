"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { FoncierService } from "@/lib/foncier-services-data";
import { FileText, CheckCircle, AlertCircle, Info, ListChecks } from "lucide-react";

interface ServiceDescriptionProps {
  service: FoncierService;
}

// Example prestations incluses (replace with dynamic data if available)
const defaultPrestations = [
  { "title": "Analyse personnalisée de votre dossier", "description": "Nous étudions en détail votre situation pour adapter la démarche à vos besoins." },
  { "title": "Accompagnement administratif complet", "description": "Nous gérons toutes les formalités et relations avec les administrations." },
  { "title": "Rédaction et vérification des documents", "description": "Nous rédigeons et contrôlons tous les documents nécessaires à votre dossier." },
  { "title": "Suivi des démarches jusqu'à l'obtention du résultat", "description": "Nous assurons un suivi régulier et vous informons à chaque étape." },
  { "title": "Conseils juridiques et techniques adaptés", "description": "Vous bénéficiez de recommandations personnalisées pour sécuriser votre projet." }
];

export function ServiceDescription({ service }: ServiceDescriptionProps) {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-7 gap-12">
            {/* Main Content: spans 5/7 columns on large screens */}
            <div className={`lg:col-span-5 space-y-10 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              {/* Section Header */}
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
                  <FileText className="w-4 h-4 mr-2" />
                  Description détaillée
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  En quoi consiste ce service ?
                </h2>
              </div>
              {/* Detailed Description */}
              <div className="prose prose-lg max-w-none mb-8">
                <div className="text-gray-700 leading-relaxed space-y-6">
                  {service.detailedDescription.split('\n').map((paragraph, index) => (
                    <p key={index} className="text-lg leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>

              {/* Avantages clés de ce service */}
              <Card className="border-l-4 border-l-primary bg-primary/5 shadow-sm mb-8">
                <CardHeader>
                  <CardTitle className="flex items-center text-xl text-gray-900">
                    <CheckCircle className="w-6 h-6 text-primary mr-3" />
                    Avantages clés de ce service
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-6">
                    <div className="space-y-4">
                      {
                        service.advantages?.map((advantage, index) => (
                          <div key={index} className="flex items-start space-x-3">
                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                            <div>
                              <h4 className="font-semibold text-gray-900">{advantage.title}</h4>
                              <p className="text-sm text-gray-600">{advantage.description}</p>
                            </div>
                          </div>
                        )) 
                      }
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Prestations incluses */}
              <div className="mb-8">
                <div className="flex items-center mb-6">
                  <ListChecks className="w-6 h-6 text-accent mr-3" />
                  <span className="text-xl font-bold text-gray-900">Prestations incluses</span>
                </div>
                <div className="grid md:grid-cols-2 gap-8">
                  {service.prestations?.map((item, idx) => {
                    const isObject = typeof item === 'object' && item !== null;
                    return (
                      <div key={idx} className="relative flex bg-white rounded-xl shadow-sm border border-gray-100 p-8 min-h-[120px] items-center">
                        {/* Number on the left, rotated */}
                        <div className="absolute left-0 top-6 flex flex-col items-center" style={{width: '40px'}}>
                          <span className="text-2xl font-bold text-accent opacity-80 transform -rotate-90 select-none">{String(idx + 1).padStart(2, '0')}</span>
                          <span className="block w-px h-12 bg-accent mt-2" />
                        </div>
                        {/* Content to the right of the number */}
                        <div className="pl-16">
                          <div className="font-semibold text-gray-900 text-lg mb-1">
                            {isObject ? item.title : item}
                          </div>
                          {isObject && item.description && (
                            <div className="text-gray-600 text-base leading-relaxed mt-1">{item.description}</div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Quand faire appel à ce service ? */}
              <Card className="border-l-4 border-l-accent bg-accent/5 shadow-sm mb-8">
                <CardHeader>
                  <CardTitle className="flex items-center text-xl text-gray-900">
                    <Info className="w-6 h-6 text-accent mr-3" />
                    Quand faire appel à ce service ?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {service.whenToCall?.map((useCase, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-accent/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-accent font-semibold text-sm">{index + 1}</span>
                        </div>
                        <p className="text-gray-700">{useCase}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
            {/* Sidebar: spans 2/7 columns on large screens */}
            <div className={`lg:col-span-2 space-y-6 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              {/* Service Summary */}
              <Card className="sticky top-8 bg-gray-50 border border-gray-200">
                <CardHeader>
                  <CardTitle className="text-lg text-gray-900">
                    Résumé du service
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center py-2 border-b border-gray-200">
                      <span className="text-gray-600 text-sm">Service</span>
                      <span className="text-gray-900 font-medium text-sm">{service.title}</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-200">
                      <span className="text-gray-600 text-sm">Catégorie</span>
                      <span className="text-gray-900 font-medium text-sm">{service.category}</span>
                    </div>
                    {/* <div className="flex justify-between items-center py-2 border-b border-gray-200">
                      <span className="text-gray-600 text-sm">Durée</span>
                      <span className="text-gray-900 font-medium text-sm">{service.estimatedDuration}</span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-600 text-sm">Complexité</span>
                      <span className={`text-sm font-medium ${
                        service.complexity === 'Simple' ? 'text-green-600' :
                        service.complexity === 'Modéré' ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {service.complexity}
                      </span>
                    </div> */}
                  </div>
                </CardContent>
              </Card>
              {/* Important Notice */}
              <Card className="border-l-4 border-l-orange-500 bg-orange-50 border border-orange-100">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg text-gray-900">
                    <AlertCircle className="w-5 h-5 text-orange-500 mr-2" />
                    Important
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 leading-relaxed">
                    Chaque dossier est unique. Les délais et procédures peuvent varier 
                    selon la complexité de votre situation. Nous vous recommandons une 
                    consultation préalable pour une évaluation personnalisée.
                  </p>
                </CardContent>
              </Card>
              {/* Related Services */}
              <Card className="bg-gray-50 border border-gray-200">
                <CardHeader>
                  <CardTitle className="text-lg text-gray-900">
                    Services complémentaires
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {getRelatedServices(service.slug).map((relatedService, index) => (
                      <div key={index} className="text-sm">
                        <a 
                          href={`/foncier/services/${relatedService.slug}`}
                          className="text-primary hover:text-primary/80 font-medium transition-colors"
                        >
                          {relatedService.title}
                        </a>
                        <p className="text-gray-600 text-xs mt-1">{relatedService.shortDescription}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Helper functions
function getServiceUseCases(slug: string): string[] {
  const useCases: Record<string, string[]> = {
    'derogation-special': [
      "Votre projet ne respecte pas certaines normes d'urbanisme standard",
      "Vous avez besoin d'une autorisation exceptionnelle pour votre construction",
      "Votre terrain présente des contraintes particulières",
      "Vous développez un projet d'intérêt public ou social"
    ],
    'dossier-technique': [
      "Vous préparez un projet de construction ou d'aménagement",
      "Vous devez valider la faisabilité technique de votre projet",
      "Vous avez besoin d'études spécialisées pour votre dossier",
      "Vous voulez optimiser les coûts et délais de votre projet"
    ],
    'mutation-par-deces': [
      "Un proche propriétaire foncier est décédé",
      "Vous devez régulariser une succession foncière",
      "Il y a plusieurs héritiers à considérer",
      "Vous voulez éviter les conflits familiaux"
    ],
    'morcellement-mutation-achat': [
      "Vous voulez diviser votre terrain en plusieurs parcelles",
      "Vous achetez ou vendez une partie de terrain",
      "Vous développez un projet de lotissement",
      "Vous optimisez la valorisation de votre patrimoine foncier"
    ],
    'concession-domanial': [
      "Vous développez un projet sur le domaine public",
      "Vous avez besoin d'un terrain pour un projet industriel ou commercial",
      "Vous voulez obtenir un droit d'usage à long terme",
      "Votre projet présente un intérêt économique ou social"
    ]
  };
  
  return useCases[slug] || [
    "Vous avez un projet foncier spécifique",
    "Vous cherchez une expertise professionnelle",
    "Vous voulez sécuriser vos démarches",
    "Vous souhaitez optimiser les délais"
  ];
}

function getRelatedServices(currentSlug: string): Array<{slug: string, title: string, shortDescription: string}> {
  const allServices = [
  { "slug": "immatriculation-directe", "title": "Immatriculation Directe", "shortDescription": "Sécurisation juridique d’un terrain occupé" },
  { "slug": "achat-terrain-non-titre", "title": "Achat de Terrain Non Titré", "shortDescription": "Acquérez un terrain non immatriculé en toute sécurité." },
  { "slug": "concession-domanial", "title": "Concession Domaniale", "shortDescription": "Nous vous guidons pour obtenir une concession temporaire ou définitive." },
  { "slug": "mutation-par-deces", "title": "Mutation par Décès", "shortDescription": "Transférez à votre nom votre terrain hérité grâce à un accompagnement complet du dossier." }
];
  
  return allServices.filter(service => service.slug !== currentSlug).slice(0, 3);
}
