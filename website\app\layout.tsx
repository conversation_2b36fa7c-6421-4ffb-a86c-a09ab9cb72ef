import type { Metada<PERSON> } from "next";
import {Analytics} from "@vercel/analytics/next";
import { Rethink_Sans } from "next/font/google";
import "./globals.css";
import { LanguageProvider } from "./translations/language-context";
import { OnlineStatusProvider } from "./contexts/online-status-context";
import { ToastContainer } from "./components/toast-container";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";


const rethinkSans = Rethink_Sans({
  variable: "--font-rethink-sans",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "<PERSON> Consulting | Foncier, Immobilier & Construction",
  description: "Charlie <PERSON> Consulting - Votre partenaire de confiance en foncier, immobilier et construction. Expertise complète en droit foncier, services immobiliers et accompagnement construction.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" suppressHydrationWarning={true}>
      <body
        className={`${rethinkSans.variable} antialiased`}
      >
        <LanguageProvider>
          <OnlineStatusProvider>
            <div className="flex flex-col min-h-screen">
              <Header />
              <main className="flex-grow pt-20">
                {children}
              </main>
              <Footer />
              <ToastContainer />
            </div>
          </OnlineStatusProvider>
        </LanguageProvider>
        <Analytics />
      </body>
    </html>
  );
}
