{"FoncierService": {"id": "string", "slug": "string", "title": "string", "shortDescription": "string", "detailedDescription": "string", "category": "string", "estimatedDuration": "string", "complexity": "Simple | Modéré | Complexe", "featured": "boolean", "documents": ["string"], "process": [{"id": "number", "title": "string", "description": "string", "duration": "string (optional)", "documents": ["string (optional)"]}], "testimonials": [{"id": "number", "name": "string", "role": "string", "location": "string", "content": "string", "rating": "number"}], "faq": [{"id": "number", "question": "string", "answer": "string"}]}, "BlogPost": {"slug": "string", "title": "string", "intro": "string", "mainContent": "string", "category": "string", "createdAt": "ISO8601 date string", "updatedAt": "ISO8601 date string", "minuteLecture": "number", "coverImage": "string (URL or file reference)", "relatedPosts": ["slug (string)"]}, "File": {"binary": "base64 string or file reference", "name": "string", "description": "string"}, "HomePage": {"hero": {"title": "string", "subtitle": "string", "cta": {"label": "string", "url": "string"}, "badge": "string", "primaryAction": {"label": "string", "url": "string"}, "secondaryAction": {"label": "string", "url": "string"}, "trustIndicators": [{"value": "string", "label": "string"}]}, "about": {"title": "string", "content": "string", "paragraphs": ["string"], "keyPoints": [{"icon": "string", "title": "string", "description": "string"}]}, "services": [{"name": "string", "description": "string", "url": "string", "services": ["string"]}], "domainsSection": {"badge": "string", "title": "string", "subtitle": "string"}, "whyChooseUs": {"sectionTitle": "string", "sectionSubtitle": "string", "description": "string", "advantages": [{"icon": "string", "title": "string", "description": "string", "color": "string", "bgColor": "string"}]}, "ctaSection": {"sectionTitle": "string", "sectionSubtitle": "string", "benefitsTitle": "string", "benefits": ["string"], "primaryAction": {"label": "string", "url": "string"}, "secondaryAction": {"label": "string", "url": "string"}, "visual": {"headline": "string", "subline": "string", "online": "string", "clients": "string"}, "contactMethods": [{"icon": "string", "title": "string", "description": "string", "value": "string", "action": "string", "color": "string", "bgColor": "string"}]}, "contactForm": {"sectionTitle": "string", "sectionSubtitle": "string", "fields": [{"name": "string", "label": "string", "type": "string", "placeholder": "string", "options": ["string"]}], "submitLabel": "string", "successMessage": "string"}, "foncierServices": {"badge": "string", "title": "string", "subtitle": "string", "services": [{"icon": "string", "title": "string", "description": "string", "features": ["string"]}]}}, "ContactPage": {"heroSection": {"title": "string", "subtitle": "string"}, "form": {"fields": [{"name": "string", "label": "string", "type": "string", "placeholder": "string", "options": ["string"]}], "submitLabel": "string", "successMessage": "string"}, "contactInfo": "ContactInfo"}, "AboutPage": {"heroSection": {"title": "string", "subtitle": "string", "domainIcons": [{"icon": "string", "label": "string"}]}, "companyDescription": {"title": "string", "content": "string"}, "mission": {"title": "string", "content": "string"}, "vision": {"title": "string", "content": "string"}, "values": [{"title": "string", "description": "string", "icon": "string", "color": "string (optional)"}], "sloganSection": {"slogan": "string", "description": "string"}}, "FoncierHomePage": {"heroSection": {"title": "string", "subtitle": "string", "leadMagnet": "string (optional)", "backgroundImage": "string (optional)"}, "whySecureSection": {"title": "string", "description": "string", "benefits": [{"icon": "string", "title": "string", "description": "string", "details": ["string"]}]}, "servicesSection": {"title": "string", "subtitle": "string", "services": [{"icon": "string", "title": "string", "description": "string", "features": ["string"]}]}, "processSection": {"title": "string", "subtitle": "string", "steps": [{"title": "string", "description": "string", "icon": "string (optional)", "duration": "string (optional)", "details": ["string"]}]}, "faqSection": {"title": "string", "description": "string", "faqs": [{"question": "string", "answer": "string", "details": ["string"]}]}, "finalCtaSection": {"title": "string", "subtitle": "string", "ctaLabel": "string", "ctaUrl": "string"}}, "ContactInfo": {"phone": "string", "email": "string", "whatsapp": "string", "locations": ["string"], "socials": [{"platform": "string", "url": "string", "icon": "string (optional)"}]}}