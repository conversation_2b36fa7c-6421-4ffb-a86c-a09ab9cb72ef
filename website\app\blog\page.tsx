import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import UnderConstruction from "@/components/UnderConstruction";
export default function BlogPage() {
  // Articles de blog fictifs pour la démonstration
  const articles = [
    {
      id: 1,
      title: "Guide complet pour obtenir un titre foncier au Cameroun",
      excerpt: "Découvrez les étapes essentielles pour sécuriser votre propriété foncière au Cameroun. Un guide pratique pour naviguer dans les procédures administratives.",
      date: "15 Décembre 2024",
      category: "Foncier",
      readTime: "5 min de lecture",
      image: "/placeholder-blog-1.jpg"
    },
    {
      id: 2,
      title: "Investir dans l'immobilier à Yaoundé : Opportunités et conseils",
      excerpt: "Le marché immobilier de Yaoundé offre de nombreuses opportunités. Voici nos conseils pour investir intelligemment dans la capitale camerounaise.",
      date: "10 Décembre 2024",
      category: "Immobilier",
      readTime: "7 min de lecture",
      image: "/placeholder-blog-2.jpg"
    },
    {
      id: 3,
      title: "Les nouvelles réglementations en matière de construction",
      excerpt: "Mise à jour sur les dernières réglementations et normes de construction au Cameroun. Ce que vous devez savoir pour vos projets.",
      date: "5 Décembre 2024",
      category: "Construction",
      readTime: "4 min de lecture",
      image: "/placeholder-blog-3.jpg"
    },
    {
      id: 4,
      title: "Comment éviter les litiges fonciers : Guide préventif",
      excerpt: "Les litiges fonciers peuvent être coûteux et longs. Découvrez comment les prévenir avec nos conseils d'experts.",
      date: "1 Décembre 2024",
      category: "Foncier",
      readTime: "6 min de lecture",
      image: "/placeholder-blog-4.jpg"
    },
    {
      id: 5,
      title: "Tendances du marché immobilier camerounais en 2024",
      excerpt: "Analyse des tendances actuelles du marché immobilier au Cameroun et perspectives pour l'année à venir.",
      date: "25 Novembre 2024",
      category: "Immobilier",
      readTime: "8 min de lecture",
      image: "/placeholder-blog-5.jpg"
    },
    {
      id: 6,
      title: "Construire écologique au Cameroun : Défis et solutions",
      excerpt: "L'importance de la construction durable au Cameroun et les solutions innovantes pour des bâtiments respectueux de l'environnement.",
      date: "20 Novembre 2024",
      category: "Construction",
      readTime: "5 min de lecture",
      image: "/placeholder-blog-6.jpg"
    }
  ];

  const categories = ["Tous", "Foncier", "Immobilier", "Construction"];
  return (
    <UnderConstruction />
  );
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary to-primary/80 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Blog & Actualités
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Restez informé des dernières actualités et conseils en foncier, immobilier et construction
          </p>
        </div>
      </section>

      {/* Blog Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {/* Categories Filter */}
          <div className="flex flex-wrap gap-4 mb-12 justify-center">
            {categories.map((category) => (
              <Button
                key={category}
                variant={category === "Tous" ? "default" : "outline"}
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>

          {/* Featured Article */}
          <div className="mb-16">
            <h2 className="text-2xl font-bold mb-8">Article à la Une</h2>
            <Card className="overflow-hidden">
              <div className="grid md:grid-cols-2">
                <div className="bg-gray-200 h-64 md:h-auto flex items-center justify-center">
                  <span className="text-gray-500">Image Article Principal</span>
                </div>
                <CardContent className="p-8">
                  <div className="flex items-center gap-4 mb-4">
                    <span className="bg-primary text-white px-3 py-1 rounded-full text-sm">
                      {articles[0].category}
                    </span>
                    <span className="text-gray-500 text-sm">{articles[0].date}</span>
                    <span className="text-gray-500 text-sm">{articles[0].readTime}</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-4">{articles[0].title}</h3>
                  <p className="text-gray-600 mb-6">{articles[0].excerpt}</p>
                  <Button asChild>
                    <Link href={`/blog/${articles[0].id}`}>Lire l'article</Link>
                  </Button>
                </CardContent>
              </div>
            </Card>
          </div>

          {/* Articles Grid */}
          <div>
            <h2 className="text-2xl font-bold mb-8">Derniers Articles</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {articles.slice(1).map((article) => (
                <Card key={article.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="bg-gray-200 h-48 flex items-center justify-center">
                    <span className="text-gray-500">Image Article</span>
                  </div>
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`px-2 py-1 rounded-full text-xs text-white ${
                        article.category === 'Foncier' ? 'bg-primary' :
                        article.category === 'Immobilier' ? 'bg-accent' :
                        'bg-orange-600'
                      }`}>
                        {article.category}
                      </span>
                      <span className="text-gray-500 text-xs">{article.readTime}</span>
                    </div>
                    <CardTitle className="text-lg leading-tight">{article.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">{article.excerpt}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500 text-xs">{article.date}</span>
                      <Button asChild variant="outline" size="sm">
                        <Link href={`/blog/${article.id}`}>Lire plus</Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Newsletter Subscription */}
          <div className="mt-16">
            <Card className="bg-gradient-to-r from-primary to-primary/80 text-white">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold mb-4">Restez Informé</h3>
                <p className="mb-6 max-w-2xl mx-auto">
                  Abonnez-vous à notre newsletter pour recevoir les derniers articles et conseils
                  directement dans votre boîte mail.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <input
                    type="email"
                    placeholder="Votre adresse email"
                    className="flex-1 px-4 py-2 rounded-lg text-gray-900"
                  />
                  <Button variant="secondary">
                    S'abonner
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}