import { Metadata } from 'next';

/**
 * Metadata for the API Documentation page
 */
export const metadata: Metadata = {
  title: 'API Documentation - Charlie Oscar Consulting',
  description: 'API documentation for Charlie Oscar Consulting services',
};

/**
 * Layout for the API Documentation page
 * 
 * This layout wraps the API documentation page with a container.
 * 
 * @param children The page content
 * @returns The layout component
 */
export default function ApiDocsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gray-50">
      {children}
    </div>
  );
}
