import React from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default function UnderConstruction() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] py-16 px-4">
      <Image src="/logo.svg" alt="Charlie Oscar Consulting Logo" width={150} height={200} className="mb-6" />
      {/* Animated Construction SVG */}
      <div className="mb-6">
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-bounce">
          <rect x="20" y="80" width="80" height="20" rx="6" fill="#FBBF24" />
          <rect x="35" y="60" width="50" height="20" rx="4" fill="#F59E42" />
          <rect x="50" y="40" width="20" height="20" rx="3" fill="#FBBF24" />
          <path d="M60 40V30" stroke="#F59E42" strokeWidth="4" strokeLinecap="round" className="animate-pulse" />
          <circle cx="60" cy="30" r="4" fill="#F59E42" className="animate-pulse" />
        </svg>
      </div>
      <h2 className="text-3xl font-bold text-gray-900 mb-4">Page en construction</h2>
      <p className="text-lg text-gray-700 mb-6 text-center max-w-md">
        Cette page est actuellement en cours de développement.<br />
        Revenez bientôt pour découvrir nos nouvelles fonctionnalités et contenus !
      </p>
      <Button asChild size="lg" className="px-8">
        <Link href="/">Retour à l'accueil</Link>
      </Button>
    </div>
  );
}
