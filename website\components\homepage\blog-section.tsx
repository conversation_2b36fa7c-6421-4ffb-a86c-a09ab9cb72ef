"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Calendar, Clock, User } from "lucide-react";

export function BlogSection() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const articles = [
    {
      id: 1,
      title: "Guide complet de la sécurisation foncière au Cameroun",
      excerpt: "Découvrez les étapes essentielles pour sécuriser vos titres fonciers et éviter les pièges juridiques courants.",
      category: "Foncier",
      author: "Charlie Oscar",
      date: "15 Mars 2024",
      readTime: "8 min",
      featured: true,
      tags: ["Titres fonciers", "Législation", "Guide pratique"]
    },
    {
      id: 2,
      title: "Investir dans l'immobilier camerounais : opportunités et défis",
      excerpt: "Analyse du marché immobilier local et conseils pour réussir vos investissements immobiliers au Cameroun.",
      category: "Immobilier",
      author: "Équipe CO",
      date: "10 Mars 2024",
      readTime: "6 min",
      featured: false,
      tags: ["Investissement", "Marché", "Stratégie"]
    },
    {
      id: 3,
      title: "Nouvelles réglementations en construction : ce qui change",
      excerpt: "Point sur les dernières évolutions réglementaires dans le secteur de la construction et leur impact sur vos projets.",
      category: "Construction",
      author: "Expert CO",
      date: "5 Mars 2024",
      readTime: "5 min",
      featured: false,
      tags: ["Réglementation", "Construction", "Actualités"]
    }
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Foncier":
        return "bg-primary/10 text-primary border-primary/20";
      case "Immobilier":
        return "bg-accent/10 text-accent border-accent/20";
      case "Construction":
        return "bg-secondary/10 text-secondary border-secondary/20";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
              Ressources et actualités
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Derniers articles et ressources utiles
            </h2>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Restez informé des dernières actualités et bénéficiez de nos conseils 
              d'experts pour optimiser vos projets fonciers et immobiliers.
            </p>
          </div>

          {/* Featured Article */}
          <div className={`mb-12 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            {articles.filter(article => article.featured).map((article) => (
              <Card key={article.id} className="overflow-hidden shadow-lg border-0 hover:shadow-xl transition-all duration-300 group">
                <div className="grid lg:grid-cols-2 gap-0">
                  {/* Image Placeholder */}
                  <div className="relative bg-gradient-to-br from-primary/10 to-accent/10 p-8 lg:p-12 flex items-center justify-center min-h-[300px]">
                    <div className="text-center space-y-4">
                      <div className="w-20 h-20 bg-white/20 rounded-full mx-auto flex items-center justify-center backdrop-blur-sm">
                        <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                      </div>
                      <Badge variant="secondary" className={`${getCategoryColor(article.category)} border`}>
                        Article vedette
                      </Badge>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-8 lg:p-12 space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={getCategoryColor(article.category)}>
                          {article.category}
                        </Badge>
                        {article.tags.slice(0, 2).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      
                      <h3 className="text-2xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                        {article.title}
                      </h3>
                      
                      <p className="text-gray-600 leading-relaxed">
                        {article.excerpt}
                      </p>
                    </div>

                    {/* Meta Info */}
                    <div className="flex items-center space-x-6 text-sm text-gray-500 py-4 border-t border-gray-200">
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4" />
                        <span>{article.author}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4" />
                        <span>{article.date}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4" />
                        <span>{article.readTime}</span>
                      </div>
                    </div>

                    <Button asChild className="group/btn">
                      <Link href={`/blog/${article.id}`} className="flex items-center">
                        Lire l'article complet
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Other Articles */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {articles.filter(article => !article.featured).map((article, index) => (
              <div
                key={article.id}
                className={`transition-all duration-700 ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                }`}
                style={{ transitionDelay: `${(index + 1) * 200}ms` }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 group border-2 border-gray-100 hover:border-primary/20">
                  <CardHeader className="space-y-4">
                    {/* Image Placeholder */}
                    <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                      <div className="text-center space-y-2">
                        <div className="w-12 h-12 bg-gray-300 rounded-full mx-auto flex items-center justify-center">
                          <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <p className="text-gray-400 text-xs">Image à venir</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={getCategoryColor(article.category)}>
                          {article.category}
                        </Badge>
                      </div>
                      
                      <CardTitle className="text-lg font-bold text-gray-900 group-hover:text-primary transition-colors duration-300 line-clamp-2">
                        {article.title}
                      </CardTitle>
                      
                      <CardDescription className="text-gray-600 line-clamp-3">
                        {article.excerpt}
                      </CardDescription>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Meta Info */}
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>{article.date}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{article.readTime}</span>
                      </div>
                    </div>

                    <Button asChild variant="outline" size="sm" className="w-full group/btn">
                      <Link href={`/blog/${article.id}`} className="flex items-center justify-center">
                        Lire l'article
                        <ArrowRight className="ml-2 h-3 w-3 transition-transform group-hover/btn:translate-x-1" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>

          {/* CTA */}
          <div className={`text-center transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <Button asChild size="lg" variant="outline" className="group">
              <Link href="/blog" className="flex items-center">
                Voir toutes nos ressources
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
