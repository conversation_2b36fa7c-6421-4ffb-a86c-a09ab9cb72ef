"use client";

import React, { useState, useEffect } from "react";
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from "lucide-react";
import { Button } from "@/components/ui/button";

export type NotificationType = "success" | "error" | "info" | "warning";

interface NotificationPopoverProps {
  message: string;
  type?: NotificationType;
  open: boolean;
  onClose: () => void;
  autoClose?: boolean;
  autoCloseTime?: number;
}

export function NotificationPopover({
  message = '',
  type = "info",
  open,
  onClose,
  autoClose = false,
  autoCloseTime = 50000,
}: NotificationPopoverProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (open) {
      setIsVisible(true);

      // Auto close after specified time if autoClose is true
      if (autoClose) {
        const timer = setTimeout(() => {
          setIsVisible(false);
          setTimeout(onClose, 300); // Allow animation to complete
        }, autoCloseTime);

        return () => clearTimeout(timer);
      }
    } else {
      setIsVisible(false);
    }
  }, [open, autoClose, autoCloseTime, onClose]);

  // Handle manual close
  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // Allow animation to complete
  };

  // Define background color based on type
  const getBgColor = () => {
    switch (type) {
      case "success":
        return "bg-green-100 border-green-500 text-green-800";
      case "error":
        return "bg-red-100 border-red-500 text-red-800";
      case "warning":
        return "bg-yellow-100 border-yellow-500 text-yellow-800";
      case "info":
      default:
        return "bg-blue-100 border-blue-500 text-blue-800";
    }
  };

  // Get the appropriate icon based on notification type
  const getIcon = () => {
    switch (type) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-600 mr-3 flex-shrink-0" />;
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-600 mr-3 flex-shrink-0" />;
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3 flex-shrink-0" />;
      case "info":
      default:
        return <Info className="h-5 w-5 text-blue-600 mr-3 flex-shrink-0" />;
    }
  };

  if (!open && !isVisible) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/50 transition-opacity duration-300"
         style={{ opacity: isVisible ? 1 : 0 }}
         onClick={(e) => {
           // Close when clicking outside the popover
           if (e.target === e.currentTarget) {
             handleClose();
           }
         }}>
      <div className={`max-w-md w-full mx-4 p-6 rounded-lg shadow-xl border-l-4 ${getBgColor()} transition-transform duration-300 bg-white`}
           style={{ transform: isVisible ? 'scale(1)' : 'scale(0.9)' }}>
        <div className="flex justify-between items-start">
          <div className="flex-1 flex items-start">
            {getIcon()}
            <p className="text-sm font-medium">{message}</p>
          </div>
          <button
            onClick={handleClose}
            className="ml-4 text-gray-500 hover:text-gray-700 focus:outline-none"
            aria-label="Close"
          >
            <X size={18} />
          </button>
        </div>

        <div className="mt-4 flex justify-end">
          <Button
            onClick={handleClose}
            variant="outline"
            size="sm"
            className="px-4"
          >
            OK
          </Button>
        </div>
      </div>
    </div>
  );
}

// Context to manage notifications globally
interface NotificationContextType {
  showNotification: (message: string, type?: NotificationType) => void;
  closeNotification: () => void;
}

const NotificationContext = React.createContext<NotificationContextType | undefined>(undefined);

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [notification, setNotification] = useState<{
    message: string;
    type: NotificationType;
    open: boolean;
  }>({
    message: "",
    type: "info",
    open: false,
  });

  const showNotification = (message: string, type: NotificationType = "info") => {
    // Ensure message is not empty or undefined
    const safeMessage = message?.trim() ? message : 'An operation was completed';
    setNotification({ message: safeMessage, type, open: true });
  };

  const closeNotification = () => {
    setNotification((prev) => ({ ...prev, open: false }));
  };

  return (
    <NotificationContext.Provider value={{ showNotification, closeNotification }}>
      {children}
      <NotificationPopover
        message={notification.message}
        type={notification.type}
        open={notification.open}
        onClose={closeNotification}
      />
    </NotificationContext.Provider>
  );
}

// Hook to use the notification context
export function useNotification() {
  const context = React.useContext(NotificationContext);
  if (context === undefined) {
    throw new Error("useNotification must be used within a NotificationProvider");
  }
  return context;
}
