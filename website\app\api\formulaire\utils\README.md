# Google Drive Integration Utilities

This directory contains utilities for integrating with Google Drive, specifically for uploading files.

## Files

- `google-drive.ts` - Core utility for uploading files to Google Drive
- `upload-form-files.ts` - Helper functions for uploading form files to Google Drive

## Setup

Before using these utilities, you need to set up a Google Cloud project and create a service account:

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Drive API
4. Create a service account
5. Generate a JSON key for the service account
6. Create a folder in Google Drive and share it with the service account email
7. Copy the `.env.example` file to `.env.local` in the root of the website directory
8. Update the environment variables in `.env.local` with the values from the JSON key and the folder ID

### Environment Variables

The following environment variables need to be set:

```
# Google Drive API credentials
GOOGLE_DRIVE_TYPE="service_account"
GOOGLE_DRIVE_PROJECT_ID="your-project-id"
GOOGLE_DRIVE_PRIVATE_KEY_ID="your-private-key-id"
GOOGLE_DRIVE_PRIVATE_KEY="-----<PERSON><PERSON>IN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
GOOGLE_DRIVE_CLIENT_EMAIL="<EMAIL>"
GOOGLE_DRIVE_CLIENT_ID="your-client-id"
GOOGLE_DRIVE_AUTH_URI="https://accounts.google.com/o/oauth2/auth"
GOOGLE_DRIVE_TOKEN_URI="https://oauth2.googleapis.com/token"
GOOGLE_DRIVE_AUTH_PROVIDER_CERT_URL="https://www.googleapis.com/oauth2/v1/certs"
GOOGLE_DRIVE_CLIENT_CERT_URL="https://www.googleapis.com/robot/v1/metadata/x509/your-service-account-email%40your-project-id.iam.gserviceaccount.com"
GOOGLE_DRIVE_UNIVERSE_DOMAIN="googleapis.com"

# Google Drive folder ID where files will be uploaded by default
GOOGLE_DRIVE_FOLDER_ID="your-google-drive-folder-id"

# Google Drive API scopes
GOOGLE_DRIVE_SCOPES="https://www.googleapis.com/auth/drive.file"
```

## Usage

### Basic File Upload

```typescript
import { uploadToGoogleDrive, getMimeType } from '@/app/api/formulaire/utils/google-drive';
import fs from 'fs/promises';

async function uploadFile() {
  // Read the file
  const fileContent = await fs.readFile('path/to/file.pdf');

  // Upload the file to Google Drive
  const result = await uploadToGoogleDrive(
    'file.pdf',
    'application/pdf',
    fileContent,
    { makePublic: true }
  );

  if (result.success) {
    console.log('File uploaded successfully!');
    console.log('File ID:', result.fileId);
    console.log('View Link:', result.webViewLink);
  } else {
    console.error('Failed to upload file:', result.error);
  }
}
```

### Creating a Folder and Uploading Files

```typescript
import { uploadToGoogleDrive, createDriveFolder } from '@/app/api/formulaire/utils/google-drive';
import fs from 'fs/promises';

// Method 1: Create a folder first, then upload files to it
async function createFolderAndUploadFiles() {
  // Create a folder
  const folder = await createDriveFolder('My Uploads');

  // Read the file
  const fileContent = await fs.readFile('path/to/file.pdf');

  // Upload the file to the folder
  const result = await uploadToGoogleDrive(
    'file.pdf',
    'application/pdf',
    fileContent,
    { folderId: folder.id }
  );

  console.log('File uploaded to folder:', folder.name);
}

// Method 2: Create a folder and upload in one step
async function uploadFileWithFolder() {
  // Read the file
  const fileContent = await fs.readFile('path/to/file.pdf');

  // Upload the file and create a folder in one step
  const result = await uploadToGoogleDrive(
    'file.pdf',
    'application/pdf',
    fileContent,
    { createFolder: 'My Uploads' }
  );

  if (result.folder) {
    console.log('File uploaded to new folder:', result.folder.name);
  }
}
```

### Uploading Form Files

```typescript
import { uploadFormFiles } from '@/app/api/formulaire/utils/upload-form-files';

async function handleFormSubmission(files: File[], referenceNumber: string) {
  const results = await uploadFormFiles(files, referenceNumber);

  console.log('Upload results:', results);
}
```

## Testing

There are two ways to test the Google Drive integration:

1. **Test UI** - A web interface for testing the upload functionality
   - Visit `/test/google-drive` in your browser
   - Select files to upload
   - View the results

2. **Integration Test** - A script that tests the integration programmatically
   - Run `npx ts-node --project tsconfig.json app/api/test/google-drive/integration-test.ts`

## Moving to Production

Before deploying to production, make sure to:

1. Set the environment variables in your production environment (not in .env files)
2. Consider using a secret management service for storing the credentials
3. Set up proper error handling and logging
4. Implement rate limiting to avoid exceeding API quotas
5. Consider implementing file deduplication to avoid uploading the same file multiple times
6. Add monitoring and alerting for API usage and errors
7. Implement a retry mechanism for failed uploads
8. Consider adding a queue system for handling large numbers of uploads
