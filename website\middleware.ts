import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { areTestRoutesEnabled } from './app/utils/environment';

/**
 * Middleware function that runs before each request
 * 
 * This middleware checks if the request is for a test route and blocks access
 * if test routes are disabled in the current environment.
 * 
 * @param request The incoming request
 * @returns The response or undefined to continue processing
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if this is a test route
  const isTestRoute = pathname.startsWith('/api/test') || pathname.startsWith('/test');

  // If this is a test route and test routes are disabled, return 404
  if (isTestRoute && !areTestRoutesEnabled()) {
    return new NextResponse(
      JSON.stringify({
        success: false,
        message: 'Test routes are disabled in this environment',
      }),
      {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }

  // Continue processing the request
  return NextResponse.next();
}

/**
 * Configure which paths the middleware should run on
 */
export const config = {
  // Match all API routes and page routes that start with 'test'
  matcher: ['/api/test/:path*', '/test/:path*'],
};
