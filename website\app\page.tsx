import { HeroSection } from "@/components/homepage/hero-section";
import { CompanyIntro } from "@/components/homepage/company-intro";
import { DomainsSection } from "@/components/homepage/domains-section";
import { FoncierServices } from "@/components/homepage/foncier-services";
import { ProjectsShowcase } from "@/components/homepage/projects-showcase";
import { WhyChooseUs } from "@/components/homepage/why-choose-us";
import { BlogSection } from "@/components/homepage/blog-section";
import { ContactForm } from "@/components/homepage/contact-form";
import { CTASection } from "@/components/homepage/cta-section";
import { getHomeContent } from "@/app/cms/utils/home";

export default function Home() {
  const content = getHomeContent();
  return (
    <div className="min-h-screen py-16 sm:py-8">
      <HeroSection content={content.hero} />
      <CompanyIntro content={content.about} />
      <DomainsSection content={content.services} section={content.domainsSection} />
      <FoncierServices />
      {/* <ProjectsShowcase /> To be implemented later */}
      <WhyChooseUs content={content.whyChooseUs} />
      <CTASection content={content.ctaSection} />
      {/* <BlogSection /> */}
      <ContactForm content={content.contactForm} />
    </div>
  );
}
