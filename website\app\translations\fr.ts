// French translations
const fr = {
  fill:"Merci de remplir soigneusement ce formulaire. Un expert de <PERSON> Consulting vous contactera dans les plus brefs délais pour vous accompagner dans votre projet.",
  common: {
    next: "Continuer",
    previous: "Précédent",
    submit: "Soumettre ma demande",
    processing: "Traitement en cours...",
    required: "*",
    optional: "Facultatif",
    fileSize: "La taille maximale est de 2 Mo",
    filePreview: "Aperçu:",
    fileSelected: "Fichier sélectionné:",
    fileFormats: ".pdf, .jpg, .png, max 2 Mo par fichier",
    notProvided: "Non renseigné",
    notSpecified: "Non précisé",
    uploading: "Téléchargement...",
    uploaded: "Téléchargé",
    uploadFailed: "Échec du téléchargement",
    retry: "Réessayer",
    // success message for succesfull form upload
    success:"Votre demande a été soumise avec succès. Un conseiller de Charlie Oscar Consulting vous contactera dans les plus brefs délais.",
    online: "En ligne",
    offline: "Hors ligne",
    connectionLost: "Connexion Internet perdue. Certaines fonctionnalités peuvent être indisponibles.",
    connectionRestored: "Connexion Internet rétablie.",
    fileUploadOffline:"Vous ne pouvez pas uploader des fichier quand vous êtes hors ligne.",
    waitForUploads: "Veuillez attendre la fin des téléchargements...",
    form: "Formulaire",
    apiDocs: "Documentation API",
    testTools: "Outils de test",
    dropFilesHere: "Déposez vos fichiers ici ou cliquez pour parcourir",
    maxFileSize: "Taille maximale: 2 Mo",
    fileTooLarge: "Le fichier est trop volumineux. La taille maximale est de 2 Mo",
    invalidFileType: "Type de fichier non pris en charge",
    fileNameTooShort: "Le nom du fichier doit comporter au moins 3 caractères",
    cancel: "Annuler",
  },

  validation: {
    required: "Ce champ est obligatoire",
    minLength: "Doit contenir au moins {min} caractères",
    email: "Veuillez entrer une adresse email valide",
    phone: "Veuillez entrer un numéro de téléphone valide",
    consent: "Vous devez donner votre consentement pour continuer",
    date: "Veuillez entrer une date valide",
    selection: "Veuillez faire une sélection",
    fileRequired: "Veuillez téléverser un fichier",
    fileSize: "La taille du fichier doit être inférieure à {size}",
    fileType: "Type de fichier non pris en charge",
  },

  sidebar: {
    title: "Charlie Oscar",
    subtitle: "Consulting",
    step: "Étape",
    of: "/",
    currentStep: "Étape actuelle",
  },

  steps: {
    personal: {
      name: "Informations Personnelles",
      description: "Entrez vos informations personnelles pour nous permettre de mieux vous connaître.",
    },
    emergency: {
      name: "Contacts d'urgence et procédure",
      description: "Indiquez vos contacts d'urgence et la procédure souhaitée.",
    },
    documents: {
      name: "Documents et localisation du terrain",
      description: "Fournissez les documents et les informations sur la localisation du terrain.",
    },
    summary: {
      name: "Récapitulatif et confirmation",
      description: "Vérifiez toutes les informations avant de soumettre votre demande.",
    },
  },

  stepPersonal: {
    title: "Vos Informations Personnelles",
    intro: "Veuillez remplir tous les champs obligatoires marqués d'un astérisque (*) avec précision.",
    dataProtection: {
      title: "Protection des données",
      description: "Les informations collectées sont strictement confidentielles et utilisées uniquement dans le cadre du traitement de votre demande foncière par Charlie Oscar Consulting SARL.",
      consent: "J'accepte que mes informations soient traitées par Charlie Oscar Consulting SARL",
    },
    personalInfo: {
      title: "Informations personnelles",
      fullName: {
        label: "Nom & prénoms",
        placeholder: "Ex: MBARGA Paul Serge",
      },
      gender: {
        label: "Sexe",
        male: "Masculin",
        female: "Féminin",
      },
      birthDate: {
        label: "Date de naissance",
      },
      birthPlace: {
        label: "Lieu de naissance",
        placeholder: "Ex: Yaoundé, Cameroun",
      },
      nationality: {
        label: "Nationalité",
        placeholder: "Sélectionnez votre nationalité",
        options: {
          cameroon: "Camerounaise",
          france: "Française",
          senegal: "Sénégalaise",
          ivoryCoast: "Ivoirienne",
          gabon: "Gabonaise",
          congo: "Congolaise",
          other: "Autre",
        },
      },
      profession: {
        label: "Profession",
        placeholder: "Ex: Enseignant, Commerçant",
      },
    },
    contactInfo: {
      title: "Informations de contact",
      primaryPhone: {
        label: "Téléphone principal",
        placeholder: "Ex: +237 6XXXXXXXX",
      },
      secondaryPhone: {
        label: "Téléphone secondaire",
        placeholder: "Ex: +237 6XXXXXXXX",
      },
      email: {
        label: "Email",
        placeholder: "<EMAIL>",
      },
      address: {
        label: "Adresse actuelle",
        placeholder: "Ex: Quartier Bastos, Rue 1.890, Yaoundé",
      },
      idDocument: {
        label: "Document d'identité",
        description: "Téléversez une copie claire et lisible de votre CNI ou passeport",
        notUploaded: "Aucun document téléversé",
        nameRequired: "Veuillez d'abord saisir votre nom complet",
      },
    },
  },

  stepDocumentsLocation: {
    title: "Documents et localisation du terrain",
    documents: {
      title: "Documents",
      intro: "Veuillez indiquer et téléverser les documents que vous possédez déjà concernant cette procédure. Le plan de localisation est fortement recommandé s'il est disponible.",
      availableDocs: {
        label: "Documents disponibles",
        help: "Cochez tous les documents que vous pouvez fournir",
        options: {
          customaryProperty: "Attestation de propriété coutumière",
          saleAct: "Acte de vente / donation",
          occupationPermit: "Autorisation d'occupation",
          locationPlan: "Plan de localisation / croquis du terrain",
          landTitle: "Titre foncier",
          otherDocs: "Autres documents",
        },
      },
      upload: {
        label: "Téléversement de documents",
        documentLabel: "Document",
        description: "Téléversez vos documents les plus pertinents pour votre demande une a la fois. Vous pourrez fournir des documents supplémentaires ultérieurement.",
      },
      details: {
        label: "Précisions sur les documents",
        placeholder: "Ex: Le titre foncier date de 2015, l'acte de vente notarié est joint...",
        help: "Facultatif : précisez la nature ou l'état des documents fournis",
      },
    },
    location: {
      title: "Localisation",
      intro: "Veuillez fournir les informations précises concernant la localisation du terrain concerné par la procédure.",
      zoneType: {
        label: "Type de zone",
        urban: "Urbaine",
        rural: "Rurale",
        help: "Sélectionnez le type d'environnement où se situe le terrain",
      },
      region: {
        label: "Région",
        placeholder: "Sélectionnez une région",
        options: [
          "Centre", "Littoral", "Sud", "Est", "Ouest",
          "Nord-Ouest", "Sud-Ouest", "Nord", "Extrême-Nord", "Adamaoua"
        ],
      },
      department: {
        label: "Département",
        placeholder: "Sélectionnez ou preciser un département",
        options: {
          centre: ["Mfoundi", "Nyong-et-Kellé", "Nyong-et-So'o", "Lekié", "Mbam-et-Inoubou"],
          littoral: ["Wouri", "Sanaga-Maritime", "Nkam", "Moungo"],
          south: ["Océan", "Vallée-du-Ntem", "Mvila", "Dja-et-Lobo"],
          east: ["Haut-Nyong", "Kadey", "Lom-et-Djérem", "Boumba-et-Ngoko"],
          west: ["Mifi", "Menoua", "Bamboutos", "Haut-Nkam", "Ndé", "Koung-Khi"],
        },
      },
      subdivision: {
        label: "Arrondissement",
        placeholder: "Ex: Yaoundé II, Douala III, Bafoussam I",
        help: "Précisez l'arrondissement où se situe le terrain",
      },
      neighborhood: {
        label: "Quartier ou village",
        placeholder: "Ex: Mokolo, Bonapriso, Banengo...",
      },
      locationDetails: {
        label: "Lieu-dit",
        placeholder: "Ex: À proximité de l'église Saint-Jean, derrière le marché central...",
        help: "Repère géographique spécifique facilitant la localisation du terrain",
      },
      area: {
        label: "Superficie estimée",
        placeholder: "Ex: 500",
        unit: "m²",
        help: "Surface approximative du terrain en mètres carrés",
      },
    },
  },

  stepEmergencyProcedure: {
    title: "Procédure et urgence",
    emergencyContacts: {
      title: "Contacts d'urgence",
      intro: "Veuillez indiquer au moins une personne à contacter en cas d'indisponibilité.",
      contact1: {
        title: "Personne 1 (obligatoire)",
        name: {
          label: "Nom & prénoms",
          placeholder: "Ex: ATANGANA Marie Claire",
        },
        phone: {
          label: "Téléphone",
          placeholder: "Ex: +237 6XXXXXXXX",
        },
        relation: {
          label: "Lien avec le demandeur",
          placeholder: "Ex: Frère, Conjoint(e), Collègue",
          help: "Précisez votre relation avec cette personne",
        },
      },
      contact2: {
        title: "Personne 2 (facultatif)",
        name: {
          label: "Nom & prénoms",
          placeholder: "Ex: ESSOMBA Jean",
        },
        phone: {
          label: "Téléphone",
          placeholder: "Ex: +237 6XXXXXXXX",
        },
        relation: {
          label: "Lien avec le demandeur",
          placeholder: "Ex: Sœur, Parent, Ami(e)",
        },
      },
    },
    landStatus: {
      title: "Statut foncier",
      intro: "Veuillez préciser votre statut par rapport au terrain concerné par la procédure.",
      label: "Votre statut",
      options: {
        owner: "Propriétaire",
        heir: "Héritier",
        buyer: "Acheteur",
        applicant: "Demandeur d'attribution",
        other: "Autre",
      },
      otherLabel: "Précision du statut",
      otherPlaceholder: "Veuillez préciser votre statut",
    },
    procedureType: {
      title: "Type de procédure",
      intro: "Veuillez indiquer le type de procédure foncière que vous souhaitez entreprendre.",
      label: "Type de procédure souhaitée",
      help: "Cette sélection déterminera les documents et procédures nécessaires pour votre dossier",
      options: [
        "Dérogation ministérielle spéciale",
        "Immatriculation directe",
        "Immatriculation par voie de concession",
        "Décision de gré à gré",
        "Protocole d'accord de bonne volonté",
        "Morcellement",
        "Mutation totale",
        "Mutation par décès",
        "Protocole d'achat d'un terrain non titré",
        "Dossier technique",
        "Indemnisation",
        "Rétrocession",
        "Recours gracieux",
        "Réhabilitation de titre foncier",
        "Autre"
      ],
      otherLabel: "Précisions sur la procédure",
      otherPlaceholder: "Veuillez préciser la nature exacte de votre demande",
      otherHelp: "Donnez le maximum de détails pour nous permettre de préparer votre dossier",
    },
    additionalInfo: {
      label: "Informations complémentaires",
      placeholder: "Saisissez ici toute information complémentaire concernant votre demande",
      help: "Facultatif : tout contexte ou détail pouvant nous aider à mieux comprendre votre situation",
    },
  },

  stepSummary: {
    title: "Récapitulatif et soumission",
    intro: "Veuillez vérifier les informations saisies avant de soumettre votre demande.",
    sections: {
      personal: {
        title: "Informations personnelles",
      },
      emergency: {
        title: "Contacts d'urgence",
      },
      procedure: {
        title: "Procédure",
      },
      documents: {
        title: "Documents fournis",
      },
      location: {
        title: "Localisation du terrain",
      },
      editButton: "Modifier cette section",
    },
    additionalComments: {
      label: "Commentaires additionnels",
      placeholder: "Ajoutez ici toute information complémentaire concernant votre demande",
      help: "Facultatif : précisez toute information qui pourrait nous aider à mieux traiter votre demande",
    },
    finalConsent: {
      dataProtection: "En soumettant ce formulaire, vous acceptez que les informations fournies soient traitées par Charlie Oscar Consulting SARL dans le strict cadre de votre demande foncière, conformément à la législation camerounaise sur la protection des données personnelles.",
      label: "Je confirme l'exactitude des informations fournies et accepte de soumettre ma demande",
    },
    submit: {
      button: "Soumettre ma demande",
      processing: "Traitement en cours...",
    },
    confirmation: {
      title: "Demande soumise avec succès",
      message: "Votre demande a été enregistrée. Un conseiller de Charlie Oscar Consulting vous contactera dans les plus brefs délais.",
      referenceLabel: "Référence de votre demande",
      referenceHelp: "Veuillez conserver cette référence pour toute communication future concernant votre dossier.",
      downloadButton: "Télécharger le récapitulatif",
      newFormButton: "Commencer un nouveau formulaire",
    },
    personalInfo: {
      title: "Informations personnelles",
      name: "Nom & prénoms",
      gender: "Sexe",
      birthDate: "Date de naissance",
      birthPlace: "Lieu de naissance",
      nationality: "Nationalité",
      profession: "Profession",
    },
    contactInfo: {
      title: "Informations de contact",
      phone: "Téléphone",
      email: "Email",
      address: "Adresse",
    },
    locationInfo: {
      title: "Localisation du terrain",
      region: "Région",
      department: "Département",
      subdivision: "Arrondissement",
      neighborhood: "Quartier/Village",
      area: "Superficie",
    },
    procedureInfo: {
      title: "Procédure",
      status: "Statut",
      type: "Type de procédure",
    },
    documents: {
      title: "Documents fournis",
      none: "Aucun document fourni",
    },
  },

  uploadPage: {
    title: "Téléverser des documents",
    description: "Téléversez votre formulaire complété et les documents justificatifs pour votre demande foncière.",
    mainFile: "Document principal",
    mainFileDescription: "Téléversez votre formulaire complété ou document principal (PDF uniquement).",
    mainFileRequired: "Veuillez téléverser votre document principal avant de soumettre.",
    relatedFiles: "Documents justificatifs",
    relatedFilesDescription: "Téléversez tous les documents justificatifs liés à votre demande.",
    addFiles: "Ajouter des fichiers",
    dragOrClick: "Glissez-déposez les fichiers ici ou cliquez pour parcourir",
    uploadedFiles: "Fichiers téléversés",
    uploadSuccess: "Vos documents ont été téléversés avec succès. Un conseiller de Charlie Oscar Consulting vous contacteras tres prochainement.",
    uploadSuccessWithRef: "Vos documents ont été téléversés avec succès avec le numéro de référence : {ref}. Un conseiller de Charlie Oscar Consulting vous contacteras tres prochainement.",
    dropMainFile: "Déposez votre document principal ici ou cliquez pour parcourir",
    selectFile: "Sélectionnez un fichier à téléverser",
    fileName: "Nom/Description du fichier",
    fileNamePlaceholder: "Entrez un nom ou une description pour ce fichier",
    pdfPreview: "Aperçu PDF",
    confirmWithErrors: "Certains fichiers présentent des erreurs de validation. Voulez-vous continuer sans eux ?",
    validationErrors: "Certains fichiers présentent des erreurs de validation et seront exclus de la soumission.",
    mainFileValidationError: "Le document principal présente des erreurs de validation. Veuillez les corriger avant de soumettre.",
  },
};

export default fr;
