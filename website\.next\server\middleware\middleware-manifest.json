{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4442ae7e._.js", "server/edge/chunks/[root-of-the-server]__e9ad0545._.js", "server/edge/chunks/edge-wrapper_1f641995.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/test/:path*{(\\\\.json)}?", "originalSource": "/api/test/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/test/:path*{(\\\\.json)}?", "originalSource": "/test/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QZPjl6R8b12JU0ik++o4llEwcy+Uu+mjvuZmYddXz8w=", "__NEXT_PREVIEW_MODE_ID": "a739b4fb623a06e54b9dda7aa0bdc46d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e605843c8f9cb3852c9d1532ac57826689dccd54da64082be8bcdb52ed8e1f85", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2961e5a6c53040a2d30770a8b6b302bd5ada95f2c1ea73af867ee2c3929ac1dc"}}}, "instrumentation": null, "functions": {}}