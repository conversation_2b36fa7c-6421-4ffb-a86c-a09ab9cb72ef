"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ChevronLeft, ChevronRight, MapPin, Clock, Award, Quote, ArrowRight, Star } from "lucide-react";

export function CaseStudiesSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % caseStudies.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const caseStudies = [
    {
      id: 1,
      title: "Sécurisation d'un terrain familial",
      location: "Yaoundé, Quartier Mvog-Ada",
      challenge: "Conflit de succession sur un terrain de 2000m²",
      solution: "Médiation familiale et régularisation administrative",
      results: {
        duration: "6 semaines",
        value: "+45% de valorisation",
        status: "Titre foncier obtenu"
      },
      testimonial: {
        text: "Grâce à Charlie Oscar, nous avons enfin pu résoudre un conflit qui durait depuis 10 ans. Leur approche professionnelle et leur connaissance du terrain ont fait la différence.",
        author: "Marie Ngono",
        role: "Propriétaire"
      },
      image: "/images/case-study-1.jpg",
      tags: ["Succession", "Médiation", "Yaoundé"]
    },
    {
      id: 2,
      title: "Acquisition d'un terrain commercial",
      location: "Douala, Zone industrielle",
      challenge: "Procédure d'acquisition complexe pour un investisseur",
      solution: "Accompagnement complet de l'étude à l'obtention du titre",
      results: {
        duration: "8 semaines",
        value: "Investissement sécurisé",
        status: "Projet réalisé"
      },
      testimonial: {
        text: "L'expertise de l'équipe nous a permis d'éviter de nombreux pièges. Nous recommandons vivement leurs services pour tout projet d'investissement foncier.",
        author: "Jean-Paul Mbida",
        role: "Investisseur"
      },
      image: "/images/case-study-2.jpg",
      tags: ["Commercial", "Investissement", "Douala"]
    },
    {
      id: 3,
      title: "Régularisation d'un lotissement",
      location: "Bafoussam, Quartier Tamdja",
      challenge: "Régularisation de 50 parcelles dans un lotissement",
      solution: "Coordination avec les autorités et suivi collectif",
      results: {
        duration: "12 semaines",
        value: "50 titres délivrés",
        status: "Projet finalisé"
      },
      testimonial: {
        text: "Un travail remarquable ! Ils ont su coordonner toutes les démarches pour l'ensemble du lotissement. Très professionnel et efficace.",
        author: "Paul Kamga",
        role: "Président du comité"
      },
      image: "/images/case-study-3.jpg",
      tags: ["Lotissement", "Collectif", "Bafoussam"]
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % caseStudies.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + caseStudies.length) % caseStudies.length);
  };

  return (
    <section ref={sectionRef} id="realisations" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
              <Award className="w-4 h-4 mr-2" />
              Nos réussites
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Nos réussites foncières
            </h2>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Découvrez comment nous avons aidé nos clients à sécuriser 
              leurs droits fonciers et valoriser leur patrimoine.
            </p>
          </div>

          {/* Carousel */}
          <div className={`relative transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="overflow-hidden rounded-2xl">
              <div 
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
              >
                {caseStudies.map((study) => (
                  <div key={study.id} className="w-full flex-shrink-0">
                    <Card className="border-0 shadow-lg">
                      <CardContent className="p-0">
                        <div className="grid lg:grid-cols-2 gap-0">
                          {/* Image */}
                          <div className="relative h-64 lg:h-full bg-gradient-to-br from-primary/20 to-accent/20">
                            <div className="absolute inset-0 bg-gradient-to-br from-gray-900/60 to-gray-800/40"></div>
                            <div className="absolute top-4 left-4 flex flex-wrap gap-2">
                              {study.tags.map((tag, index) => (
                                <span key={index} className="px-3 py-1 bg-white/20 backdrop-blur-sm text-white text-xs rounded-full">
                                  {tag}
                                </span>
                              ))}
                            </div>
                            <div className="absolute bottom-4 left-4 text-white">
                              <div className="flex items-center space-x-2 text-sm">
                                <MapPin className="w-4 h-4" />
                                <span>{study.location}</span>
                              </div>
                            </div>
                          </div>

                          {/* Content */}
                          <div className="p-8 space-y-6">
                            <div className="space-y-4">
                              <h3 className="text-2xl font-bold text-gray-900">
                                {study.title}
                              </h3>
                              
                              <div className="space-y-3">
                                <div>
                                  <h4 className="font-semibold text-gray-900 text-sm mb-1">Défi :</h4>
                                  <p className="text-gray-600 text-sm">{study.challenge}</p>
                                </div>
                                
                                <div>
                                  <h4 className="font-semibold text-gray-900 text-sm mb-1">Solution :</h4>
                                  <p className="text-gray-600 text-sm">{study.solution}</p>
                                </div>
                              </div>
                            </div>

                            {/* Results */}
                            <div className="grid grid-cols-3 gap-4 py-4 border-y border-gray-200">
                              <div className="text-center">
                                <div className="flex items-center justify-center mb-1">
                                  <Clock className="w-4 h-4 text-primary mr-1" />
                                </div>
                                <div className="text-lg font-bold text-gray-900">{study.results.duration}</div>
                                <div className="text-xs text-gray-600">Délai</div>
                              </div>
                              
                              <div className="text-center">
                                <div className="flex items-center justify-center mb-1">
                                  <Award className="w-4 h-4 text-primary mr-1" />
                                </div>
                                <div className="text-lg font-bold text-gray-900">{study.results.value}</div>
                                <div className="text-xs text-gray-600">Résultat</div>
                              </div>
                              
                              <div className="text-center">
                                <div className="flex items-center justify-center mb-1">
                                  <Star className="w-4 h-4 text-primary mr-1" />
                                </div>
                                <div className="text-lg font-bold text-gray-900">{study.results.status}</div>
                                <div className="text-xs text-gray-600">Statut</div>
                              </div>
                            </div>

                            {/* Testimonial */}
                            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                              <Quote className="w-6 h-6 text-primary" />
                              <p className="text-gray-700 italic text-sm leading-relaxed">
                                "{study.testimonial.text}"
                              </p>
                              <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                                  <span className="text-primary font-semibold text-sm">
                                    {study.testimonial.author.split(' ').map(n => n[0]).join('')}
                                  </span>
                                </div>
                                <div>
                                  <div className="font-semibold text-gray-900 text-sm">
                                    {study.testimonial.author}
                                  </div>
                                  <div className="text-gray-600 text-xs">
                                    {study.testimonial.role}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-between mt-8">
              <Button
                variant="outline"
                size="icon"
                onClick={prevSlide}
                className="rounded-full"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              {/* Dots */}
              <div className="flex space-x-2">
                {caseStudies.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                      index === currentSlide ? 'bg-primary' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="outline"
                size="icon"
                onClick={nextSlide}
                className="rounded-full"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Statistics */}
          <div className={`mt-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8">
              <div className="text-center space-y-6">
                <h3 className="text-2xl font-bold text-gray-900">
                  Nos résultats en chiffres
                </h3>
                
                <div className="grid md:grid-cols-4 gap-8">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">300+</div>
                    <div className="text-sm text-gray-600">Titres fonciers obtenus</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">95%</div>
                    <div className="text-sm text-gray-600">Taux de réussite</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">15j</div>
                    <div className="text-sm text-gray-600">Délai moyen</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">100%</div>
                    <div className="text-sm text-gray-600">Clients satisfaits</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* CTA */}
          <div className={`text-center mt-16 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-900">
                Votre projet sera notre prochaine réussite
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Rejoignez nos clients satisfaits et sécurisez votre patrimoine foncier 
                avec l'accompagnement de nos experts.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="group">
                  <Link href="/formulaire" className="flex items-center">
                    Commencer mon projet
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <Link href="/foncier/realisations">
                    Voir toutes les études de cas
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
