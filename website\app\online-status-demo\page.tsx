"use client";

import React from "react";
import { OnlineStatusIndicator } from "../components/online-status-indicator";
import { useOnlineStatus } from "../contexts/online-status-context";
import { useLanguage } from "../translations/language-context";

export default function OnlineStatusDemo() {
  const { isOnline } = useOnlineStatus();
  const { t } = useLanguage();

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Online Status Demo</h1>
      
      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Current Status</h2>
        <div className="flex items-center mb-4">
          <OnlineStatusIndicator className="mr-4" />
        </div>
        
        <div className="p-4 rounded-md mb-4" 
        //@ts-ignore
          className={isOnline ? "bg-green-100" : "bg-red-100"}
        >
          <p>
            {isOnline 
              ? "You are currently online. All features are available." 
              : "You are currently offline. Some features may be unavailable."}
          </p>
        </div>
      </div>
      
      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Testing Instructions</h2>
        <ol className="list-decimal pl-5 space-y-2">
          <li>To test offline mode, open your browser's developer tools (F12)</li>
          <li>Go to the Network tab and check "Offline" to simulate offline mode</li>
          <li>The status indicator should change to "Offline"</li>
          <li>Uncheck "Offline" to return to online mode</li>
          <li>The status indicator should change back to "Online"</li>
        </ol>
      </div>
    </div>
  );
}
