"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

// Define the form data structure
export interface FormData {
  // Step 1: Personal Information
  consent?: boolean;
  fullName?: string;
  gender?: "Masculin" | "Féminin";
  birthDate?: string;
  birthPlace?: string;
  nationality?: string;
  profession?: string;
  primaryPhone?: string;
  secondaryPhone?: string;
  email?: string;
  address?: string;
  idDocument?: File | null;

  // File upload tracking
  userFolderId?: string;
  userFolderName?: string;
  userFolderLink?: string;
  uploadedFileIds?: Record<string, string>; // Maps file names to their Google Drive IDs
  uploadedFileUrls?: Record<string, string>; // Maps file names to their Google Drive URLs (direct sharing links)
  uploadedFileNames?: Record<string, string>; // Maps original file names to their unique file names with UUID
  uploadStatus?: Record<string, 'uploading' | 'success' | 'error' | 'idle'>; // Tracks upload status for each file
  uploadErrors?: Record<string, string>; // Stores error messages for failed uploads

  // Step 2: Emergency Contacts and Procedure
  emergencyContact1Name?: string;
  emergencyContact1Phone?: string;
  emergencyContact1Relation?: string;
  emergencyContact2Name?: string;
  emergencyContact2Phone?: string;
  emergencyContact2Relation?: string;
  landStatus?: string;
  otherLandStatus?: string; // Changed from landStatusOther for consistency
  procedureTypes?: string[];
  otherProcedureType?: string; // Changed from procedureOtherDetails for consistency
  additionalInfo?: string;

  // Step 3: Documents and Location
  availableDocuments?: string[];
  uploadedDocuments?: File[];
  documentsDetails?: string;
  zoneType?: "Urbaine" | "Rurale";
  region?: string;
  department?: string;
  subdivision?: string;
  neighborhood?: string;
  locationDetails?: string;
  area?: string;

  // Step 4: Summary
  additionalComments?: string;
  finalConsent?: boolean;

  // Reference number for the submission
  referenceNumber?: string;

  // Flag to prevent duplicate PDF submissions
  pdfSubmitted?: boolean;
}

interface FormContextType {
  formData: FormData;
  updateFormData: (newData: Partial<FormData>) => void;
  resetFormData: () => void;
}

const FormContext = createContext<FormContextType | undefined>(undefined);

export function FormProvider({ children }: { children: ReactNode }) {
  // Initialize form data from local storage if available
  const [formData, setFormData] = useState<FormData>(() => {
    // Only run this code on the client side
    if (typeof window !== 'undefined') {
      const savedData = localStorage.getItem('formData');
      if (savedData) {
        try {
          // Parse the saved data
          const parsedData = JSON.parse(savedData);

          // Debug: Log the parsed data
          console.log('FormContext: Parsed data from localStorage:', parsedData);
          console.log('FormContext: uploadedFileUrls from localStorage:', parsedData.uploadedFileUrls);

          //set consent to true by default
          parsedData.consent = true;
          // make sure that when the form is loaded, the hasActiveUploads function resturns false
          if (parsedData.uploadStatus) {
            for (const key in parsedData.uploadStatus) {
              if (parsedData.uploadStatus[key] === 'uploading') {
                parsedData.uploadStatus[key] = 'idle'; // Reset to idle if any file is uploading
              }
            }
          }
          
          // Return the saved data
          return parsedData;
        } catch (error) {
          console.error('Error parsing saved form data:', error);
        }
      }
    }
    return {consent:true};
  });

  // Save form data to local storage whenever it changes
  useEffect(() => {
    // Only run this code on the client side
    if (typeof window !== 'undefined') {
      // Create a copy of the form data without file objects (which can't be serialized)
      const dataToSave = { ...formData };

      // Remove file objects before saving
      if (dataToSave.idDocument) delete dataToSave.idDocument;
      if (dataToSave.uploadedDocuments) delete dataToSave.uploadedDocuments;

      // Debug: Log the form data being saved to local storage
      console.log('Saving form data to local storage:', JSON.stringify(dataToSave, null, 2));
      console.log('uploadedFileUrls in form data:', dataToSave.uploadedFileUrls);

      // Save to local storage
      localStorage.setItem('formData', JSON.stringify(dataToSave));
    }
  }, [formData]);

  const updateFormData = (newData: Partial<FormData>) => {
    setFormData((prevData) => {
      // Special handling for uploadedFileUrls to ensure they're properly merged
      let mergedUploadedFileUrls = prevData.uploadedFileUrls || {};
      if (newData.uploadedFileUrls) {
        mergedUploadedFileUrls = { ...mergedUploadedFileUrls, ...newData.uploadedFileUrls };
        console.log('Merging uploadedFileUrls:', mergedUploadedFileUrls);
      }

      const updatedData = {
        ...prevData,
        ...newData,
        // Always use the merged uploadedFileUrls if newData contains uploadedFileUrls
        ...(newData.uploadedFileUrls ? { uploadedFileUrls: mergedUploadedFileUrls } : {})
      };

      return updatedData;
    });
  };

  // Function to completely reset the form data
  const resetFormData = () => {
    // Debug: Log the form data before reset
    console.log('FormContext: Form data before reset:', formData);
    console.log('FormContext: uploadedFileUrls before reset:', formData.uploadedFileUrls);

    // Clear form data state
    setFormData({});

    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('formData');
      console.log('Form data has been completely reset');
    }
  };

  return (
    <FormContext.Provider value={{ formData, updateFormData, resetFormData }}>
      {children}
    </FormContext.Provider>
  );
}

export function useFormContext() {
  const context = useContext(FormContext);
  if (context === undefined) {
    throw new Error("useFormContext must be used within a FormProvider");
  }
  return context;
}
