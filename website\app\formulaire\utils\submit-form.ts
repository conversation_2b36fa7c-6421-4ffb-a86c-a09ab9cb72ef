"use client";

/**
 * Form submission utilities
 *
 * This module provides utilities for submitting form data and associated files
 * to the server API. It handles uploading generated files (like PDFs) and
 * updating form data with submission results.
 *
 * @module submit-form
 * @requires ../components/form-context
 * @requires ./file-upload
 */

import type { FormData } from "../components/form-context";
import { generateUniqueFolderName } from "./file-upload";

/**
 * Interface for form submission result from the server API
 *
 * This interface represents the result of a form submission operation returned by the server API.
 * It includes information about the submission status, any generated files, and any errors that occurred.
 *
 * @interface FormSubmissionResult
 */
export interface FormSubmissionResult {
  /**
   * Whether the submission was successful
   *
   * @type {boolean}
   * @memberof FormSubmissionResult
   */
  success: boolean;

  /**
   * A unique identifier for the submission
   * Only present if the submission was successful
   *
   * @type {string}
   * @memberof FormSubmissionResult
   */
  submissionId?: string;

  /**
   * The ID of the uploaded file in Google Drive
   * Only present if a file was uploaded as part of the submission
   *
   * @type {string}
   * @memberof FormSubmissionResult
   */
  fileId?: string;

  /**
   * The name of the uploaded file
   * Only present if a file was uploaded as part of the submission
   *
   * @type {string}
   * @memberof FormSubmissionResult
   */
  fileName?: string;

  /**
   * The URL to view the file in Google Drive
   * Only present if a file was uploaded as part of the submission
   *
   * @type {string}
   * @memberof FormSubmissionResult
   */
  webViewLink?: string;

  /**
   * Information about the folder the submission was stored in
   * Only present if a folder was created or used for the submission
   *
   * @type {Object}
   * @property {string} id - The ID of the folder
   * @property {string} name - The name of the folder
   * @property {string} [webViewLink] - The URL to view the folder
   * @memberof FormSubmissionResult
   */
  folderInfo?: {
    id: string;
    name: string;
    webViewLink?: string;
  };

  /**
   * Error message if the submission failed
   * Only present if the submission was not successful (success = false)
   *
   * @type {string}
   * @memberof FormSubmissionResult
   */
  error?: string;
}

/**
 * Submit the form and upload any generated files to the server API
 *
 * This function submits the form data and any generated files (like PDFs) to the server API.
 * It handles creating a folder for the user if needed, uploading the generated file,
 * and updating the form data with the submission results.
 *
 * @param {FormData} formData - The current form data state to submit
 * @param {File | Buffer | Blob} generatedFile - The file generated from the form data (e.g., PDF)
 * @param {string} fileName - The name to give the generated file
 * @param {string} mimeType - The MIME type of the generated file (e.g., 'application/pdf')
 * @param {function} updateFormData - Function to update the form data state
 * @returns {Promise<FormSubmissionResult>} A promise that resolves to the result of the submission operation
 *
 * @throws {Error} The function handles errors internally and returns a FormSubmissionResult with success=false
 *
 * @example
 * // Submit form with a generated PDF
 * const pdfBlob = await generatePdf(formData);
 * const result = await submitForm(
 *   formData,
 *   pdfBlob,
 *   'customer-form.pdf',
 *   'application/pdf',
 *   updateFormData
 * );
 *
 * if (result.success) {
 *   console.log(`Form submitted successfully with ID: ${result.submissionId}`);
 *   if (result.folderInfo) {
 *     console.log(`Files stored in folder: ${result.folderInfo.name}`);
 *   }
 * } else {
 *   console.error(`Form submission failed: ${result.error}`);
 * }
 */
export async function submitForm(
  formData: FormData,
  generatedFile: File | Buffer | Blob,
  fileName: string,
  mimeType: string,
  updateFormData: (newData: Partial<FormData>) => void
): Promise<FormSubmissionResult> {
  console.log('SubmitForm: Starting form submission process');

  // Check if this PDF has already been submitted (prevent duplicate submissions)
  if (formData.pdfSubmitted) {
    console.log('SubmitForm: PDF already submitted, preventing duplicate submission');
    return {
      success: false,
      error: 'This form has already been submitted. Please refresh the page to start a new submission.'
    };
  }

  try {
    // Create form data for the API request
    const apiFormData = new FormData();

    // Debug: Log the form data being submitted
    console.log('SubmitForm: Form data being submitted:', JSON.stringify(formData, null, 2));
    console.log('SubmitForm: uploadedFileUrls:', formData.uploadedFileUrls);

    // Create a deep copy of the form data to ensure we're not losing any properties
    const formDataCopy = JSON.parse(JSON.stringify(formData));

    // Add the form data as JSON
    apiFormData.append('formData', JSON.stringify(formDataCopy));

    // Add the PDF file
    if (generatedFile instanceof File) {
      apiFormData.append('pdfFile', generatedFile);
    } else if (generatedFile instanceof Blob) {
      // Use the Blob directly
      apiFormData.append('pdfFile', generatedFile, fileName);
    } else {
      // Convert Buffer to Blob
      const blob = new Blob([generatedFile], { type: mimeType });
      apiFormData.append('pdfFile', blob, fileName);
    }

    // Mark this PDF as submitted to prevent duplicate submissions
    updateFormData({ pdfSubmitted: true });

    // Add the folder information
    if (formData.userFolderId) {
      apiFormData.append('userFolderId', formData.userFolderId);
    }
    // If we have a reference number, use it directly as the folder name
    else if (formData.referenceNumber) {
      // Use the reference number directly as the folder name for consistency
      const folderName = formData.referenceNumber;
      apiFormData.append('folderName', folderName);
    }
    // Check if reference number exists in local storage
    else if (typeof window !== 'undefined') {
      try {
        const savedData = localStorage.getItem('formData');
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData.referenceNumber) {
            // Use the reference number from local storage
            const folderName = parsedData.referenceNumber;
            console.log('SubmitForm: Creating folder with reference number from local storage:', folderName);
            apiFormData.append('folderName', folderName);

            // Update form data with reference number from local storage
            updateFormData({ referenceNumber: parsedData.referenceNumber });
          }
        }
      } catch (error) {
        console.error('SubmitForm: Error reading reference number from local storage:', error);
      }
    }
    // Otherwise, create a new folder if we have a name
    else if (formData.fullName) {
      const folderName = generateUniqueFolderName(formData.fullName);
      apiFormData.append('folderName', folderName);
    }

    // We don't need to re-upload files that were already uploaded when selected
    // Just include file references in the form data JSON
    console.log('SubmitForm: Not re-uploading already uploaded files');

    // Send the request to the API
    console.log('SubmitForm: Sending form data to API');
    const response = await fetch('/api/formulaire/submit', {
      method: 'POST',
      body: apiFormData,
    });

    if (!response.ok) {
      throw new Error(`Form submission failed: ${response.statusText}`);
    }

    // Parse the response
    const result = await response.json();
    console.log('SubmitForm: API response:', result);

    // Update form data with folder information if available
    if (result.folder && !formData.userFolderId) {
      updateFormData({
        userFolderId: result.folder.id,
        userFolderName: result.folder.name,
        userFolderLink: result.folder.webViewLink
      });
    }

    console.log('SubmitForm: Form submission completed successfully');

    return {
      success: true,
      submissionId: result.referenceNumber || `submission-${Date.now()}`,
      fileId: result.fileId,
      fileName: result.fileName,
      webViewLink: result.webViewLink,
      folderInfo: result.folder
    };
  } catch (error) {
    console.error('SubmitForm: Error submitting form:', error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
