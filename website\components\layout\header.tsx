"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { LanguageSwitcher } from "@/app/components/language-switcher";
import {
  Menu,
  X,
  ChevronDown,
  Building2,
  Home,
  Hammer,
  Phone,
  Mail,
  Search,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  FileText,
  Users,
  Calculator,
  ClipboardCheck,
  MessageCircle
} from "lucide-react";

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const pathname = usePathname();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navigation = [
    {
      name: "Accueil",
      href: "/",
      current: pathname === "/"
    },
    {
      name: "Services",
      href: "#",
      current: false,
      dropdown: [
        {
          name: "Foncier",
          href: "/foncier",
          description: "Sécurisation et conseil foncier",
          icon: Building2,
          featured: true
        },
        {
          name: "Immobilier",
          href: "/immobilier",
          description: "Conseil et investissement immobilier",
          icon: Home
        },
        {
          name: "Construction",
          href: "/construction",
          description: "Expertise technique et suivi",
          icon: Hammer
        }
      ]
    },
    // {
    //   name: "Formulaires",
    //   href: "#",
    //   current: false,
    //   dropdown: [
    //     {
    //       name: "Formulaire Foncier",
    //       href: "/formulaire",
    //       description: "Demande d'accompagnement foncier",
    //       icon: FileText,
    //       featured: true
    //     },
    //     {
    //       name: "Évaluation Immobilière",
    //       href: "/formulaire/evaluation",
    //       description: "Demande d'évaluation de bien",
    //       icon: Calculator
    //     },
    //     {
    //       name: "Consultation Générale",
    //       href: "/formulaire/consultation",
    //       description: "Demande de consultation",
    //       icon: ClipboardCheck
    //     }
    //   ]
    // },
    {
      name: "À propos",
      href: "/a-propos",
      current: pathname === "/a-propos"
    },
    {
      name: "Projets",
      href: "/projets",
      current: pathname === "/projets"
    },
    {
      name: "Blog",
      href: "/blog",
      current: pathname === "/blog"
    }
  ];

  const socialLinks = [
    { name: "Facebook", href: "https://www.facebook.com/charlieoscarconsulting", icon: Facebook },
    { name: "LinkedIn", href: "https://www.linkedin.com/company/charlie-oscar-consulting", icon: Linkedin },

  ];

  return (
    pathname.startsWith("/m") ? null : // Hide header for /m routes
    <header className="fixed top-0 left-0 right-0 z-50">
      {/* Top Level - Contact & Social */}
      <div
        className={`bg-gray-900 text-white transition-all duration-500 ease-in-out ${
          isScrolled ? 'h-0 overflow-hidden opacity-0' : 'h-12 opacity-100'
        }`}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-12">
            {/* Contact Info */}
            <div className="flex items-center space-x-6 text-sm">
              <a href="tel:+237682658037" className="flex items-center space-x-2 hover:text-primary transition-colors">
                <Phone className="w-4 h-4" />
                <span>+237682658037</span>
              </a>
              <a href="mailto:<EMAIL>" className="flex items-center space-x-2 hover:text-primary transition-colors">
                <Mail className="w-4 h-4" />
                <span className="hidden md:inline"><EMAIL></span>
              </a>
            </div>

            {/* Search & Social */}
            <div className="flex items-center space-x-4">
              {/* Search Bar */}
              {/* <div className="relative hidden md:block">
                <Input
                  type="text"
                  placeholder="Rechercher..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-64 h-8 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-primary text-sm"
                />
                <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div> */}

              {/* Social Links */}
              <div className="flex items-center space-x-2">
                {socialLinks.map((social) => {
                  const Icon = social.icon;
                  return (
                    <a
                      key={social.name}
                      href={social.href}
                      className="w-8 h-8 flex items-center justify-center hover:bg-primary rounded transition-colors"
                      aria-label={social.name}
                      title={`Suivez-nous sur ${social.name}`}
                      target="_blank"
                    >
                      <Icon className="w-4 h-4" />
                    </a>
                  );
                })}
                <a
                      href="https://wa.me/+237682658037"
                      className="w-8 h-8 flex items-center justify-center hover:bg-primary rounded transition-colors"
                      aria-label="WhatsApp"
                      target="_blank"
                      title="Contactez-nous sur WhatsApp"
                  >
                  <MessageCircle className="w-4 h-4" />
                </a>
                {/* <LanguageSwitcher className="w-8 h-8 flex items-center justify-center hover:bg-primary rounded transition-colors" /> */}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Level - Logo & Navigation */}
      <div
        className={`bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200 transition-all duration-500 ease-in-out ${
          isScrolled ? 'translate-y-0' : 'translate-y-0'
        }`}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3 group">
              <img
                src="/logo.svg"
                alt="Charlie Oscar Consulting Logo"
                className="w-40 h-40 sm:w-52 sm:h-52  rounded-lg transition-transform duration-300 group-hover:scale-110 object-contain"
              />
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigation.map((item) => (
                <div key={item.name} className="relative">
                  {item.dropdown ? (
                    <div
                      className="relative"
                      onMouseEnter={() => setActiveDropdown(item.name)}
                      onMouseLeave={() => setActiveDropdown(null)}
                    >
                      <button
                        className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                          item.current
                            ? 'text-primary bg-primary/10'
                            : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                        }`}
                      >
                        {item.name}
                        <ChevronDown className={`ml-1 h-4 w-4 transition-transform duration-200 ${
                          activeDropdown === item.name ? 'rotate-180' : ''
                        }`} />
                      </button>

                      {/* Dropdown Menu */}
                      {activeDropdown === item.name && (
                        <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-xl border border-gray-200 py-4 z-50">
                          <div className="px-4 pb-3 border-b border-gray-100">
                            <h3 className="text-sm font-semibold text-gray-900">
                              {item.name === "Services" ? "Nos domaines d'expertise" : "Nos formulaires"}
                            </h3>
                          </div>
                          <div className="py-2">
                            {item.dropdown.map((dropdownItem) => {
                              const Icon = dropdownItem.icon;
                              return (
                                <Link
                                  key={dropdownItem.name}
                                  href={dropdownItem.href}
                                  className={`flex items-start space-x-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 ${
                                    dropdownItem.featured ? 'bg-primary/5' : ''
                                  }`}
                                >
                                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                                    dropdownItem.featured
                                      ? 'bg-primary/10 text-primary'
                                      : 'bg-gray-100 text-gray-600'
                                  }`}>
                                    <Icon className="w-5 h-5" />
                                  </div>
                                  <div className="flex-1">
                                    <div className={`font-medium ${
                                      dropdownItem.featured ? 'text-primary' : 'text-gray-900'
                                    }`}>
                                      {dropdownItem.name}
                                      {dropdownItem.featured && (
                                        <span className="ml-2 text-xs bg-primary text-white px-2 py-0.5 rounded-full">
                                          Principal
                                        </span>
                                      )}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                      {dropdownItem.description}
                                    </div>
                                  </div>
                                </Link>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                        item.current
                          ? 'text-primary bg-primary/10'
                          : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                      }`}
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
            </nav>

            {/* CTA Button */}
            <div className="hidden lg:flex items-center space-x-4">
              <Button asChild className="group">
                <Link href="/formulaire" className="flex items-center">
                  Demander un accompagnement
                  <ChevronDown className="ml-2 h-4 w-4 rotate-[-90deg] transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="lg:hidden">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>

              <SheetContent side="right" className="w-full sm:w-80">
                <div className="flex flex-col h-full">
                  {/* Mobile Header */}
                  <div className="flex items-center justify-between pb-6 border-b border-gray-200">
                    <Link href="/" className="flex items-center space-x-3" onClick={() => setIsMobileMenuOpen(false)}>
                      <div className="w-32 h-24  bg-white rounded-lg flex items-center justify-center">
                        <img src="/logo.svg" alt="Charlie Oscar Consulting Logo" className="w-full h-full" />
                      </div>
                    </Link>
                  </div>

                  {/* Mobile Navigation */}
                  <nav className="flex-1 py-6 space-y-2">
                    {navigation.map((item) => (
                      <div key={item.name}>
                        {item.dropdown ? (
                          <div className="space-y-2">
                            <div className="text-sm font-semibold text-gray-900 px-3 py-2">
                              {item.name}
                            </div>
                            <div className="space-y-1 pl-4">
                              {item.dropdown.map((dropdownItem) => {
                                const Icon = dropdownItem.icon;
                                return (
                                  <Link
                                    key={dropdownItem.name}
                                    href={dropdownItem.href}
                                    className="flex items-center space-x-3 px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-colors"
                                    onClick={() => setIsMobileMenuOpen(false)}
                                  >
                                    <Icon className="w-5 h-5" />
                                    <span>{dropdownItem.name}</span>
                                    {dropdownItem.featured && (
                                      <span className="text-xs bg-primary text-white px-2 py-0.5 rounded-full">
                                        Principal
                                      </span>
                                    )}
                                  </Link>
                                );
                              })}
                            </div>
                          </div>
                        ) : (
                          <Link
                            href={item.href}
                            className={`block px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                              item.current
                                ? 'text-primary bg-primary/10'
                                : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                            }`}
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            {item.name}
                          </Link>
                        )}
                      </div>
                    ))}
                  </nav>

                  {/* Mobile Contact & Search */}
                  <div className="border-t border-gray-200 pt-6 space-y-4">
                    {/* Mobile Search */}
                    {/* <div className="relative">
                      <Input
                        type="text"
                        placeholder="Rechercher..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full"
                      />
                      <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    </div> */}

                    {/* Contact Info */}
                    <div className="space-y-3">
                      <a href="tel:+237682658037" className="flex items-center space-x-3 text-gray-700 hover:text-primary transition-colors">
                        <Phone className="w-5 h-5" />
                        <span>+237682658037</span>
                      </a>
                      <a href="mailto:<EMAIL>" className="flex items-center space-x-3 text-gray-700 hover:text-primary transition-colors">
                        <Mail className="w-5 h-5" />
                        <span className=""><EMAIL></span>
                      </a>
                    </div>

                    {/* Social Links */}
                    <div className="flex items-center space-x-3">
                      {socialLinks.map((social) => {
                        const Icon = social.icon;
                        return (
                          <a
                            key={social.name}
                            href={social.href}
                            className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-primary hover:text-white transition-colors"
                            target="_blank"
                            aria-label={social.name}
                          >
                            <Icon className="w-5 h-5" />
                          </a>
                        );
                      })}
                      <a
                        href="https://wa.me/+237682658037"
                        className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-primary hover:text-white transition-colors"
                        aria-label="WhatsApp"
                        target="_blank"
                        title="Contactez-nous sur WhatsApp"
                      >
                        <MessageCircle className="w-4 h-4" />
                      </a>
                    </div>

                    <Button asChild className="w-full">
                      <Link href="/formulaire" onClick={() => setIsMobileMenuOpen(false)}>
                        Demander un accompagnement
                      </Link>
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}
