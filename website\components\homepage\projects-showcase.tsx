"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, ChevronLeft, ChevronRight, MapPin, Calendar, Users } from "lucide-react";

export function ProjectsShowcase() {
  const [isVisible, setIsVisible] = useState(false);
  const [currentProject, setCurrentProject] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const projects = [
    {
      id: 1,
      title: "Sécurisation foncière - Quartier Bastos",
      category: "Foncier",
      location: "Yaoundé, Cameroun",
      duration: "3 mois",
      client: "Promoteur immobilier",
      description: "Accompagnement complet dans la sécurisation de 15 titres fonciers pour un projet de développement résidentiel haut de gamme.",
      results: [
        "15 titres sécurisés avec succès",
        "Réduction des délais de 40%",
        "Économie de 25% sur les coûts",
        "Zéro litige post-acquisition"
      ],
      tags: ["Titres fonciers", "Résidentiel", "Yaoundé"],
      testimonial: {
        text: "Charlie Oscar Consulting a su naviguer avec expertise dans les complexités administratives. Leur accompagnement a été déterminant pour le succès de notre projet.",
        author: "Jean-Paul M.",
        role: "Directeur de projet"
      }
    },
    {
      id: 2,
      title: "Complexe commercial - Douala",
      category: "Immobilier",
      location: "Douala, Cameroun",
      duration: "6 mois",
      client: "Investisseur international",
      description: "Conseil stratégique et accompagnement juridique pour l'acquisition et le développement d'un complexe commercial de 5000m².",
      results: [
        "Acquisition réussie en 4 mois",
        "Optimisation fiscale de 30%",
        "Permis obtenus dans les délais",
        "ROI projeté de 18%"
      ],
      tags: ["Commercial", "Investissement", "Douala"],
      testimonial: {
        text: "Leur connaissance du marché local et leur réseau professionnel ont été des atouts majeurs pour notre investissement.",
        author: "Sarah K.",
        role: "Directrice des investissements"
      }
    },
    {
      id: 3,
      title: "Projet résidentiel - Kribi",
      category: "Construction",
      location: "Kribi, Cameroun",
      duration: "8 mois",
      client: "Développeur local",
      description: "Supervision technique et juridique d'un projet de 50 villas avec infrastructure complète dans la région côtière.",
      results: [
        "50 villas livrées conformément",
        "Respect du budget initial",
        "Certification qualité obtenue",
        "Satisfaction client 98%"
      ],
      tags: ["Résidentiel", "Villas", "Kribi"],
      testimonial: {
        text: "Un accompagnement professionnel de A à Z. Leur expertise technique nous a permis d'éviter de nombreux écueils.",
        author: "Michel D.",
        role: "Promoteur immobilier"
      }
    }
  ];

  const nextProject = () => {
    setCurrentProject((prev) => (prev + 1) % projects.length);
  };

  const prevProject = () => {
    setCurrentProject((prev) => (prev - 1 + projects.length) % projects.length);
  };

  const currentProjectData = projects[currentProject];

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
              Nos réalisations
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Projets réalisés avec succès
            </h2>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Découvrez quelques-uns de nos projets emblématiques qui illustrent 
              notre expertise et notre engagement envers l'excellence.
            </p>
          </div>

          {/* Project Showcase */}
          <div className={`transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <Card className="overflow-hidden shadow-xl border-0">
              <CardContent className="p-0">
                <div className="grid lg:grid-cols-2 gap-0">
                  {/* Project Image/Visual */}
                  <div className="relative bg-gradient-to-br from-primary/10 to-accent/10 p-8 lg:p-12 flex items-center justify-center min-h-[400px]">
                    <div className="text-center space-y-6">
                      <div className="w-24 h-24 bg-white/20 rounded-full mx-auto flex items-center justify-center backdrop-blur-sm">
                        <svg className="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                      </div>
                      <div className="space-y-2">
                        <Badge variant="secondary" className="bg-white/20 text-gray-700 backdrop-blur-sm">
                          {currentProjectData.category}
                        </Badge>
                        <p className="text-gray-600 text-sm">Image du projet à venir</p>
                      </div>
                    </div>

                    {/* Navigation */}
                    <div className="absolute top-1/2 left-4 transform -translate-y-1/2">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={prevProject}
                        className="bg-white/80 backdrop-blur-sm hover:bg-white"
                      >
                        <ChevronLeft className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="absolute top-1/2 right-4 transform -translate-y-1/2">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={nextProject}
                        className="bg-white/80 backdrop-blur-sm hover:bg-white"
                      >
                        <ChevronRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="p-8 lg:p-12 space-y-8">
                    {/* Header */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="border-primary text-primary">
                          {currentProjectData.category}
                        </Badge>
                        {currentProjectData.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      
                      <h3 className="text-2xl font-bold text-gray-900">
                        {currentProjectData.title}
                      </h3>
                      
                      <p className="text-gray-600 leading-relaxed">
                        {currentProjectData.description}
                      </p>
                    </div>

                    {/* Project Info */}
                    <div className="grid grid-cols-3 gap-4 py-4 border-y border-gray-200">
                      <div className="text-center">
                        <div className="flex items-center justify-center mb-2">
                          <MapPin className="w-4 h-4 text-gray-400" />
                        </div>
                        <div className="text-sm font-medium text-gray-900">{currentProjectData.location}</div>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center mb-2">
                          <Calendar className="w-4 h-4 text-gray-400" />
                        </div>
                        <div className="text-sm font-medium text-gray-900">{currentProjectData.duration}</div>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center mb-2">
                          <Users className="w-4 h-4 text-gray-400" />
                        </div>
                        <div className="text-sm font-medium text-gray-900">{currentProjectData.client}</div>
                      </div>
                    </div>

                    {/* Results */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-gray-900">Résultats obtenus :</h4>
                      <ul className="space-y-2">
                        {currentProjectData.results.map((result, index) => (
                          <li key={index} className="flex items-start space-x-3 text-sm text-gray-600">
                            <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span>{result}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Testimonial */}
                    <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                      <p className="text-gray-700 italic">"{currentProjectData.testimonial.text}"</p>
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-primary font-semibold text-sm">
                            {currentProjectData.testimonial.author.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{currentProjectData.testimonial.author}</div>
                          <div className="text-sm text-gray-600">{currentProjectData.testimonial.role}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Project Indicators */}
            <div className="flex justify-center space-x-2 mt-8">
              {projects.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentProject(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentProject ? 'bg-primary' : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* CTA */}
          <div className={`text-center mt-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <Button asChild size="lg" variant="outline" className="group">
              <Link href="/projets" className="flex items-center">
                Voir tous nos projets
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
