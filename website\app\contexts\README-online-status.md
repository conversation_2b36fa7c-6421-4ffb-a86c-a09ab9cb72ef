# Online Status Context

This context provides a way to track the user's online status throughout the application.

## Features

- Tracks whether the user is online or offline
- Provides a hook to access the online status
- Shows a toast notification when the connection status changes
- Includes a visual indicator component

## Usage

### 1. Wrap your application with the OnlineStatusProvider

The provider is already included in the root layout, so you don't need to add it manually.

### 2. Use the useOnlineStatus hook to access the online status

```tsx
import { useOnlineStatus } from "@/app/contexts/online-status-context";

function MyComponent() {
  const { isOnline } = useOnlineStatus();
  
  return (
    <div>
      {isOnline ? "You are online" : "You are offline"}
    </div>
  );
}
```

### 3. Use the OnlineStatusIndicator component to show the online status

```tsx
import { OnlineStatusIndicator } from "@/app/components/online-status-indicator";

function MyComponent() {
  return (
    <div>
      <OnlineStatusIndicator />
    </div>
  );
}
```

## Components

### OnlineStatusProvider

The provider component that tracks the online status and provides it to the rest of the application.

### OnlineStatusIndicator

A visual indicator component that shows whether the user is online or offline.

### ConnectionStatusToast

A toast notification component that shows a message when the connection status changes.

## Testing

You can test the online status functionality by:

1. Opening your browser's developer tools (F12)
2. Going to the Network tab
3. Checking "Offline" to simulate offline mode
4. Unchecking "Offline" to return to online mode

You can also visit the demo page at `/online-status-demo` to see the online status in action.
