import { Metada<PERSON> } from "next";
import Link from "next/link";
import { getAllImmobilierServices, getFeaturedImmobilierServices } from "@/lib/immobilier-services-data";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Clock, Home, ChevronRight, CheckCircle, Star } from "lucide-react";

export const metadata: Metadata = {
  title: "Services Immobiliers - Expertise complète | Charlie Oscar Consulting",
  description: "Découvrez notre gamme complète de services immobiliers au Cameroun : achat, vente, location, conseil juridique et expertise immobilière.",
  keywords: [
    "services immobiliers",
    "achat vente immobilier",
    "location immobilier",
    "conseil juridique immobilier",
    "expertise immobilière",
    "Charlie Oscar"
  ],
  openGraph: {
    title: "Services Immobiliers - Expertise complète | Charlie <PERSON> Consulting",
    description: "Découvrez notre gamme complète de services immobiliers au Cameroun.",
    type: "website",
    locale: "fr_FR",
    siteName: "Charlie Oscar Consulting"
  },
  alternates: {
    canonical: "/immobilier/services"
  }
};

export default function ImmobilierServicesPage() {
  const allServices = getAllImmobilierServices();
  const featuredServices = getFeaturedImmobilierServices();

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Simple':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Modéré':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Complexe':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-50 to-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
              <Link href="/" className="hover:text-primary transition-colors flex items-center">
                <Home className="w-4 h-4 mr-1" />
                Accueil
              </Link>
              <ChevronRight className="w-4 h-4" />
              <Link href="/immobilier" className="hover:text-primary transition-colors">
                Immobilier
              </Link>
              <ChevronRight className="w-4 h-4" />
              <span className="text-primary">Services</span>
            </nav>

            <div className="text-center space-y-6">
              <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
                Nos services immobiliers
              </div>
              
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Solutions immobilières{" "}
                <span className="text-primary relative">
                  complètes
                  <div className="absolute -bottom-2 left-0 w-full h-1 bg-primary/40 rounded-full"></div>
                </span>
              </h1>
              
              <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                De l'achat à la vente, de la location au conseil juridique, découvrez notre gamme 
                complète de services pour tous vos projets immobiliers au Cameroun.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="group">
                  <Link href="/formulaire" className="flex items-center">
                    Demander un devis
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                
                <Button asChild size="lg" variant="outline">
                  <Link href="/contact">
                    Consultation gratuite
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Services */}
      {featuredServices.length > 0 && (
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center space-y-4 mb-16">
                <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
                  <Star className="w-4 h-4 mr-2 fill-current" />
                  Services phares
                </div>
                
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  Nos services les plus demandés
                </h2>
                
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Ces services représentent notre expertise principale et bénéficient 
                  de notre expérience la plus approfondie.
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {featuredServices.map((service) => (
                  <Card key={service.id} className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/30">
                    <CardHeader className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 flex items-center">
                          <Star className="w-3 h-3 mr-1 fill-current" />
                          Service phare
                        </Badge>
                        <Badge className={getComplexityColor(service.complexity)}>
                          {service.complexity}
                        </Badge>
                      </div>
                      
                      <CardTitle className="text-xl group-hover:text-primary transition-colors">
                        {service.title}
                      </CardTitle>
                      
                      <CardDescription className="text-gray-600 leading-relaxed">
                        {service.shortDescription}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-6">
                      {/* Service Metrics */}
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">{service.estimatedDuration}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-600">{service.category}</span>
                        </div>
                      </div>

                      {/* Key Results */}
                      <div className="space-y-2">
                        <h4 className="font-semibold text-gray-900 text-sm">Résultats clés :</h4>
                        <ul className="space-y-1">
                          {service.results.slice(0, 3).map((result, index) => (
                            <li key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                              <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0 mt-0.5" />
                              <span>{result}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* CTA */}
                      <Button asChild className="w-full group/btn">
                        <Link href={`/immobilier/services/${service.slug}`} className="flex items-center justify-center">
                          En savoir plus
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* All Services */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                Tous nos services immobiliers
              </h2>
              
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Une expertise complète pour répondre à tous vos besoins immobiliers.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {allServices.map((service) => (
                <Card key={service.id} className="group hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <CardHeader className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="text-xs">
                        {service.category}
                      </Badge>
                      <Badge className={getComplexityColor(service.complexity)}>
                        {service.complexity}
                      </Badge>
                    </div>
                    
                    <CardTitle className="text-lg group-hover:text-primary transition-colors">
                      {service.title}
                    </CardTitle>
                    
                    <CardDescription className="text-gray-600 text-sm leading-relaxed">
                      {service.shortDescription}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Service Info */}
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{service.estimatedDuration}</span>
                      </div>
                      {service.featured && (
                        <div className="flex items-center space-x-1 text-yellow-600">
                          <Star className="w-3 h-3 fill-current" />
                          <span className="text-xs">Phare</span>
                        </div>
                      )}
                    </div>

                    {/* CTA */}
                    <Button asChild variant="outline" className="w-full group/btn">
                      <Link href={`/immobilier/services/${service.slug}`} className="flex items-center justify-center">
                        Découvrir
                        <ArrowRight className="ml-2 h-3 w-3 transition-transform group-hover/btn:translate-x-1" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Besoin d'un service personnalisé ?
            </h2>
            
            <p className="text-xl text-primary-foreground/90">
              Chaque projet immobilier est unique. Nos experts analysent votre situation 
              et vous proposent la solution la plus adaptée.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" variant="secondary" className="group">
                <Link href="/formulaire" className="flex items-center">
                  Demander un devis personnalisé
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
                <Link href="/contact">
                  Parler à un expert
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
