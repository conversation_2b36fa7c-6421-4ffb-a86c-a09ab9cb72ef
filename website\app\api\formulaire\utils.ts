import { writeFile, mkdir } from 'fs/promises';
import path from 'path';
import { existsSync } from 'fs';

/**
 * Save a file to the server
 * @param file The file to save
 * @param directory The directory to save the file in
 * @param fileName Optional custom file name (defaults to the original file name)
 * @returns The path to the saved file
 */
export async function saveFile(
  file: File,
  directory: string,
  fileName?: string
): Promise<string> {
  try {
    // Create the directory if it doesn't exist
    if (!existsSync(directory)) {
      await mkdir(directory, { recursive: true });
    }
    
    // Use the provided file name or the original file name
    const finalFileName = fileName || file.name;
    
    // Create the full file path
    const filePath = path.join(directory, finalFileName);
    
    // Convert the file to a buffer and save it
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    await writeFile(filePath, fileBuffer);
    
    return filePath;
  } catch (error) {
    console.error(`Error saving file ${file.name}:`, error);
    throw error;
  }
}

/**
 * Generate a unique reference number for a submission
 * @returns A reference number in the format REF-XXXXXXXX
 */
export function generateReferenceNumber(): string {
  const timestamp = Date.now().toString();
  const randomPart = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `REF-${timestamp.slice(-5)}${randomPart}`;
}

/**
 * Validate the form data
 * @param formData The form data to validate
 * @returns An object with a valid flag and any error messages
 */
export function validateFormData(formData: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check for required fields
  if (!formData.fullName) errors.push('Full name is required');
  if (!formData.email) errors.push('Email is required');
  if (!formData.primaryPhone) errors.push('Primary phone is required');
  
  // Add more validation as needed
  
  return {
    valid: errors.length === 0,
    errors,
  };
}
