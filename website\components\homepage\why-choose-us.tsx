"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Award, MapPin, Users, Shield, Clock } from "lucide-react";

export function WhyChooseUs({ content }: { content: any }) {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const iconMap: Record<string, any> = { Award, MapPin, Users, Shield, Clock };
  const advantages = (content?.advantages || []).map((adv: any) => ({
    ...adv,
    icon: iconMap[adv.icon] || Award
  }));

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
              {content?.sectionTitle}
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {content?.sectionSubtitle}
            </h2>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {content?.description}
            </p>
          </div>

          {/* Advantages Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {advantages.slice(0, 3).map((advantage, index) => {
              const Icon = advantage.icon;
              
              return (
                <div
                  key={index}
                  className={`transition-all duration-700 ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                  }`}
                  style={{ transitionDelay: `${index * 200}ms` }}
                >
                  <Card className="h-full hover:shadow-lg transition-all duration-300 group border-2 border-gray-100 hover:border-primary/20">
                    <CardHeader className="text-center space-y-4">
                      <div className={`w-16 h-16 ${advantage.bgColor} rounded-2xl flex items-center justify-center mx-auto transition-transform duration-300 group-hover:scale-110`}>
                        <Icon className={`w-8 h-8 ${advantage.color}`} />
                      </div>
                      
                      <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                        {advantage.title}
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="text-center">
                      <p className="text-gray-600 leading-relaxed">
                        {advantage.description}
                      </p>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Additional Advantages */}
          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {advantages.slice(3).map((advantage, index) => {
              const Icon = advantage.icon;
              
              return (
                <div
                  key={index + 3}
                  className={`transition-all duration-700 ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                  }`}
                  style={{ transitionDelay: `${(index + 3) * 200}ms` }}
                >
                  <Card className="h-full hover:shadow-lg transition-all duration-300 group border-2 border-gray-100 hover:border-primary/20">
                    <CardContent className="p-8">
                      <div className="flex items-start space-x-6">
                        <div className={`w-14 h-14 ${advantage.bgColor} rounded-xl flex items-center justify-center flex-shrink-0 transition-transform duration-300 group-hover:scale-110`}>
                          <Icon className={`w-7 h-7 ${advantage.color}`} />
                        </div>
                        
                        <div className="space-y-3">
                          <h3 className="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                            {advantage.title}
                          </h3>
                          <p className="text-gray-600 leading-relaxed">
                            {advantage.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Stats Section */}
          {/* <div className={`bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 mb-16 transition-all duration-1000 delay-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="grid md:grid-cols-4 gap-8 text-center">
              <div className="space-y-2">
                <div className="text-3xl font-bold text-primary">10+</div>
                <div className="text-sm text-gray-600">Années d'expérience</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-primary">500+</div>
                <div className="text-sm text-gray-600">Projets réalisés</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-primary">95%</div>
                <div className="text-sm text-gray-600">Clients satisfaits</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-primary">24/7</div>
                <div className="text-sm text-gray-600">Support disponible</div>
              </div>
            </div>
          </div> */}

        </div>
      </div>
    </section>
  );
}
